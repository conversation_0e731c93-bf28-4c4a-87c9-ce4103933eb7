This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023/Arch Linux) (preloaded format=pdflatex 2024.2.23)  11 APR 2024 20:50
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/home/<USER>/WorkSpace/Physics 540A/HW4.tex"
(/home/<USER>/WorkSpace/Physics 540A/HW4.tex
LaTeX2e <2022-11-01> patch level 1
L3 programming layer <2023-02-22> (/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2022/07/02 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2022/07/02 v1.4n Standard LaTeX file (size option)
)
\c@part=\count185
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@subparagraph=\count190
\c@figure=\count191
\c@table=\count192
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (/usr/share/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks16
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (/usr/share/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2022/11/09 v4.1 Extensive control of page headers and footers
\f@nch@headwidth=\skip50
\f@nch@O@elh=\skip51
\f@nch@O@erh=\skip52
\f@nch@O@olh=\skip53
\f@nch@O@orh=\skip54
\f@nch@O@elf=\skip55
\f@nch@O@erf=\skip56
\f@nch@O@olf=\skip57
\f@nch@O@orf=\skip58
) (/usr/share/texmf-dist/tex/latex/listings/listings.sty (/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
\lst@mode=\count193
\lst@gtempboxa=\box51
\lst@token=\toks18
\lst@length=\count194
\lst@currlwidth=\dimen141
\lst@column=\count195
\lst@pos=\count196
\lst@lostspace=\dimen142
\lst@width=\dimen143
\lst@newlines=\count197
\lst@lineno=\count198
\lst@maxwidth=\dimen144
 (/usr/share/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2023/02/27 1.9 (Carsten Heinz)
\c@lstnumber=\count199
\lst@skipnumbers=\count266
\lst@framebox=\box52
) (/usr/share/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2023/02/27 1.9 listings configuration
))
Package: listings 2023/02/27 1.9 (Carsten Heinz)
 (/usr/share/texmf-dist/tex/latex/comment/comment.sty
\CommentStream=\write3
 Excluding comment 'comment') (/usr/share/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/share/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count267
\Gm@cntv=\count268
\c@Gm@tempcnt=\count269
\Gm@bindingoffset=\dimen145
\Gm@wd@mp=\dimen146
\Gm@odd@mp=\dimen147
\Gm@even@mp=\dimen148
\Gm@layoutwidth=\dimen149
\Gm@layoutheight=\dimen150
\Gm@layouthoffset=\dimen151
\Gm@layoutvoffset=\dimen152
\Gm@dimlist=\toks19
) (/usr/share/texmf-dist/tex/latex/stackengine/stackengine.sty
Package: stackengine 2021/07/22 v4.11\ Stacking text and objects in convenient ways
 (/usr/share/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count270
) (/usr/share/texmf-dist/tex/generic/listofitems/listofitems.sty (/usr/share/texmf-dist/tex/generic/listofitems/listofitems.tex
\loi_cnt_foreach_nest=\count271
\loi_nestcnt=\count272
)
Package: listofitems 2019/08/21 v1.63 Grab items in lists using user-specified sep char (CT)
) (/usr/share/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2017/05/25 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count273
\calc@Bcount=\count274
\calc@Adimen=\dimen153
\calc@Bdimen=\dimen154
\calc@Askip=\skip59
\calc@Bskip=\skip60
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count275
\calc@Cskip=\skip61
)
\c@@stackindex=\count276
\@boxshift=\skip62
\stack@tmplength=\skip63
\temp@stkl=\skip64
\@stackedboxwidth=\skip65
\@addedbox=\box53
\@anchorbox=\box54
\@insetbox=\box55
\se@backgroundbox=\box56
\stackedbox=\box57
\@centerbox=\box58
\c@ROWcellindex@=\count277
) (/usr/share/texmf-dist/tex/latex/psnfss/times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
) (/usr/share/texmf-dist/tex/latex/cancel/cancel.sty
Package: cancel 2013/04/12 v2.2 Cancel math terms
) (/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2022/04/08 v2.17n AMS math features
\@mathmargin=\skip66

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen155
)) (/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen156
) (/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count278
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count279
\leftroot@=\count280
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count281
\DOTSCASE@=\count282
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box59
\strutbox@=\box60
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen157
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count283
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count284
\dotsspace@=\muskip16
\c@parentequation=\count285
\dspbrk@lvl=\count286
\tag@help=\toks21
\row@=\count287
\column@=\count288
\maxfields@=\count289
\andhelp@=\toks22
\eqnshift@=\dimen158
\alignsep@=\dimen159
\tagshift@=\dimen160
\tagwidth@=\dimen161
\totwidth@=\dimen162
\lineht@=\dimen163
\@envbody=\toks23
\multlinegap=\skip67
\multlinetaggap=\skip68
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/share/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks25
\pgfutil@tempdima=\dimen164
\pgfutil@tempdimb=\dimen165
) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box61
) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/share/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/share/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (/usr/share/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (/usr/share/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen166
\Gin@req@width=\dimen167
) (/usr/share/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks26
\pgfkeys@temptoks=\toks27
 (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks28
))
\pgf@x=\dimen168
\pgf@y=\dimen169
\pgf@xa=\dimen170
\pgf@ya=\dimen171
\pgf@xb=\dimen172
\pgf@yb=\dimen173
\pgf@xc=\dimen174
\pgf@yc=\dimen175
\pgf@xd=\dimen176
\pgf@yd=\dimen177
\w@pgf@writea=\write4
\r@pgf@reada=\read2
\c@pgf@counta=\count290
\c@pgf@countb=\count291
\c@pgf@countc=\count292
\c@pgf@countd=\count293
\t@pgf@toka=\toks29
\t@pgf@tokb=\toks30
\t@pgf@tokc=\toks31
\pgf@sys@id@count=\count294
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count295
\pgfsyssoftpath@bigbuffer@items=\count296
) (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2022/06/12 v2.14 LaTeX color extensions (UK)
 (/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 227.
 (/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1353.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1357.
Package xcolor Info: Model `RGB' extended on input line 1369.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1371.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1372.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1373.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1374.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1375.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1376.
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen178
\pgfmath@count=\count297
\pgfmath@box=\box62
\pgfmath@toks=\toks32
\pgfmath@stack@operand=\toks33
\pgfmath@stack@operation=\toks34
) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count298
)) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen179
\pgf@picmaxx=\dimen180
\pgf@picminy=\dimen181
\pgf@picmaxy=\dimen182
\pgf@pathminx=\dimen183
\pgf@pathmaxx=\dimen184
\pgf@pathminy=\dimen185
\pgf@pathmaxy=\dimen186
\pgf@xx=\dimen187
\pgf@xy=\dimen188
\pgf@yx=\dimen189
\pgf@yy=\dimen190
\pgf@zx=\dimen191
\pgf@zy=\dimen192
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen193
\pgf@path@lasty=\dimen194
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen195
\pgf@shorten@start@additional=\dimen196
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box63
\pgf@hbox=\box64
\pgf@layerbox@main=\box65
\pgf@picture@serial@count=\count299
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen197
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen198
\pgf@pt@y=\dimen199
\pgf@pt@temp=\dimen256
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen257
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen258
\pgf@sys@shading@range@num=\count300
\pgf@shadingcount=\count301
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box66
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box67
) (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen259
\pgf@nodesepend=\dimen260
) (/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/usr/share/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen261
\pgffor@skip=\dimen262
\pgffor@stack=\toks35
\pgffor@toks=\toks36
)) (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count302
\pgfplotmarksize=\dimen263
)
\tikz@lastx=\dimen264
\tikz@lasty=\dimen265
\tikz@lastxsaved=\dimen266
\tikz@lastysaved=\dimen267
\tikz@lastmovetox=\dimen268
\tikz@lastmovetoy=\dimen269
\tikzleveldistance=\dimen270
\tikzsiblingdistance=\dimen271
\tikz@figbox=\box68
\tikz@figbox@bg=\box69
\tikz@tempbox=\box70
\tikz@tempbox@bg=\box71
\tikztreelevel=\count303
\tikznumberofchildren=\count304
\tikznumberofcurrentchild=\count305
\tikz@fig@count=\count306
 (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count307
\pgfmatrixcurrentcolumn=\count308
\pgf@matrix@numberofcolumns=\count309
)
\tikz@expandcount=\count310
 (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/changepage/changepage.sty
Package: changepage 2009/10/20 v1.0c check page and change page layout
\c@cp@cntr=\count311
\cp@tempcnt=\count312
) (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
)
\c@theorem=\count313
\c@axiom=\count314
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 59.
 (/usr/share/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
) (/usr/share/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2023-01-16 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count315
\l__pdf_internal_box=\box72
) (./HW4.aux)
\openout1 = `HW4.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 59.
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 59.
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 59.
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 59.
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 59.
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 59.
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 59.
LaTeX Font Info:    ... okay on input line 59.
\c@lstlisting=\count316

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(62.59596pt, 472.31595pt, 62.59596pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=472.31595pt
* \textheight=702.78308pt
* \oddsidemargin=-9.67403pt
* \evensidemargin=-9.67403pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/share/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count317
\scratchdimen=\dimen272
\scratchbox=\box73
\nofMPsegments=\count318
\nofMParguments=\count319
\everyMPshowfont=\toks37
\MPscratchCnt=\count320
\MPscratchDim=\dimen273
\MPnumerator=\count321
\makeMPintoPDFobject=\count322
\everyMPtoPDFconversion=\toks38
) (/usr/share/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/share/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
LaTeX Font Info:    Trying to load font information for U+msa on input line 60.
 (/usr/share/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 60.
 (/usr/share/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
) [1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}{/usr/share/texmf-dist/fonts/enc/dvips/base/8r.enc}]
Underfull \hbox (badness 10000) in paragraph at lines 97--99

 []

[2] [3] [4]
Overfull \hbox (53.96387pt too wide) detected at line 186
[] \OT1/cmr/m/n/12 + [] + [] \OMS/cmsy/m/n/12 ^^@ []
 []

<plotxy.png, id=28, 320.1561pt x 302.0886pt>
File: plotxy.png Graphic file (type png)
<use plotxy.png>
Package pdftex.def Info: plotxy.png  used on input line 195.
(pdftex.def)             Requested size: 160.07765pt x 151.04391pt.
<plotzx.png, id=31, 320.1561pt x 302.0886pt>
File: plotzx.png Graphic file (type png)
<use plotzx.png>
Package pdftex.def Info: plotzx.png  used on input line 196.
(pdftex.def)             Requested size: 160.07765pt x 151.04391pt.
<plotyz.png, id=33, 320.1561pt x 302.0886pt>
File: plotyz.png Graphic file (type png)
<use plotyz.png>
Package pdftex.def Info: plotyz.png  used on input line 197.
(pdftex.def)             Requested size: 160.07765pt x 151.04391pt.
<Hplot1.png, id=35, 1729.21031pt x 1445.4pt>
File: Hplot1.png Graphic file (type png)
<use Hplot1.png>
Package pdftex.def Info: Hplot1.png  used on input line 198.
(pdftex.def)             Requested size: 432.30153pt x 361.34912pt.
[5] [6 <./plotxy.png> <./plotzx.png> <./plotyz.png> <./Hplot1.png>] [7] (./HW4.aux) ) 
Here is how much of TeX's memory you used:
 16361 strings out of 477985
 313021 string characters out of 5840058
 1865388 words of memory out of 5000000
 36270 multiletter control sequences out of 15000+600000
 527474 words of font info for 72 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 102i,6n,104p,596b,262s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi12.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/symbols/msam10.pfb></usr/share/texmf-dist/fonts/type1/urw/times/utmb8a.pfb></usr/share/texmf-dist/fonts/type1/urw/times/utmr8a.pfb>
Output written on HW4.pdf (7 pages, 1379287 bytes).
PDF statistics:
 99 PDF objects out of 1000 (max. 8388607)
 57 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 57 words of extra memory for PDF output out of 10000 (max. 10000000)

