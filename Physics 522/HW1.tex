\documentclass[12pt]{article}%
\usepackage{amsfonts}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{comment}
\usepackage{fontspec}
\usepackage[a4paper, top=2.5cm, bottom=2.5cm, left=2.2cm, right=2.2cm]{geometry}
\def\shrinkage{2.1mu}
\def\vecsign{\mathchar"017E}
\def\dvecsign{\smash{\stackon[-1.95pt]{\mkern-\shrinkage\vecsign}{\rotatebox{180}{$\mkern-\shrinkage\vecsign$}}}}
\def\dvec#1{\def\useanchorwidth{T}\stackon[-4.2pt]{#1}{\,\dvecsign}}
\usepackage{stackengine}
\stackMath
\usepackage{times}
\usepackage{cancel}
\usepackage{amsmath}
\usepackage{tikz}
\usepackage{changepage}
\usetikzlibrary{shapes.misc}
\usepackage{amssymb}
\usepackage{graphicx}%
\setcounter{MaxMatrixCols}{30}
\newtheorem{theorem}{Theorem}
\newtheorem{acknowledgement}[theorem]{Acknowledgement}
\newtheorem{algorithm}[theorem]{Algorithm}
\newtheorem{axiom}{Axiom}
\newtheorem{case}[theorem]{Case}
\newtheorem{claim}[theorem]{Claim}
\newtheorem{conclusion}[theorem]{Conclusion}
\newtheorem{condition}[theorem]{Condition}
\newtheorem{conjecture}[theorem]{Conjecture}
\newtheorem{corollary}[theorem]{Corollary}
\newtheorem{criterion}[theorem]{Criterion}
\newtheorem{definition}[theorem]{Definition}
\newtheorem{example}[theorem]{Example}
\newtheorem{exercise}[theorem]{Exercise}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{notation}[theorem]{Notation}
\newtheorem{problem}[theorem]{Problem}
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{remark}[theorem]{Remark}
\newtheorem{summary}[theorem]{Summary}
\newenvironment{proof}[1][Proof]{\textbf{#1.} }{\ \rule{0.5em}{0.5em}}
\newenvironment{solution}[1][Solution]{\textbf{#1}}{\ \rule{0.5em}{0.5em}}
\newcommand{\Q}{\mathbb{Q}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\Z}{\mathbb{Z}}
\newcommand{\N}{\mathbb{N}}
\newcommand{\Lagr}{\mathcal{L}}
\newcommand{\lagr}{\mathcal{L}_0}
\newcommand{\lb}{\left\{}
\newcommand{\rb}{\right\}}
\newcommand{\set}[1]{\left\{#1\right\}}
\newcommand{\pr}[1]{\left(\{#1\right\})}
\newcommand{\dbar}[1]{\bar{\bar{#1}}}
\newcommand{\tbar}[1]{\bar{\bar{\bar{#1}}}}
\setmainfont{Times New Roman}[BoldFont={* Bold}]
\title{{Homework 1 Physics 522}}
\author{\textbf{Tyler Hadsell}}
\maketitle
\begin{document}
\section*{Problem 1}
Throughout this course will we bump into the binomial coefficient $\binom{N}{n} \equiv \frac{N!}{(N-n)!n!}$ and natural logs of factorials ($\ln x!$). Often these variables $(N, n$, or $x)$ are very large compared to one. Use Stirling's approximation to show that
\[
\ln \binom{N}{n} \approx-N(p \ln (p)+(1-p) \ln (1-p))
\]
for $N \gg 1$ and $n \gg 1$ with $p=n / N$ finite.

\section*{Solution 1}
We can begin by writing out the natural log of the binomial coefficient:
\[
\ln(\binom{N}{n}) = \ln\left(\frac{N!}{(N-n)!n!}\right) = \ln(N!) - \ln((N-n)!) - \ln(n!)
\]

[Rest of solution continues similarly...]

\section*{Problem 2}
Consider flipping coins like in lecture. You flip the coin $N$ times. Assign the $i$th flip a value $l_{i}$ with $l_{i}=1$ if it lands heads and $l_{i}=-1$ if it lands tails. Recall that there are $2^{N}$ values possible for $S_{N}=\sum_{i=1}^{N} l_{i}$ which, in general, range from $\{N^{(1)},(N-2)^{(\alpha)}, \ldots,-(N-2)^{(\alpha)},-N^{(1)}\}$ (for $N \geq 2$) where the superscript indicates how many different configurations of the $\{l_{i}\}$ yield the same value of $S_{N}$. We also showed in class that $\langle S_{N}\rangle=0$ and $\langle S_{N}^{2}\rangle=N$ and we defined the standard deviation from the mean to be $\sigma_{N} \equiv \sqrt{\langle S_{N}^{2}\rangle}=\sqrt{N}$.

\begin{enumerate}
\item For $N=4,5,6,10$, and 20 calculate the fraction of the total values for $S_{N}$ that are within $\pm \sigma_{N}$ of the mean $\mu \equiv\langle S_{N}\rangle=0$.
\item Can you say anything about this fraction as $N \rightarrow \infty$?
\end{enumerate}
\end{document}