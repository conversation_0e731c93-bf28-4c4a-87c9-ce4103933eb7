This is XeTeX, Version 3.141592653-2.6-0.999993 (MiKTeX 22.1) (preloaded format=xelatex 2024.11.9)  2 MAR 2025 11:28
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/Users/<USER>/WorkSpace/Physics 522/HW3.tex"
(/Users/<USER>/WorkSpace/Physics 522/HW3.tex
LaTeX2e <2023-06-01> patch level 1
L3 programming layer <2023-08-29>
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/base/size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count181
\c@section=\count182
\c@subsection=\count183
\c@subsubsection=\count184
\c@paragraph=\count185
\c@subparagraph=\count186
\c@figure=\count187
\c@table=\count188
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsfonts/amsfonts
.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks17
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/fancyhdr/fancyhdr.sty
Package: fancyhdr 2022/11/09 v4.1 Extensive control of page headers and footers

\f@nch@headwidth=\skip50
\f@nch@O@elh=\skip51
\f@nch@O@erh=\skip52
\f@nch@O@olh=\skip53
\f@nch@O@orh=\skip54
\f@nch@O@elf=\skip55
\f@nch@O@erf=\skip56
\f@nch@O@olf=\skip57
\f@nch@O@orf=\skip58
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/listings/listings
.sty
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics/keyval.s
ty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
)
\lst@mode=\count189
\lst@gtempboxa=\box51
\lst@token=\toks19
\lst@length=\count190
\lst@currlwidth=\dimen141
\lst@column=\count191
\lst@pos=\count192
\lst@lostspace=\dimen142
\lst@width=\dimen143
\lst@newlines=\count193
\lst@lineno=\count194
\lst@maxwidth=\dimen144

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/listings/lstmisc.
sty
File: lstmisc.sty 2023/02/27 1.9 (Carsten Heinz)
\c@lstnumber=\count195
\lst@skipnumbers=\count196
\lst@framebox=\box52
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/listings/listings
.cfg
File: listings.cfg 2023/02/27 1.9 listings configuration
))
Package: listings 2023/02/27 1.9 (Carsten Heinz)

(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/comment/comment.sty
\CommentStream=\write3
 Excluding comment 'comment')
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/iftex/ifvtex.st
y
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count197
\Gm@cntv=\count198
\c@Gm@tempcnt=\count199
\Gm@bindingoffset=\dimen145
\Gm@wd@mp=\dimen146
\Gm@odd@mp=\dimen147
\Gm@even@mp=\dimen148
\Gm@layoutwidth=\dimen149
\Gm@layoutheight=\dimen150
\Gm@layouthoffset=\dimen151
\Gm@layoutvoffset=\dimen152
\Gm@dimlist=\toks20

(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/geometry/geometry.cfg))
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/stackengine/stackengine.sty
Package: stackengine 2021/07/22 v4.11\ Stacking text and objects in convenient 
ways

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/etoolbox/etoolbox
.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count266
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/listofitems/listofitems.sty
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/gene
ric/listofitems/listofitems.tex
Invalid UTF-8 byte or sequence at line 50 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 50 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 52 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 52 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 52 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 96 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 96 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 104 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 105 replaced by U+FFFD.
\loi_cnt_foreach_nest=\count267
Invalid UTF-8 byte or sequence at line 139 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 146 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 192 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 209 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 209 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 222 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 222 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 223 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 223 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 224 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 226 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 226 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 238 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 243 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 243 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 260 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 284 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 310 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 310 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 324 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 337 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 337 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 339 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 350 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 350 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 350 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 354 replaced by U+FFFD.
\loi_nestcnt=\count268
Invalid UTF-8 byte or sequence at line 367 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 367 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 367 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 367 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 375 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 375 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 375 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 375 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 377 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 378 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 398 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 398 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 399 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 399 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 399 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 399 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 399 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 399 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 409 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 410 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 410 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 410 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 410 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 411 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 411 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 411 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 411 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 411 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 411 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 414 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 422 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 422 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 426 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 428 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 433 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 433 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 441 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 441 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 442 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 443 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 443 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 443 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 446 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 446 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 446 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 448 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 448 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 451 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 455 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 455 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 458 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 458 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 472 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 475 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 492 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 508 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 508 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 509 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 509 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 519 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 541 replaced by U+FFFD.
Invalid UTF-8 byte or sequence at line 541 replaced by U+FFFD.
)
Package: listofitems 2019/08/21 v1.63 Grab items in lists using user-specified 
sep char (CT)
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/tools/calc.sty
Package: calc 2017/05/25 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count269
\calc@Bcount=\count270
\calc@Adimen=\dimen153
\calc@Bdimen=\dimen154
\calc@Askip=\skip59
\calc@Bskip=\skip60
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count271
\calc@Cskip=\skip61
)
\c@@stackindex=\count272
\@boxshift=\skip62
\stack@tmplength=\skip63
\temp@stkl=\skip64
\@stackedboxwidth=\skip65
\@addedbox=\box53
\@anchorbox=\box54
\@insetbox=\box55
\se@backgroundbox=\box56
\stackedbox=\box57
\@centerbox=\box58
\c@ROWcellindex@=\count273
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/psnfss/times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/cancel/cancel.sty
Package: cancel 2013/04/12 v2.2 Cancel math terms
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsmath/amsmath.s
ty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip66

For additional information on amsmath, use the `?' option.

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsmath/amstext.s
ty
Package: amstext 2021/08/26 v2.01 AMS text

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsmath/amsgen.st
y
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks21
\ex@=\dimen155
))
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsmath/amsbsy.st
y
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen156
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsmath/amsopn.st
y
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count274
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count275
\leftroot@=\count276
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count277
\DOTSCASE@=\count278
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box59
\strutbox@=\box60
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen157
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count279
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count280
\dotsspace@=\muskip16
\c@parentequation=\count281
\dspbrk@lvl=\count282
\tag@help=\toks22
\row@=\count283
\column@=\count284
\maxfields@=\count285
\andhelp@=\toks23
\eqnshift@=\dimen158
\alignsep@=\dimen159
\tagshift@=\dimen160
\tagwidth@=\dimen161
\totwidth@=\dimen162
\lineht@=\dimen163
\@envbody=\toks24
\multlinegap=\skip67
\multlinetaggap=\skip68
\mathdisplay@stack=\toks25
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/frontendlayer
/tikz.sty
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/basiclayer/pg
f.sty
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/utilities/pgf
rcs.sty
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/p
gfutil-common.tex
\pgfutil@everybye=\toks26
\pgfutil@tempdima=\dimen164
\pgfutil@tempdimb=\dimen165
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/p
gfutil-latex.def
\pgfutil@abb=\box61
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/p
gfrcs.code.tex
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/pgf.revisio
n.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/basiclayer/pg
fcore.sty
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics/graphicx
.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics/graphics
.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics-cfg/grap
hics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics-def/xete
x.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen166
\Gin@req@width=\dimen167
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/systemlayer/p
gfsys.sty
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer
/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/p
gfkeys.code.tex
\pgfkeys@pathtoks=\toks27
\pgfkeys@temptoks=\toks28

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/p
gfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks29
))
\pgf@x=\dimen168
\pgf@y=\dimen169
\pgf@xa=\dimen170
\pgf@ya=\dimen171
\pgf@xb=\dimen172
\pgf@yb=\dimen173
\pgf@xc=\dimen174
\pgf@yc=\dimen175
\pgf@xd=\dimen176
\pgf@yd=\dimen177
\w@pgf@writea=\write4
\r@pgf@reada=\read2
\c@pgf@counta=\count286
\c@pgf@countb=\count287
\c@pgf@countc=\count288
\c@pgf@countd=\count289
\t@pgf@toka=\toks30
\t@pgf@tokb=\toks31
\t@pgf@tokc=\toks32
\pgf@sys@id@count=\count290

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer
/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer
/pgfsys-xetex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer
/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer
/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count291
)))
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer
/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count292
\pgfsyssoftpath@bigbuffer@items=\count293
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer
/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/xcolor/xcolor.sty
Package: xcolor 2022/06/12 v2.14 LaTeX color extensions (UK)

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics-cfg/colo
r.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 227.

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics/mathcolo
r.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1369.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1371.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1372.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1373.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1374.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1375.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1376.
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
h.code.tex
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hutil.code.tex)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hparser.code.tex
\pgfmath@dimen=\dimen178
\pgfmath@count=\count294
\pgfmath@box=\box62
\pgfmath@toks=\toks33
\pgfmath@stack@operand=\toks34
\pgfmath@stack@operation=\toks35
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hfunctions.code.tex)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hfunctions.basic.code.tex)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hfunctions.trigonometric.code.tex)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hfunctions.random.code.tex)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hfunctions.comparison.code.tex)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hfunctions.base.code.tex)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hfunctions.round.code.tex)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hfunctions.misc.code.tex)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hfunctions.integerarithmetics.code.tex)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hcalc.code.tex)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
hfloat.code.tex
\c@pgfmathroundto@lastzeros=\count295
))
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfint
.code.tex)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen179
\pgf@picmaxx=\dimen180
\pgf@picminy=\dimen181
\pgf@picmaxy=\dimen182
\pgf@pathminx=\dimen183
\pgf@pathmaxx=\dimen184
\pgf@pathminy=\dimen185
\pgf@pathmaxy=\dimen186
\pgf@xx=\dimen187
\pgf@xy=\dimen188
\pgf@yx=\dimen189
\pgf@yy=\dimen190
\pgf@zx=\dimen191
\pgf@zy=\dimen192
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen193
\pgf@path@lasty=\dimen194
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen195
\pgf@shorten@start@additional=\dimen196
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box63
\pgf@hbox=\box64
\pgf@layerbox@main=\box65
\pgf@picture@serial@count=\count296
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen197
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen198
\pgf@pt@y=\dimen199
\pgf@pt@temp=\dimen256
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen257
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen258
\pgf@sys@shading@range@num=\count297
\pgf@shadingcount=\count298
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box66
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/
pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/modules/pgf
moduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box67
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/modules/pgf
moduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/compatibility
/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen259
\pgf@nodesepend=\dimen260
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/compatibility
/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/utilities/pgf
for.sty
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/utilities/pgf
keys.sty
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/p
gfkeys.code.tex))
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/math/pgfmath.
sty
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmat
h.code.tex))
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/p
gffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen261
\pgffor@skip=\dimen262
\pgffor@stack=\toks36
\pgffor@toks=\toks37
))
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlay
er/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/libraries/p
gflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count299
\pgfplotmarksize=\dimen263
)
\tikz@lastx=\dimen264
\tikz@lasty=\dimen265
\tikz@lastxsaved=\dimen266
\tikz@lastysaved=\dimen267
\tikz@lastmovetox=\dimen268
\tikz@lastmovetoy=\dimen269
\tikzleveldistance=\dimen270
\tikzsiblingdistance=\dimen271
\tikz@figbox=\box68
\tikz@figbox@bg=\box69
\tikz@tempbox=\box70
\tikz@tempbox@bg=\box71
\tikztreelevel=\count300
\tikznumberofchildren=\count301
\tikznumberofcurrentchild=\count302
\tikz@fig@count=\count303

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/modules/pgf
modulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count304
\pgfmatrixcurrentcolumn=\count305
\pgf@matrix@numberofcolumns=\count306
)
\tikz@expandcount=\count307

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlay
er/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/changepage/changepage.sty
Package: changepage 2009/10/20 v1.0c check page and change page layout
\c@cp@cntr=\count308
\cp@tempcnt=\count309
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlay
er/tikz/libraries/tikzlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/libraries/s
hapes/pgflibraryshapes.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsfonts/amssymb.
sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
)
\c@theorem=\count310
\c@axiom=\count311
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 59
.
LaTeX Font Info:    No file TUptm.fd. on input line 59.


LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 59.


(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/l3backend/l3backe
nd-xetex.def
File: l3backend-xetex.def 2023-04-19 L3 backend support: XeTeX
\g__graphics_track_int=\count312
\l__pdf_internal_box=\box72
\g__pdf_backend_object_int=\count313
\g__pdf_backend_annotation_int=\count314
\g__pdf_backend_link_int=\count315
) (HW3.aux)
\openout1 = `HW3.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 59.
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 59.
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 59.
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 59.
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 59.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 5
9.

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 59.
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 59.
LaTeX Font Info:    ... okay on input line 59.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 59.
LaTeX Font Info:    ... okay on input line 59.
\c@lstlisting=\count316

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(62.59596pt, 472.31595pt, 62.59596pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=472.31595pt
* \textheight=702.78308pt
* \oddsidemargin=-9.67403pt
* \evensidemargin=-9.67403pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Font Info:    Trying to load font information for U+msa on input line 60.

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 60.

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 62.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 62.

[1

] (HW3.aux)
 ***********
LaTeX2e <2023-06-01> patch level 1
L3 programming layer <2023-08-29>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.

 ) 
Here is how much of TeX's memory you used:
 15674 strings out of 410380
 299973 string characters out of 5786836
 1912438 words of memory out of 5000000
 36764 multiletter control sequences out of 15000+600000
 560692 words of font info for 50 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 102i,6n,107p,460b,266s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on HW3.pdf (1 page).
