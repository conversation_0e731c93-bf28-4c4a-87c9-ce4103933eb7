{"cells": [{"cell_type": "code", "execution_count": 6, "id": "b25d32d8", "metadata": {}, "outputs": [], "source": ["from jupyter_manim import *\n", "from manim import *\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 15, "id": "6981c137", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Manim Community <span style=\"color: #008000; text-decoration-color: #008000\">v0.19.0</span>\n", "\n", "</pre>\n"], "text/plain": ["Manim Community \u001b[32mv0.\u001b[0m\u001b[32m19.0\u001b[0m\n", "\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                  \r"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[04/07/25 16:38:43] </span><span style=\"color: #008000; text-decoration-color: #008000\">INFO    </span> Animation <span style=\"color: #008000; text-decoration-color: #008000\">0</span> : Partial movie file written in                   <a href=\"file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">scene_file_writer.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py#588\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">588</span></a>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #008000; text-decoration-color: #008000\">'/home/<USER>/WorkSpace/Physics 522/media/videos/Physics </span>     <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">                        </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #008000; text-decoration-color: #008000\">522/720p30/partial_movie_files/PlotFunc/2016333726_1803131322</span> <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">                        </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #008000; text-decoration-color: #008000\">_223132457.mp4'</span>                                               <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">                        </span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[04/07/25 16:38:43]\u001b[0m\u001b[2;36m \u001b[0m\u001b[32mINFO    \u001b[0m Animation \u001b[32m0\u001b[0m : Partial movie file written in                   \u001b]8;id=754430;file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py\u001b\\\u001b[2mscene_file_writer.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=709800;file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py#588\u001b\\\u001b[2m588\u001b[0m\u001b]8;;\u001b\\\n", "\u001b[2;36m                    \u001b[0m         \u001b[32m'/home/<USER>/WorkSpace/Physics 522/media/videos/Physics \u001b[0m     \u001b[2m                        \u001b[0m\n", "\u001b[2;36m                    \u001b[0m         \u001b[32m522/720p30/partial_movie_files/PlotFunc/2016333726_1803131322\u001b[0m \u001b[2m                        \u001b[0m\n", "\u001b[2;36m                    \u001b[0m         \u001b[32m_223132457.mp4'\u001b[0m                                               \u001b[2m                        \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["                                                                               \r"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span><span style=\"color: #008000; text-decoration-color: #008000\">INFO    </span> Animation <span style=\"color: #008000; text-decoration-color: #008000\">1</span> : Partial movie file written in                   <a href=\"file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">scene_file_writer.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py#588\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">588</span></a>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #008000; text-decoration-color: #008000\">'/home/<USER>/WorkSpace/Physics 522/media/videos/Physics </span>     <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">                        </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #008000; text-decoration-color: #008000\">522/720p30/partial_movie_files/PlotFunc/543634251_3450934378_</span> <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">                        </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #008000; text-decoration-color: #008000\">3987418148.mp4'</span>                                               <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">                        </span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m                   \u001b[0m\u001b[2;36m \u001b[0m\u001b[32mINFO    \u001b[0m Animation \u001b[32m1\u001b[0m : Partial movie file written in                   \u001b]8;id=163860;file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py\u001b\\\u001b[2mscene_file_writer.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=569340;file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py#588\u001b\\\u001b[2m588\u001b[0m\u001b]8;;\u001b\\\n", "\u001b[2;36m                    \u001b[0m         \u001b[32m'/home/<USER>/WorkSpace/Physics 522/media/videos/Physics \u001b[0m     \u001b[2m                        \u001b[0m\n", "\u001b[2;36m                    \u001b[0m         \u001b[32m522/720p30/partial_movie_files/PlotFunc/543634251_3450934378_\u001b[0m \u001b[2m                        \u001b[0m\n", "\u001b[2;36m                    \u001b[0m         \u001b[32m3987418148.mp4'\u001b[0m                                               \u001b[2m                        \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[04/07/25 16:38:44] </span><span style=\"color: #008000; text-decoration-color: #008000\">INFO    </span> Animation <span style=\"color: #008000; text-decoration-color: #008000\">2</span> : Partial movie file written in                   <a href=\"file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">scene_file_writer.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py#588\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">588</span></a>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #008000; text-decoration-color: #008000\">'/home/<USER>/WorkSpace/Physics 522/media/videos/Physics </span>     <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">                        </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #008000; text-decoration-color: #008000\">522/720p30/partial_movie_files/PlotFunc/543634251_4217992463_</span> <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">                        </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #008000; text-decoration-color: #008000\">2339291126.mp4'</span>                                               <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">                        </span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[04/07/25 16:38:44]\u001b[0m\u001b[2;36m \u001b[0m\u001b[32mINFO    \u001b[0m Animation \u001b[32m2\u001b[0m : Partial movie file written in                   \u001b]8;id=266498;file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py\u001b\\\u001b[2mscene_file_writer.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=729409;file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py#588\u001b\\\u001b[2m588\u001b[0m\u001b]8;;\u001b\\\n", "\u001b[2;36m                    \u001b[0m         \u001b[32m'/home/<USER>/WorkSpace/Physics 522/media/videos/Physics \u001b[0m     \u001b[2m                        \u001b[0m\n", "\u001b[2;36m                    \u001b[0m         \u001b[32m522/720p30/partial_movie_files/PlotFunc/543634251_4217992463_\u001b[0m \u001b[2m                        \u001b[0m\n", "\u001b[2;36m                    \u001b[0m         \u001b[32m2339291126.mp4'\u001b[0m                                               \u001b[2m                        \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span><span style=\"color: #008000; text-decoration-color: #008000\">INFO    </span> Combining to Movie file.                                      <a href=\"file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">scene_file_writer.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py#739\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">739</span></a>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m                   \u001b[0m\u001b[2;36m \u001b[0m\u001b[32mINFO    \u001b[0m Combining to Movie file.                                      \u001b]8;id=860600;file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py\u001b\\\u001b[2mscene_file_writer.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=279773;file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py#739\u001b\\\u001b[2m739\u001b[0m\u001b]8;;\u001b\\\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span><span style=\"color: #008000; text-decoration-color: #008000\">INFO    </span>                                                               <a href=\"file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">scene_file_writer.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py#886\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">886</span></a>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">File</span> ready at <span style=\"color: #008000; text-decoration-color: #008000\">'/home/<USER>/WorkSpace/Physics </span>                <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">                        </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #008000; text-decoration-color: #008000\">522/media/videos/Physics 522/720p30/PlotFunc.mp4'</span>             <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">                        </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>                                                                       <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">                        </span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m                   \u001b[0m\u001b[2;36m \u001b[0m\u001b[32mINFO    \u001b[0m                                                               \u001b]8;id=67551;file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py\u001b\\\u001b[2mscene_file_writer.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=108222;file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene_file_writer.py#886\u001b\\\u001b[2m886\u001b[0m\u001b]8;;\u001b\\\n", "\u001b[2;36m                    \u001b[0m         \u001b[1;33mFile\u001b[0m ready at \u001b[32m'/home/<USER>/WorkSpace/Physics \u001b[0m                \u001b[2m                        \u001b[0m\n", "\u001b[2;36m                    \u001b[0m         \u001b[32m522/media/videos/Physics 522/720p30/PlotFunc.mp4'\u001b[0m             \u001b[2m                        \u001b[0m\n", "\u001b[2;36m                    \u001b[0m                                                                       \u001b[2m                        \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span><span style=\"color: #008000; text-decoration-color: #008000\">INFO    </span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">Rendered</span> PlotFunc                                                         <a href=\"file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">scene.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene.py#255\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">255</span></a>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">Played</span> <span style=\"color: #008000; text-decoration-color: #008000\">3</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">animations</span>                                                       <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">            </span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m                   \u001b[0m\u001b[2;36m \u001b[0m\u001b[32mINFO    \u001b[0m \u001b[1;33mRendered\u001b[0m PlotFunc                                                         \u001b]8;id=422449;file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene.py\u001b\\\u001b[2mscene.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=33442;file:///home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/manim/scene/scene.py#255\u001b\\\u001b[2m255\u001b[0m\u001b]8;;\u001b\\\n", "\u001b[2;36m                    \u001b[0m         \u001b[1;33mPlayed\u001b[0m \u001b[32m3\u001b[0m \u001b[1;33manimations\u001b[0m                                                       \u001b[2m            \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<video src=\"media/jupyter/PlotFunc@2025-04-07@16-38-44.mp4\" controls autoplay loop style=\"max-width: 60%;\"  >\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%%manim -qm PlotFunc\n", "\n", "from manim import *\n", "\n", "class PlotFunc(Scene):\n", "    def construct(self):\n", "        axes = Axes(\n", "            x_range=[0.01, 10, 1],\n", "            y_range=[-5, 10, 1],\n", "            axis_config={\"color\": BLUE},\n", "            x_length=10,\n", "            y_length=15,\n", "        )\n", "        self.play(Create(axes))\n", "        self.play(Create(axes.plot(lambda x: x*np.log(np.exp(PI/x)-1), color=RED)))\n", "\n", "        self.wait(1)"]}, {"cell_type": "code", "execution_count": null, "id": "6eda139f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "mainenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}