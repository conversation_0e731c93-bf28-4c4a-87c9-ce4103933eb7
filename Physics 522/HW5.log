This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2026/dev/Arch Linux) (preloaded format=xelatex 2025.4.1)  7 APR 2025 19:19
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/home/<USER>/WorkSpace/Physics 522/HW5"
(/home/<USER>/WorkSpace/Physics 522/HW5.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count192
\c@section=\count193
\c@subsection=\count194
\c@subsubsection=\count195
\c@paragraph=\count196
\c@subparagraph=\count197
\c@figure=\count198
\c@table=\count199
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/usr/share/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks17
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (/usr/share/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers
\f@nch@headwidth=\skip51
\f@nch@offset@elh=\skip52
\f@nch@offset@erh=\skip53
\f@nch@offset@olh=\skip54
\f@nch@offset@orh=\skip55
\f@nch@offset@elf=\skip56
\f@nch@offset@erf=\skip57
\f@nch@offset@olf=\skip58
\f@nch@offset@orf=\skip59
\f@nch@height=\skip60
\f@nch@footalignment=\skip61
\f@nch@widthL=\skip62
\f@nch@widthC=\skip63
\f@nch@widthR=\skip64
\@temptokenb=\toks18
) (/usr/share/texmf-dist/tex/latex/listings/listings.sty (/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
)
\lst@mode=\count266
\lst@gtempboxa=\box52
\lst@token=\toks20
\lst@length=\count267
\lst@currlwidth=\dimen142
\lst@column=\count268
\lst@pos=\count269
\lst@lostspace=\dimen143
\lst@width=\dimen144
\lst@newlines=\count270
\lst@lineno=\count271
\lst@maxwidth=\dimen145
 (/usr/share/texmf-dist/tex/latex/listings/lstpatch.sty
File: lstpatch.sty 2024/09/23 1.10c (Carsten Heinz)
) (/usr/share/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2024/09/23 1.10c (Carsten Heinz)
\c@lstnumber=\count272
\lst@skipnumbers=\count273
\lst@framebox=\box53
) (/usr/share/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2024/09/23 1.10c listings configuration
))
Package: listings 2024/09/23 1.10c (Carsten Heinz)
 (/usr/share/texmf-dist/tex/latex/comment/comment.sty
\CommentStream=\write3
 Excluding comment 'comment') (/usr/share/texmf-dist/tex/latex/fontspec/fontspec.sty (/usr/share/texmf-dist/tex/latex/l3packages/xparse/xparse.sty (/usr/share/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (/usr/share/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count274
\l__pdf_internal_box=\box54
\g__pdf_backend_annotation_int=\count275
\g__pdf_backend_link_int=\count276
))
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
 (/usr/share/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count277
\l__fontspec_language_int=\count278
\l__fontspec_strnum_int=\count279
\l__fontspec_tmp_int=\count280
\l__fontspec_tmpa_int=\count281
\l__fontspec_tmpb_int=\count282
\l__fontspec_tmpc_int=\count283
\l__fontspec_em_int=\count284
\l__fontspec_emdef_int=\count285
\l__fontspec_strong_int=\count286
\l__fontspec_strongdef_int=\count287
\l__fontspec_tmpa_dim=\dimen146
\l__fontspec_tmpb_dim=\dimen147
\l__fontspec_tmpc_dim=\dimen148
 (/usr/share/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (/usr/share/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (/usr/share/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/share/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count288
\Gm@cntv=\count289
\c@Gm@tempcnt=\count290
\Gm@bindingoffset=\dimen149
\Gm@wd@mp=\dimen150
\Gm@odd@mp=\dimen151
\Gm@even@mp=\dimen152
\Gm@layoutwidth=\dimen153
\Gm@layoutheight=\dimen154
\Gm@layouthoffset=\dimen155
\Gm@layoutvoffset=\dimen156
\Gm@dimlist=\toks21
) (/usr/share/texmf-dist/tex/latex/stackengine/stackengine.sty
Package: stackengine 2021/07/22 v4.11\ Stacking text and objects in convenient ways
 (/usr/share/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count291
) (/usr/share/texmf-dist/tex/generic/listofitems/listofitems.sty (/usr/share/texmf-dist/tex/generic/listofitems/listofitems.tex
\loi_cnt_foreach_nest=\count292
\loi_nestcnt=\count293
)
Package: listofitems 2024/03/09 v1.65 Grab items in lists using user-specified sep char (CT)
) (/usr/share/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count294
\calc@Bcount=\count295
\calc@Adimen=\dimen157
\calc@Bdimen=\dimen158
\calc@Askip=\skip65
\calc@Bskip=\skip66
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count296
\calc@Cskip=\skip67
)
\c@@stackindex=\count297
\@boxshift=\skip68
\stack@tmplength=\skip69
\temp@stkl=\skip70
\@stackedboxwidth=\skip71
\@addedbox=\box55
\@anchorbox=\box56
\@insetbox=\box57
\se@backgroundbox=\box58
\stackedbox=\box59
\@centerbox=\box60
\c@ROWcellindex@=\count298
) (/usr/share/texmf-dist/tex/latex/psnfss/times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
) (/usr/share/texmf-dist/tex/latex/cancel/cancel.sty
Package: cancel 2013/04/12 v2.2 Cancel math terms
) (/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip72

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks22
\ex@=\dimen159
)) (/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen160
) (/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count299
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count300
\leftroot@=\count301
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count302
\DOTSCASE@=\count303
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box61
\strutbox@=\box62
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen161
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count304
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count305
\dotsspace@=\muskip17
\c@parentequation=\count306
\dspbrk@lvl=\count307
\tag@help=\toks23
\row@=\count308
\column@=\count309
\maxfields@=\count310
\andhelp@=\toks24
\eqnshift@=\dimen162
\alignsep@=\dimen163
\tagshift@=\dimen164
\tagwidth@=\dimen165
\totwidth@=\dimen166
\lineht@=\dimen167
\@envbody=\toks25
\multlinegap=\skip73
\multlinetaggap=\skip74
\mathdisplay@stack=\toks26
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/share/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks27
\pgfutil@tempdima=\dimen168
\pgfutil@tempdimb=\dimen169
) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box63
) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/share/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/share/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/share/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
 (/usr/share/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen170
\Gin@req@width=\dimen171
) (/usr/share/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks28
\pgfkeys@temptoks=\toks29
 (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks30
))
\pgf@x=\dimen172
\pgf@y=\dimen173
\pgf@xa=\dimen174
\pgf@ya=\dimen175
\pgf@xb=\dimen176
\pgf@yb=\dimen177
\pgf@xc=\dimen178
\pgf@yc=\dimen179
\pgf@xd=\dimen180
\pgf@yd=\dimen181
\w@pgf@writea=\write4
\r@pgf@reada=\read2
\c@pgf@counta=\count311
\c@pgf@countb=\count312
\c@pgf@countc=\count313
\c@pgf@countd=\count314
\t@pgf@toka=\toks31
\t@pgf@tokb=\toks32
\t@pgf@tokc=\toks33
\pgf@sys@id@count=\count315
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-xetex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count316
))) (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count317
\pgfsyssoftpath@bigbuffer@items=\count318
) (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen182
\pgfmath@count=\count319
\pgfmath@box=\box64
\pgfmath@toks=\toks34
\pgfmath@stack@operand=\toks35
\pgfmath@stack@operation=\toks36
) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count320
)) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen183
\pgf@picmaxx=\dimen184
\pgf@picminy=\dimen185
\pgf@picmaxy=\dimen186
\pgf@pathminx=\dimen187
\pgf@pathmaxx=\dimen188
\pgf@pathminy=\dimen189
\pgf@pathmaxy=\dimen190
\pgf@xx=\dimen191
\pgf@xy=\dimen192
\pgf@yx=\dimen193
\pgf@yy=\dimen194
\pgf@zx=\dimen195
\pgf@zy=\dimen196
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen197
\pgf@path@lasty=\dimen198
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen199
\pgf@shorten@start@additional=\dimen256
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box65
\pgf@hbox=\box66
\pgf@layerbox@main=\box67
\pgf@picture@serial@count=\count321
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen257
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen258
\pgf@pt@y=\dimen259
\pgf@pt@temp=\dimen260
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen261
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen262
\pgf@sys@shading@range@num=\count322
\pgf@shadingcount=\count323
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box68
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box69
) (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen263
\pgf@nodesepend=\dimen264
) (/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/usr/share/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen265
\pgffor@skip=\dimen266
\pgffor@stack=\toks37
\pgffor@toks=\toks38
)) (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count324
\pgfplotmarksize=\dimen267
)
\tikz@lastx=\dimen268
\tikz@lasty=\dimen269
\tikz@lastxsaved=\dimen270
\tikz@lastysaved=\dimen271
\tikz@lastmovetox=\dimen272
\tikz@lastmovetoy=\dimen273
\tikzleveldistance=\dimen274
\tikzsiblingdistance=\dimen275
\tikz@figbox=\box70
\tikz@figbox@bg=\box71
\tikz@tempbox=\box72
\tikz@tempbox@bg=\box73
\tikztreelevel=\count325
\tikznumberofchildren=\count326
\tikznumberofcurrentchild=\count327
\tikz@fig@count=\count328
 (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count329
\pgfmatrixcurrentcolumn=\count330
\pgf@matrix@numberofcolumns=\count331
)
\tikz@expandcount=\count332
 (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/changepage/changepage.sty
Package: changepage 2009/10/20 v1.0c check page and change page layout
\c@cp@cntr=\count333
\cp@tempcnt=\count334
) (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
)
\c@theorem=\count335
\c@axiom=\count336

Package fontspec Info: 
(fontspec)             Font family 'TimesNewRoman(0)' created for font 'Times
(fontspec)             New Roman' with options [Ligatures=TeX].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"Times New
(fontspec)             Roman/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"Times New
(fontspec)             Roman/B/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"Times New
(fontspec)             Roman/I/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"Times New
(fontspec)             Roman/BI/OT:script=latn;language=dflt;mapping=tex-text;"

 (./HW5.aux)
\openout1 = `HW5.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
\c@lstlisting=\count337

Package fontspec Info: 
(fontspec)             Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 62.
LaTeX Font Info:    Redeclaring math accent \acute on input line 62.
LaTeX Font Info:    Redeclaring math accent \grave on input line 62.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 62.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 62.
LaTeX Font Info:    Redeclaring math accent \bar on input line 62.
LaTeX Font Info:    Redeclaring math accent \breve on input line 62.
LaTeX Font Info:    Redeclaring math accent \check on input line 62.
LaTeX Font Info:    Redeclaring math accent \hat on input line 62.
LaTeX Font Info:    Redeclaring math accent \dot on input line 62.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 62.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 62.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 62.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 62.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/TimesNewRoman(0)/m/n on input line 62.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 62.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRoman(0)/m/n on input line 62.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/TimesNewRoman(0)/m/n --> TU/TimesNewRoman(0)/m/n on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/TimesNewRoman(0)/m/it on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRoman(0)/b/n on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/phv/m/n on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/pcr/m/n on input line 62.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/TimesNewRoman(0)/m/n --> TU/TimesNewRoman(0)/b/n on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/TimesNewRoman(0)/b/it on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/phv/b/n on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/pcr/b/n on input line 62.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(62.59596pt, 472.31595pt, 62.59596pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=472.31595pt
* \textheight=702.78308pt
* \oddsidemargin=-9.67403pt
* \evensidemargin=-9.67403pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Font Info:    Trying to load font information for U+msa on input line 63.
(/usr/share/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 63.
 (/usr/share/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[1

]

[2]

[3]
File: /home/<USER>/Pictures/screenshot_07042025_170954.jpg Graphic file (type bmp)
</home/<USER>/Pictures/screenshot_07042025_170954.jpg>


[4]

[5]
Underfull \hbox (badness 10000) in paragraph at lines 204--206

 []



[6] (./HW5.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
 ) 
Here is how much of TeX's memory you used:
 18347 strings out of 475848
 395779 string characters out of 5776014
 809019 words of memory out of 5000000
 40962 multiletter control sequences out of 15000+600000
 566249 words of font info for 86 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 102i,7n,107p,449b,266s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on HW5.pdf (6 pages).
