\documentclass[12pt]{article}%
\usepackage{amsfonts}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{comment}
\usepackage{fontspec}
\usepackage[a4paper, top=2.5cm, bottom=2.5cm, left=2.2cm, right=2.2cm]{geometry}
\def\shrinkage{2.1mu}
\def\vecsign{\mathchar"017E}
\def\dvecsign{\smash{\stackon[-1.95pt]{\mkern-\shrinkage\vecsign}{\rotatebox{180}{$\mkern-\shrinkage\vecsign$}}}}
\def\dvec#1{\def\useanchorwidth{T}\stackon[-4.2pt]{#1}{\,\dvecsign}}
\usepackage{stackengine}
\stackMath
\usepackage{times}
\usepackage{cancel}
\usepackage{amsmath}
\usepackage{tikz}
\usepackage{changepage}
\usetikzlibrary{shapes.misc}
\usepackage{amssymb}
\usepackage{graphicx}%
\setcounter{MaxMatrixCols}{30}
\newtheorem{theorem}{Theorem}
\newtheorem{acknowledgement}[theorem]{Acknowledgement}
\newtheorem{algorithm}[theorem]{Algorithm}
\newtheorem{axiom}{Axiom}
\newtheorem{case}[theorem]{Case}
\newtheorem{claim}[theorem]{Claim}
\newtheorem{conclusion}[theorem]{Conclusion}
\newtheorem{condition}[theorem]{Condition}
\newtheorem{conjecture}[theorem]{Conjecture}
\newtheorem{corollary}[theorem]{Corollary}
\newtheorem{criterion}[theorem]{Criterion}
\newtheorem{definition}[theorem]{Definition}
\newtheorem{example}[theorem]{Example}
\newtheorem{exercise}[theorem]{Exercise}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{notation}[theorem]{Notation}
\newtheorem{problem}[theorem]{Problem}
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{remark}[theorem]{Remark}
\newtheorem{summary}[theorem]{Summary}
\newenvironment{proof}[1][Proof]{\textbf{#1.} }{\ \rule{0.5em}{0.5em}}
\newenvironment{solution}[1][Solution]{\textbf{#1}}{\ \rule{0.5em}{0.5em}}
\setmainfont{Times New Roman} % Replace with your desired font
\newcommand{\Q}{\mathbb{Q}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\Z}{\mathbb{Z}}
\newcommand{\N}{\mathbb{N}}
\newcommand{\Lagr}{\mathcal{L}}
\newcommand{\lagr}{\mathcal{L}_0}
\newcommand{\lb}{\left\{}
\newcommand{\rb}{\right\}}
\newcommand{\set}[1]{\left\{#1\right\}}
\newcommand{\pr}[1]{\left(\{#1\right\})}
\newcommand{\dbar}[1]{\bar{\bar{#1}}}
\newcommand{\tbar}[1]{\bar{\bar{\bar{#1}}}}
\newcommand{\h}{\hslash} % New command for hbar
\title{Physics 522 Statistical Mechanics Homework 5}
\author{Tyler Hadsell}
\begin{document}
\maketitle
\begin{problem}
\end{problem}
\begin{solution}\\
We can begin with the Fermi-Dirac distribution:
$$f(\epsilon) = \frac{1}{e^{\beta(\epsilon - \mu)} + 1}$$
We can then take the derivative of this with respect to $\epsilon$ in the limit $\epsilon \rightarrow \mu$:
$$\frac{\partial f}{\partial \epsilon} = \lim_{\epsilon \to \mu}\left(-\frac{\beta e^{\beta\left(\epsilon - \mu\right)}}{(e^{\beta\left(\epsilon - \mu\right)} + 1)^2}\right) = -\frac{\beta}{4} = -\frac{1}{4k_BT}$$
Therefore:
$$\boxed{-\frac{\partial f}{\partial \epsilon}\Bigg|_{\epsilon=\mu} = \frac{1}{4k_BT}}$$
\end{solution}
\begin{problem}

\end{problem}
\begin{solution}\\
    We begin with the grand partition function for $\ell$ particles in an energy state $\epsilon$:
    $$\mathcal{Z}_\ell(\epsilon) = \sum_{n=0}^\ell e^{-\beta(\epsilon - \mu)n}$$

    This is not easy to evaluate in closed form in its current state, so we will split up the sum into two parts:
    $$\mathcal{Z}_\ell(\epsilon) = \sum_{n=0}^\infty e^{-\beta(\epsilon - \mu)n}  - \sum_{n=\ell+1}^\infty e^{-\beta(\epsilon - \mu)n}$$
    We can then evaluate the first sum:
    $$\sum_{n=0}^\infty e^{-\beta(\epsilon - \mu)n} = \frac{1}{1 - e^{-\beta(\epsilon - \mu)}}$$
    For the second sum, we can reindex using the following transformation:
    $$n' = 0 \text{ when } n = \ell + 1$$
    $$n' \to n - \left(\ell + 1\right)$$
    $$\Rightarrow n = n' + \left(\ell + 1\right)$$

    Our second sum then becomes:
    $$\sum_{n' = 0}^\infty e^{-\beta(\epsilon - \mu )\left(n' + \ell + 1\right)} = e^{-\beta(\epsilon - \mu)\left(\ell + 1\right)}\sum_{n' = 0}^\infty e^{-\beta(\epsilon - \mu)n'}$$
    Now the sum is in the geometric form, so we can evaluate it:
    $$\sum_{n' = 0}^\infty e^{-\beta(\epsilon - \mu)n'} = \frac{1}{1 - e^{-\beta(\epsilon - \mu)}}$$
    Our total partition function is then:
    $$\mathcal{Z}_\ell(\epsilon) = \frac{1}{1 - e^{-\beta(\epsilon - \mu)}} - e^{-\beta(\epsilon - \mu)\left(\ell + 1\right)}\frac{1}{1 - e^{-\beta(\epsilon - \mu)}} = \frac{1 - e^{-\beta(\epsilon - \mu)\left(\ell + 1\right)}}{1 - e^{-\beta(\epsilon - \mu)}}$$
    We can now find the average number of particles in the energy state $\epsilon$:
    $$\langle n \rangle = \frac{1}{\beta}\frac{\partial}{\partial \mu}\ln \mathcal{Z}_\ell(\epsilon) = \frac{1}{\beta}\frac{\partial}{\partial \mu}\ln \left(1 - e^{-\beta(\epsilon - \mu)\left(\ell + 1\right)}\right) - \frac{1}{\beta}\frac{\partial}{\partial \mu}\ln \left(1 - e^{-\beta(\epsilon - \mu)}\right)$$
    $$=\frac{1}{\beta} \left(\frac{-\beta\left(\ell + 1\right)e^{-\beta\left(\epsilon - \mu\right)\left(\ell + 1\right)}}{1 - e^{-\beta\left(\epsilon - \mu\right)\left(\ell + 1\right)}} + \frac{\beta e^{-\beta\left(\epsilon - \mu\right)}}{1 - e^{-\beta\left(\epsilon - \mu\right)}}\right)$$
    We can then simplify this by factoring out $e^{-\beta\left(\epsilon - \mu\right)} = e^{-\beta\epsilon}f$ from the numerators and denominators of the two terms:
    $$\boxed{\langle n \rangle = \frac{1}{e^{\beta\epsilon}f^{-1} -1} - \frac{\left(\ell + 1\right)}{\left(e^{\beta\epsilon}f^{-1}\right)^{\left(\ell + 1\right)} - 1}}$$
    If we take the limit as $\ell \to \infty$, we get:
    $$\lim_{\ell \to \infty} \langle n \rangle = \lim_{\ell \to \infty}\left(\frac{1}{e^{\beta\epsilon}f^{-1} -1} - \frac{\left(\ell + 1\right)}{\left(e^{\beta\epsilon}f^{-1}\right)^{\left(\ell + 1\right)} - 1}\right) = \boxed{\frac{1}{e^{\beta\epsilon}f^{-1} - 1}}$$
    This is the same result as we get in Bose-Einstein statistics, which is expected
    When we take the $\ell = 1$ limit, we get:
    $$\lim_{\ell \to 1} \langle n \rangle = \lim_{\ell \to 1}\left(\frac{1}{e^{\beta\epsilon}f^{-1} -1} - \frac{\left(\ell + 1\right)}{\left(e^{\beta\epsilon}f^{-1}\right)^{\left(\ell + 1\right)} - 1}\right) $$$$= \frac{1}{e^{\beta\epsilon}f^{-1} - 1} - \frac{2}{\left(e^{\beta\epsilon}f^{-1}\right)^2 - 1} = \boxed{\frac{1}{e^{\beta\epsilon}f^{-1} + 1}}$$
    This is the same result as we get in Fermi-Dirac statistics, which is expected.

\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
    We can begin this problem with the following expressions:
    $$N=\sum_i \langle n_i\rangle, \quad \epsilon = \frac{\h^2 k^2}{2m}, \quad N = \int_{-\infty}^{\infty} \frac{L}{2\pi} \langle n_i\rangle dk$$
    We can solve to take this integral in terms of $k$
    $$d\epsilon = \frac{2m}{\h^2}kdk$$
    $$k = \sqrt{\frac{2m}{\h^2}\epsilon}$$
    $$dk = \sqrt{\frac{m}{2\h^2}}\epsilon^{-\frac{1}{2}}d\epsilon$$
    Our integral becomes
    $$N = \int_0^\infty \frac{L}{2\pi\left(e^{\beta\left(\epsilon - \mu\right)} - 1\right)} \sqrt{\frac{m}{2\h^2}}\epsilon^{-\frac{1}{2}}d\epsilon$$
    The constants are irrelevant so we will move them outside the integral:
    $$N = \gamma \int_0^\infty \frac{1}{e^{\beta\left(\epsilon-\mu\right)}-1}\epsilon^{-\frac{1}{2}}d\epsilon$$
    Additionally, we take the fugacity $f=1$ limit:
    $$N = \gamma \int_0^\infty \frac{1}{e^{\beta\epsilon}-1}\epsilon^{-\frac{1}{2}}d\epsilon$$
    This integral does not converge for any finite temperature.
    At the $T = 0$ limit, we see that $\beta = \infty$, so the integral becomes:
    $$N = 0$$
    This means that there are no particles in the system, meaning the state is not a Bose-Einstein condensate. In one dimension therefore, a Bose-Einstein condensate cannot exist purely by temperature and volumetric confinement.
\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
    1.)\\
    We can begin with the Density of States of a two-dimensional Fermi gas:
    $$g(\epsilon) = \frac{Am}{\pi \h^2}$$
    To obtain the total number of particles we integrate the Density of States, weighted by the  Fermi-Dirac distribution:
    $$N = \int_0^\infty g(\epsilon)f(\epsilon)d\epsilon = \int_0^\infty \frac{Am}{\pi \h^2} \frac{1}{e^{\beta(\epsilon - \mu)} + 1}d\epsilon$$
    We can utilize the following substitution:
    $$\beta\left(\epsilon - \mu\right) \to u$$
    $$\Rightarrow \beta d\epsilon = du$$
    $$N = \frac{Am}{\beta \pi \h^2}\int_{-\beta \mu}^\infty \frac{1}{e^u + 1}du = \frac{Am}{\beta \pi \h^2}\left[u -\ln\left(1 + e^u\right)\right]_{-\beta \mu}^\infty$$

    Evaluating the bounds and simplifying limits we obtain:
    $$N = \frac{Am}{\beta \pi \h^2}\left(\beta \mu + \ln\left(1 + e^{-\beta \mu}\right)\right)$$
    Now let us solve for $\mu$:
    $$\frac{\pi N\beta\h^2}{Am} = \beta\mu + \ln\left(1 + e^{-\beta \mu}\right)$$
    We can combine the two terms into a single logarithm:
    $$\frac{\pi N\beta\h^2}{Am} = \beta\mu + \ln\left(1 + e^{-\beta \mu}\right) = \ln\left[\left(1+e^{-\beta\mu}\right)e^{\beta\mu}\right]$$
    We can exponentiate both sides:
    $$e^{\frac{\pi N\beta\h^2}{Am}} = \left(1+e^{-\beta\mu}\right)e^{\beta\mu} = e^{\beta\mu} +1$$
    $$e^{\frac{\pi N\beta\h^2}{Am}} - 1 = e^{\beta\mu}$$
    We can now take the logarithm again on both sides:
    $$\ln\left(e^{\frac{\pi N\beta\h^2}{Am}} - 1\right) = \beta\mu$$
    $$\boxed{\mu\left(T\right) = \frac{1}{\beta}\ln\left(e^{\frac{\pi N\beta\h^2}{Am} - 1}\right)= k_B T \ln\left(e^{\frac{\pi N\h^2}{Am k_B T}} - 1\right)}$$
    2.)\\ 
    To find the equation's behavior in the low temperature limit we can take the limit as $T \to 0$:
    $$\lim_{T \to 0} \mu\left(T\right) = \lim_{T \to 0} k_B T \ln\left(e^{\frac{\pi N\h^2}{Am k_B T}} - 1\right)$$
    The exponential term of the logarithm will dominate the $-1$ term, so we can approximate the logarithm as:
    $$\lim_{T \to 0} \mu\left(T\right) = \lim_{T \to 0} k_B T \ln\left(e^{\frac{\pi N\h^2}{Am k_B T}}\right) = \lim_{T \to 0} k_B T \left(\frac{\pi N\h^2}{Am k_B T}\right) = \boxed{\frac{\pi N\h^2}{Am}}$$
    3.)\\ 
    For the high temperature limit we can take the limit as $T \to \infty$:
    $$\lim_{T \to \infty} \mu\left(T\right) = \lim_{T \to \infty} k_B T \ln\left(e^{\frac{\pi N\h^2}{Am k_B T}} - 1\right)$$
    The exponential term in the logarithm will be small as $T \to \infty$, so we can approximate the exponential as:
        $$e^{\frac{\pi N\h^2}{Am k_B T}} \approx 1 + \frac{\pi N\h^2}{Am k_B T}$$
    This gives us:
    $$\boxed{\lim_{T \to \infty} \mu\left(T\right) = k_B T \ln\left(\frac{\pi N\h^2}{Am k_B T}\right)}$$
    4.)\\
    Now plotting our exact function for $\mu\left(T\right)$ with parameters $\h = 1$, $m = 1$, $A = 1$, $N = 1$, and $k_B = 1$, we get:
    \begin{center}
    \includegraphics[scale=0.1]{/home/<USER>/Pictures/screenshot_07042025_170954.jpg}
    \end{center}
\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
    1.)\\
    We can begin with the Fermi Energy for a three-dimensional Fermi gas:
    $$\epsilon_{F_\text{silver}} = \frac{\h^2}{2m}\left(\frac{3\pi^2N}{V}\right)^{\frac{2}{3}} = \frac{\h^2}{2m}\left(3\pi^2n\right)^{\frac{2}{3}}$$
    The number density for conduction electrons in Silver is $n = 5.86\times 10^{28} m^{-3}$.
    The mass of an electron is given by $m = 9.11\times 10^{-31} kg$ and the reduced Planck's constant is $\h = 1.055\times 10^{-34} J\cdot s$.
    Plugging these values into the Fermi Energy equation we get:
    $$\epsilon_{F_\text{silver}} = \frac{\h^2}{2m}\left(3\pi^2n\right)^{\frac{2}{3}} = \frac{\left(1.055\times 10^{-34} J\cdot s\right)^2}{2\left(9.11\times 10^{-31} kg\right)}\left(3\pi^2\left(5.86\times 10^{28} m^{-3}\right)\right)^{\frac{2}{3}} = 8.80\times 10^{-19} J$$
    We can convert this energy to electron volts by dividing by the charge of an electron:
    $$\boxed{\epsilon_{F_\text{silver}} = \frac{8.80\times 10^{-19} J}{1.60\times 10^{-19} C} = 5.50 eV}$$
    For Lead we have $n = 1.32\times 10^{29} m^{-3}$, so the Fermi Energy is:
    $$\epsilon_{F_\text{lead}} = \frac{\h^2}{2m}\left(3\pi^2n\right)^{\frac{2}{3}} = \frac{\left(1.055\times 10^{-34} J\cdot s\right)^2}{2\left(9.11\times 10^{-31} kg\right)}\left(3\pi^2\left(1.32\times 10^{29} m^{-3}\right)\right)^{\frac{2}{3}} = 1.51 \times 10^{-18} J$$
    $$\boxed{\epsilon_{F_\text{lead}} = \frac{1.51 \times 10^{-18} J}{1.60\times 10^{-19} C} = 9.44 eV}$$
    Now for Aluminum we have $n = 1.81\times 10^{29} m^{-3}$, so the Fermi Energy is:
    $$\epsilon_{F_\text{aluminum}} = \frac{\h^2}{2m}\left(3\pi^2n\right)^{\frac{2}{3}} = \frac{\left(1.055\times 10^{-34} J\cdot s\right)^2}{2\left(9.11\times 10^{-31} kg\right)}\left(3\pi^2\left(1.81\times 10^{29} m^{-3}\right)\right)^{\frac{2}{3}} = 1.87 \times 10^{-18} J$$
    $$\boxed{\epsilon_{F_\text{aluminum}} = \frac{1.87 \times 10^{-18} J}{1.60\times 10^{-19} C} = 11.66 eV}$$
    2.)\\ 
    We can start with the atomic volume for $\textbf{He}^3$
    $$V = 63 \times 10^{-30} \frac{\text{m}^3}{\text{atom}}$$
    The number density is given by:
    $$n = \frac{1}{V} = \frac{1}{63 \times 10^{-30} \frac{\text{m}^3}{\text{atom}}} = 1.59\times 10^{28} \,\frac{\text{atoms}}{\text{m}^3}$$
    The mass of a single $\textbf{He}^3$ atom is $3.0160293 \text{ amu} = 4.99\times 10^{-27} kg$.
    Calculating the Fermi-Energy we obtain:
    $$\epsilon_{F_\text{helium}} = \frac{\h^2}{2m}\left(3\pi^2n\right)^{\frac{2}{3}} = \frac{\left(1.055\times 10^{-34} J\cdot s\right)^2}{2\left(4.99\times 10^{-27} kg\right)}\left(3\pi^2\left(1.59\times 10^{28} \frac{\text{atoms}}{\text{m}^3}\right)\right)^{\frac{2}{3}} = 1.12 \times 10^{-18} J$$
    $$\boxed{\epsilon_{F_\text{helium}} = 4.21 \times 10^{-4} eV}$$

    1T.)\\ 
    Now to find the Fermi Temperature we simply divide the Fermi Energy by the Boltzmann constant:
    $$\boxed{T_{F_{\text{silver}}} = \frac{\epsilon_{F_\text{silver}}}{k_B} = \frac{5.50 eV}{8.62\times 10^{-5} eV/K} = 6.38\times 10^4 K}$$
    $$\boxed{T_{F_{\text{lead}}} = \frac{\epsilon_{F_\text{lead}}}{k_B} = \frac{9.44 eV}{8.62\times 10^{-5} eV/K} = 1.09\times 10^5 K}$$
    $$\boxed{T_{F_{\text{aluminum}}} = \frac{\epsilon_{F_\text{aluminum}}}{k_B} = \frac{11.66 eV}{8.62\times 10^{-5} eV/K} = 1.35\times 10^5 K}$$
    2T.)\\
    $$\boxed{T_{F_{\text{helium}}} = \frac{\epsilon_{F_\text{helium}}}{k_B} = \frac{4.21 \times 10^{-4} eV}{8.62\times 10^{-5} eV/K} = 4.88 K}$$
 
\end{solution}
\end{document}