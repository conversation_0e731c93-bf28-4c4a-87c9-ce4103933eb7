This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024/Arch Linux) (preloaded format=xelatex 2024.8.31)  8 OCT 2024 11:39
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/home/<USER>/WorkSpace/PhDApplications/AwesomeCV/Awesome CV/coverletter"
(/home/<USER>/WorkSpace/PhDApplications/AwesomeCV/Awesome CV/coverletter.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(./awesome-cv.cls
Document Class: awesome-cv 2017/02/05 v1.6.1 Awesome Curriculum Vitae Class
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count184
\c@section=\count185
\c@subsection=\count186
\c@subsubsection=\count187
\c@paragraph=\count188
\c@subparagraph=\count189
\c@figure=\count190
\c@table=\count191
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (/usr/share/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen141
\ar@mcellbox=\box51
\extrarowheight=\dimen142
\NC@list=\toks17
\extratabsurround=\skip50
\backup@length=\skip51
\ar@cellbox=\box52
) (/usr/share/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\enitkv@toks@=\toks18
\labelindent=\skip52
\enit@outerparindent=\dimen143
\enit@toks=\toks19
\enit@inbox=\box53
\enit@count@id=\count192
\enitdp@description=\count193
) (/usr/share/texmf-dist/tex/latex/ragged2e/ragged2e.sty
Package: ragged2e 2023/06/22 v3.6 ragged2e Package
\CenteringLeftskip=\skip53
\RaggedLeftLeftskip=\skip54
\RaggedRightLeftskip=\skip55
\CenteringRightskip=\skip56
\RaggedLeftRightskip=\skip57
\RaggedRightRightskip=\skip58
\CenteringParfillskip=\skip59
\RaggedLeftParfillskip=\skip60
\RaggedRightParfillskip=\skip61
\JustifyingParfillskip=\skip62
\CenteringParindent=\skip63
\RaggedLeftParindent=\skip64
\RaggedRightParindent=\skip65
\JustifyingParindent=\skip66
) (/usr/share/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks20
) (/usr/share/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count194
\Gm@cntv=\count195
\c@Gm@tempcnt=\count196
\Gm@bindingoffset=\dimen144
\Gm@wd@mp=\dimen145
\Gm@odd@mp=\dimen146
\Gm@even@mp=\dimen147
\Gm@layoutwidth=\dimen148
\Gm@layoutheight=\dimen149
\Gm@layouthoffset=\dimen150
\Gm@layoutvoffset=\dimen151
\Gm@dimlist=\toks21
) (/usr/share/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2022/11/09 v4.1 Extensive control of page headers and footers
\f@nch@headwidth=\skip67
\f@nch@O@elh=\skip68
\f@nch@O@erh=\skip69
\f@nch@O@olh=\skip70
\f@nch@O@orh=\skip71
\f@nch@O@elf=\skip72
\f@nch@O@erf=\skip73
\f@nch@O@olf=\skip74
\f@nch@O@orf=\skip75
) (/usr/share/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (/usr/share/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
) (/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (/usr/share/texmf-dist/tex/generic/iftex/ifxetex.sty
Package: ifxetex 2019/10/25 v0.7 ifxetex legacy package. Use iftex instead.
) (/usr/share/texmf-dist/tex/latex/xifthen/xifthen.sty
Package: xifthen 2015/11/05 v1.4.0 Extended ifthen features
 (/usr/share/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count197
\calc@Bcount=\count198
\calc@Adimen=\dimen152
\calc@Bdimen=\dimen153
\calc@Askip=\skip76
\calc@Bskip=\skip77
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count199
\calc@Cskip=\skip78
) (/usr/share/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
) (/usr/share/texmf-dist/tex/latex/ifmtarg/ifmtarg.sty
Package: ifmtarg 2018/04/16 v1.2b check for an empty argument
)) (/usr/share/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count266
) (/usr/share/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
) (/usr/share/texmf-dist/tex/latex/fontspec/fontspec.sty (/usr/share/texmf-dist/tex/latex/l3packages/xparse/xparse.sty (/usr/share/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (/usr/share/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count267
\l__pdf_internal_box=\box54
\g__pdf_backend_object_int=\count268
\g__pdf_backend_annotation_int=\count269
\g__pdf_backend_link_int=\count270
))
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: fontspec 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
 (/usr/share/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count271
\l__fontspec_language_int=\count272
\l__fontspec_strnum_int=\count273
\l__fontspec_tmp_int=\count274
\l__fontspec_tmpa_int=\count275
\l__fontspec_tmpb_int=\count276
\l__fontspec_tmpc_int=\count277
\l__fontspec_em_int=\count278
\l__fontspec_emdef_int=\count279
\l__fontspec_strong_int=\count280
\l__fontspec_strongdef_int=\count281
\l__fontspec_tmpa_dim=\dimen154
\l__fontspec_tmpb_dim=\dimen155
\l__fontspec_tmpc_dim=\dimen156
 (/usr/share/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (/usr/share/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (/usr/share/texmf-dist/tex/latex/unicode-math/unicode-math.sty
Package: unicode-math 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX
 (/usr/share/texmf-dist/tex/latex/unicode-math/unicode-math-xetex.sty
Package: unicode-math-xetex 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX
 (/usr/share/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2024-02-18 LaTeX2e option processing using LaTeX3 keys
) (/usr/share/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (/usr/share/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
)) (/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip79

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks22
\ex@=\dimen157
)) (/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen158
) (/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count282
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count283
\leftroot@=\count284
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count285
\DOTSCASE@=\count286
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box55
\strutbox@=\box56
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen159
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count287
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count288
\dotsspace@=\muskip16
\c@parentequation=\count289
\dspbrk@lvl=\count290
\tag@help=\toks23
\row@=\count291
\column@=\count292
\maxfields@=\count293
\andhelp@=\toks24
\eqnshift@=\dimen160
\alignsep@=\dimen161
\tagshift@=\dimen162
\tagwidth@=\dimen163
\totwidth@=\dimen164
\lineht@=\dimen165
\@envbody=\toks25
\multlinegap=\skip80
\multlinetaggap=\skip81
\mathdisplay@stack=\toks26
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
\g__um_fam_int=\count294
\g__um_fonts_used_int=\count295
\l__um_primecount_int=\count296
\g__um_primekern_muskip=\muskip17
 (/usr/share/texmf-dist/tex/latex/unicode-math/unicode-math-table.tex))) (/usr/share/texmf-dist/tex/latex/fontawesome/fontawesome.sty
Package: fontawesome 2016/05/15 v4.6.3.1 font awesome icons
 (/usr/share/texmf-dist/tex/generic/iftex/ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
) (/usr/share/texmf-dist/tex/latex/fontawesome/fontawesomesymbols-generic.tex) (/usr/share/texmf-dist/tex/latex/fontawesome/fontawesomesymbols-xeluatex.tex)) (/usr/share/texmf-dist/tex/latex/sourcesanspro/sourcesanspro.sty
Package: sourcesanspro 2018/05/19 v2.7 Adobe's Source Sans Pro typeface
 (/usr/share/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (/usr/share/texmf-dist/tex/generic/xkeyval/xkeyval.tex (/usr/share/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks27
\XKV@tempa@toks=\toks28
)
\XKV@depth=\count297
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/SourceSansPro(1)/m/n on input line 123.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/SourceSansPro(1)/bx/n on input line 123.
) (/usr/share/texmf-dist/tex/latex/tcolorbox/tcolorbox.sty
Package: tcolorbox 2024/01/10 version 6.2.0 text color boxes
 (/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks29
\pgfutil@tempdima=\dimen166
\pgfutil@tempdimb=\dimen167
) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box57
) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/share/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/share/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (/usr/share/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
)
\Gin@req@height=\dimen168
\Gin@req@width=\dimen169
) (/usr/share/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks30
\pgfkeys@temptoks=\toks31
 (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks32
))
\pgf@x=\dimen170
\pgf@y=\dimen171
\pgf@xa=\dimen172
\pgf@ya=\dimen173
\pgf@xb=\dimen174
\pgf@yb=\dimen175
\pgf@xc=\dimen176
\pgf@yc=\dimen177
\pgf@xd=\dimen178
\pgf@yd=\dimen179
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count298
\c@pgf@countb=\count299
\c@pgf@countc=\count300
\c@pgf@countd=\count301
\t@pgf@toka=\toks33
\t@pgf@tokb=\toks34
\t@pgf@tokc=\toks35
\pgf@sys@id@count=\count302
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-xetex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count303
))) (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count304
\pgfsyssoftpath@bigbuffer@items=\count305
) (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen180
\pgfmath@count=\count306
\pgfmath@box=\box58
\pgfmath@toks=\toks36
\pgfmath@stack@operand=\toks37
\pgfmath@stack@operation=\toks38
) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count307
)) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen181
\pgf@picmaxx=\dimen182
\pgf@picminy=\dimen183
\pgf@picmaxy=\dimen184
\pgf@pathminx=\dimen185
\pgf@pathmaxx=\dimen186
\pgf@pathminy=\dimen187
\pgf@pathmaxy=\dimen188
\pgf@xx=\dimen189
\pgf@xy=\dimen190
\pgf@yx=\dimen191
\pgf@yy=\dimen192
\pgf@zx=\dimen193
\pgf@zy=\dimen194
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen195
\pgf@path@lasty=\dimen196
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen197
\pgf@shorten@start@additional=\dimen198
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box59
\pgf@hbox=\box60
\pgf@layerbox@main=\box61
\pgf@picture@serial@count=\count308
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen199
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen256
\pgf@pt@y=\dimen257
\pgf@pt@temp=\dimen258
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen259
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen260
\pgf@sys@shading@range@num=\count309
\pgf@shadingcount=\count310
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box62
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box63
) (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen261
\pgf@nodesepend=\dimen262
) (/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2023-11-06 v1.5v LaTeX2e package for verbatim enhancements
\every@verbatim=\toks39
\verbatim@line=\toks40
\verbatim@in@stream=\read3
) (/usr/share/texmf-dist/tex/latex/environ/environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments
 (/usr/share/texmf-dist/tex/latex/trimspaces/trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))
\tcb@titlebox=\box64
\tcb@upperbox=\box65
\tcb@lowerbox=\box66
\tcb@phantombox=\box67
\c@tcbbreakpart=\count311
\c@tcblayer=\count312
\c@tcolorbox@number=\count313
\l__tcobox_tmpa_box=\box68
\l__tcobox_tmpa_dim=\dimen263
\tcb@temp=\box69
\tcb@temp=\box70
\tcb@temp=\box71
\tcb@temp=\box72
 (/usr/share/texmf-dist/tex/latex/tcolorbox/tcbskins.code.tex
Library (tcolorbox): 'tcbskins.code.tex' version '6.2.0'
(/usr/share/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/usr/share/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen264
\pgffor@skip=\dimen265
\pgffor@stack=\toks41
\pgffor@toks=\toks42
)) (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count314
\pgfplotmarksize=\dimen266
)
\tikz@lastx=\dimen267
\tikz@lasty=\dimen268
\tikz@lastxsaved=\dimen269
\tikz@lastysaved=\dimen270
\tikz@lastmovetox=\dimen271
\tikz@lastmovetoy=\dimen272
\tikzleveldistance=\dimen273
\tikzsiblingdistance=\dimen274
\tikz@figbox=\box73
\tikz@figbox@bg=\box74
\tikz@tempbox=\box75
\tikz@tempbox@bg=\box76
\tikztreelevel=\count315
\tikznumberofchildren=\count316
\tikznumberofcurrentchild=\count317
\tikz@fig@count=\count318
 (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count319
\pgfmatrixcurrentcolumn=\count320
\pgf@matrix@numberofcolumns=\count321
)
\tikz@expandcount=\count322
 (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/tikzfill/tikzfill.image.sty
Package: tikzfill.image 2023/08/08 v1.0.1 Image filling library for TikZ
 (/usr/share/texmf-dist/tex/latex/tikzfill/tikzfill-common.sty
Package: tikzfill-common 2023/08/08 v1.0.1 Auxiliary code for tikzfill
) (/usr/share/texmf-dist/tex/latex/tikzfill/tikzlibraryfill.image.code.tex
File: tikzlibraryfill.image.code.tex 2023/08/08 v1.0.1 Image filling library
\l__tikzfill_img_box=\box77
)) (/usr/share/texmf-dist/tex/latex/tcolorbox/tcbskinsjigsaw.code.tex
Library (tcolorbox): 'tcbskinsjigsaw.code.tex' version '6.2.0'
))) (/usr/share/texmf-dist/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments
 (/usr/share/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
 (/usr/share/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/usr/share/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))) (/usr/share/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX
 (/usr/share/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/share/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (/usr/share/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/share/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (/usr/share/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/share/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (/usr/share/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (/usr/share/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/share/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count323
)
\@linkdim=\dimen275
\Hy@linkcounter=\count324
\Hy@pagecounter=\count325
 (/usr/share/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
) (/usr/share/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count326
 (/usr/share/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4062.
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count327
 (/usr/share/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen276
 (/usr/share/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/share/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count328
\Field@Width=\dimen277
\Fld@charsize=\dimen278
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring OFF on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.
 (/usr/share/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count329
\c@Item=\count330
\c@Hfootnote=\count331
)
Package hyperref Info: Driver (autodetected): hxetex.
 (/usr/share/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-01-20 v7.01h Hyperref driver for XeTeX
 (/usr/share/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box78
\c@Hy@AnnotLevel=\count332
\HyField@AnnotCount=\count333
\Fld@listcount=\count334
\c@bookmark@seq@number=\count335
 (/usr/share/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (/usr/share/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (/usr/share/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip82
))
LaTeX Font Info:    Font shape `TU/SourceSansPro(0)/m/n' will be
(Font)              scaled to size 10.95pt on input line 100.
 (./coverletter.aux)
\openout1 = `coverletter.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 100.
LaTeX Font Info:    ... okay on input line 100.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 100.
LaTeX Font Info:    ... okay on input line 100.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 100.
LaTeX Font Info:    ... okay on input line 100.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 100.
LaTeX Font Info:    ... okay on input line 100.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 100.
LaTeX Font Info:    ... okay on input line 100.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 100.
LaTeX Font Info:    ... okay on input line 100.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 100.
LaTeX Font Info:    ... okay on input line 100.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 100.
LaTeX Font Info:    ... okay on input line 100.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 100.
LaTeX Font Info:    ... okay on input line 100.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 100.
LaTeX Font Info:    ... okay on input line 100.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(39.83368pt, 517.84052pt, 39.83368pt)
* v-part:(T,H,B)=(22.76228pt, 771.06952pt, 51.21504pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=517.84052pt
* \textheight=771.06952pt
* \oddsidemargin=-32.43631pt
* \evensidemargin=-32.43631pt
* \topmargin=-86.5077pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=14.22636pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 100.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 100.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/bx/n on input line 100.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 100.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/bx/n on input line 100.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 100.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/latinmodern-math.otf(1)/m/n on input line 100.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 100.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/latinmodern-math.otf(1)/b/n on input line 100.
LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `normal' on input line 100.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> TU/latinmodern-math.otf(2)/m/n on input line 100.
LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `bold' on input line 100.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> TU/latinmodern-math.otf(2)/b/n on input line 100.
LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 100.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> TU/latinmodern-math.otf(3)/m/n on input line 100.
LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 100.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> TU/latinmodern-math.otf(3)/b/n on input line 100.
Package hyperref Info: Link coloring OFF on input line 100.
(./coverletter.out) (./coverletter.out)
\@outlinefile=\write4
\openout4 = `coverletter.out'.

\headertextwidth=\skip83
\headerphotowidth=\skip84
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 10.95116pt on input line 104.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 7.6658pt on input line 104.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 5.47559pt on input line 104.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 10.94882pt on input line 104.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 7.66417pt on input line 104.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 5.47441pt on input line 104.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 7.6pt on input line 104.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/sc' will be
(Font)              scaled to size 7.6pt on input line 104.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 9.0pt on input line 104.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/it' will be
(Font)              scaled to size 9.0pt on input line 104.

Overfull \hbox (1.46pt too wide) in paragraph at lines 104--105
[]$[]$ $[]$$[]$
 []


Underfull \hbox (badness 10000) in paragraph at lines 104--105

 []

LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 11.0pt on input line 114.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/b/n' will be
(Font)              scaled to size 11.0pt on input line 114.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/n' will be
(Font)              scaled to size 9.0pt on input line 114.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/sl' in size <9> not available
(Font)              Font shape `TU/SourceSansPro(2)/m/it' tried instead on input line 114.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/it' will be
(Font)              scaled to size 9.0pt on input line 114.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/sc' will be
(Font)              scaled to size 9.0pt on input line 114.

Underfull \hbox (badness 10000) in paragraph at lines 114--114

 []


Underfull \hbox (badness 10000) in paragraph at lines 114--114

 []

LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/n' will be
(Font)              scaled to size 10.0pt on input line 114.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/b/n' will be
(Font)              scaled to size 10.0pt on input line 114.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 10.00107pt on input line 114.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 7.00075pt on input line 114.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 5.00053pt on input line 114.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 9.99893pt on input line 114.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 6.99925pt on input line 114.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 4.99947pt on input line 114.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 14.0pt on input line 121.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/b/n' will be
(Font)              scaled to size 14.0pt on input line 121.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 10.0pt on input line 135.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/b/n' will be
(Font)              scaled to size 10.0pt on input line 135.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/sl' in size <10> not available
(Font)              Font shape `TU/SourceSansPro(2)/m/it' tried instead on input line 135.
LaTeX Font Info:    Font shape `TU/SourceSansPro(2)/m/it' will be
(Font)              scaled to size 10.0pt on input line 135.

Underfull \hbox (badness 10000) in paragraph at lines 135--136

 []


Underfull \hbox (badness 10000) in paragraph at lines 135--136

 []


Underfull \hbox (badness 10000) in paragraph at lines 135--136

 []


Underfull \hbox (badness 10000) in paragraph at lines 135--136

 []

LaTeX Font Info:    Font shape `TU/SourceSansPro(0)/m/n' will be
(Font)              scaled to size 10.0pt on input line 137.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/n' will be
(Font)              scaled to size 8.0pt on input line 137.
LaTeX Font Info:    Font shape `TU/SourceSansPro(1)/m/sc' will be
(Font)              scaled to size 8.0pt on input line 137.
[1

] (./coverletter.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********
Package rerunfilecheck Info: File `coverletter.out' has not changed.
(rerunfilecheck)             Checksum: D41D8CD98F00B204E9800998ECF8427E;0.
 ) 
Here is how much of TeX's memory you used:
 32466 strings out of 476772
 656955 string characters out of 5803394
 1934839 words of memory out of 5000000
 53947 multiletter control sequences out of 15000+600000
 561362 words of font info for 123 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 116i,11n,121p,768b,330s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on coverletter.pdf (1 page).
