%-------------------------------------------------------------------------------
%	SECTION TITLE
%-------------------------------------------------------------------------------
\cvsection{Work Experience}


%-------------------------------------------------------------------------------
%	CONTENT
%-------------------------------------------------------------------------------
\begin{cventries}

%---------------------------------------------------------
  \cventry
    {Software Architect} % Job title
    {Omnious. Co., Ltd.} % Organization
    {Seoul, S.Korea} % Location
    {Jun. 2017 - May. 2018} % Date(s)
    {
      \begin{cvitems} % Description(s) of tasks/responsibilities
        \item {Provisioned an easily managable hybrid infrastructure(Amazon AWS + On-premise) utilizing IaC(Infrastructure as Code) tools like Ansible, Packer and Terraform.}
        \item {Built fully automated CI/CD pipelines on CircleCI for containerized applications using Docker, AWS ECR and Rancher.}
        \item {Designed an overall service architecture and pipelines of the Machine Learning based Fashion Tagging API SaaS product with the micro-services architecture.}
        \item {Implemented several API microservices in Node.js Koa and in the serverless AWS Lambda functions.}
        \item {Deployed a centralized logging environment(ELK, Filebeat, CloudWatch, S3) which gather log data from docker containers and AWS resources.}
        \item {Deployed a centralized monitoring environment(Grafana, InfluxDB, CollectD) which gather system metrics as well as docker run-time metrics.}
      \end{cvitems}
    }

%---------------------------------------------------------
  \cventry
    {Co-founder \& Software Engineer} % Job title
    {PLAT Corp.} % Organization
    {Seoul, S.Korea} % Location
    {Jan. 2016 - Jun. 2017} % Date(s)
    {
      \begin{cvitems} % Description(s) of tasks/responsibilities
        \item {Implemented RESTful API server for car rental booking application(CARPLAT in Google Play).}
        \item {Built and deployed overall service infrastructure utilizing Docker container, CircleCI, and several AWS stack(Including EC2, ECS, Route 53, S3, CloudFront, RDS, ElastiCache, IAM), focusing on high-availability, fault tolerance, and auto-scaling.}
        \item {Developed an easy-to-use Payment module which connects to major PG(Payment Gateway) companies in Korea.}
      \end{cvitems}
    }

%---------------------------------------------------------
  \cventry
    {Software Engineer \& Security Researcher (Compulsory Military Service)} % Job title
    {R.O.K Cyber Command, MND} % Organization
    {Seoul, S.Korea} % Location
    {Aug. 2014 - Apr. 2016} % Date(s)
    {
      \begin{cvitems} % Description(s) of tasks/responsibilities
        \item {Lead engineer on agent-less backtracking system that can discover client device's fingerprint(including public and private IP) independently of the Proxy, VPN and NAT.}
        \item {Implemented a distributed web stress test tool with high anonymity.}
        \item {Implemented a military cooperation system which is web based real time messenger in Scala on Lift.}
      \end{cvitems}
    }

%---------------------------------------------------------
  \cventry
    {Game Developer Intern at Global Internship Program} % Job title
    {NEXON} % Organization
    {Seoul, S.Korea \& LA, U.S.A} % Location
    {Jan. 2013 - Feb. 2013} % Date(s)
    {
      \begin{cvitems} % Description(s) of tasks/responsibilities
        \item {Developed in Cocos2d-x an action puzzle game(Dragon Buster) targeting U.S. market.}
        \item {Implemented API server which is communicating with game client and In-App Store, along with two other team members who wrote the game logic and designed game graphics.}
        \item {Won the 2nd prize in final evaluation.}
      \end{cvitems}
    }

%---------------------------------------------------------
  \cventry
    {Software Engineer} % Job title
    {ShitOne Corp.} % Organization
    {Seoul, S.Korea} % Location
    {Dec. 2011 - Feb. 2012} % Date(s)
    {
      \begin{cvitems} % Description(s) of tasks/responsibilities
        \item {Developed a proxy drive smartphone application which connects proxy driver and customer.}
        \item {Implemented overall Android application logic and wrote API server for community service, along with lead engineer who designed bidding protocol on raw socket and implemented API server for bidding.}
      \end{cvitems}
    }

%---------------------------------------------------------
  \cventry
    {Freelance Penetration Tester} % Job title
    {SAMSUNG Electronics} % Organization
    {S.Korea} % Location
    {Sep. 2013, Mar. 2011 - Oct. 2011} % Date(s)
    {
      \begin{cvitems} % Description(s) of tasks/responsibilities
        \item {Conducted penetration testing on SAMSUNG KNOX, which is solution for enterprise mobile security.}
        \item {Conducted penetration testing on SAMSUNG Smart TV.}
      \end{cvitems}
      %\begin{cvsubentries}
      %  \cvsubentry{}{KNOX(Solution for Enterprise Mobile Security) Penetration Testing}{Sep. 2013}{}
      %  \cvsubentry{}{Smart TV Penetration Testing}{Mar. 2011 - Oct. 2011}{}
      %\end{cvsubentries}
    }

%---------------------------------------------------------
\end{cventries}
