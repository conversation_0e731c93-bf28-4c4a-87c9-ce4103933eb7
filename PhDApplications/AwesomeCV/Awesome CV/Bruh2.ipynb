{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["An error occurred or no results were found.\n"]}], "source": ["import requests\n", "\n", "class PerplexityAI:\n", "    def __init__(self, api_key):\n", "        self.api_key = api_key\n", "        self.base_url = 'https://api.perplexity.ai'\n", "\n", "    def search_professors(self, school, field, interests):\n", "        endpoint = f'{self.base_url}/chat/completions'\n", "        headers = {'Authorization': f'Bearer {self.api_key}', 'Content-Type': 'application/json'}\n", "        prompt = f\"Find professors at {school} who specialize in {field}.\\n\\nTheir research interests should be related to: {interests}.\"\n", "        data = {\n", "            'model': 'llama-3-sonar-large-32k-online',\n", "            'messages': [{'role': 'user', 'content': prompt}]\n", "        }\n", "        response = requests.post(endpoint, headers=headers, json=data)\n", "        return response.json()\n", "\n", "def main():\n", "    api_key = input(\"Enter your Perplexity AI API key: \")\n", "    perplexity_ai = PerplexityAI(api_key)\n", "\n", "    school = input(\"Enter the name of the graduate school: \")\n", "    field = input(\"Enter the field of research: \")\n", "    interests = input(\"Enter your current interests and experience: \")\n", "\n", "    result = perplexity_ai.search_professors(school, field, interests)\n", "    \n", "    if 'choices' in result and len(result['choices']) > 0:\n", "        print(\"\\nHere are the professors that match your criteria:\")\n", "        print(result['choices'][0]['message']['content'])\n", "    else:\n", "        print(\"An error occurred or no results were found.\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["myapikey = '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'"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"ename": "UnicodeDecodeError", "evalue": "'utf-8' codec can't decode byte 0xe4 in position 10: invalid continuation byte", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mUnicodeDecodeError\u001b[0m                        Traceback (most recent call last)", "Cell \u001b[0;32mIn[9], line 50\u001b[0m\n\u001b[1;32m     47\u001b[0m             \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m---\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     49\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 50\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[9], line 34\u001b[0m, in \u001b[0;36mmain\u001b[0;34m()\u001b[0m\n\u001b[1;32m     31\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mmain\u001b[39m():\n\u001b[1;32m     32\u001b[0m     grad_school, research_field, cv_path, statement_path, email_example_path \u001b[38;5;241m=\u001b[39m get_user_input()\n\u001b[0;32m---> 34\u001b[0m     cv_content \u001b[38;5;241m=\u001b[39m \u001b[43mread_file\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcv_path\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     35\u001b[0m     statement_content \u001b[38;5;241m=\u001b[39m read_file(statement_path)\n\u001b[1;32m     36\u001b[0m     email_example \u001b[38;5;241m=\u001b[39m read_file(email_example_path)\n", "Cell \u001b[0;32mIn[9], line 16\u001b[0m, in \u001b[0;36mread_file\u001b[0;34m(file_path)\u001b[0m\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m file_path \u001b[38;5;129;01mand\u001b[39;00m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mexists(file_path):\n\u001b[1;32m     15\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(file_path, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m file:\n\u001b[0;32m---> 16\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mfile\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "File \u001b[0;32m<frozen codecs>:322\u001b[0m, in \u001b[0;36mdecode\u001b[0;34m(self, input, final)\u001b[0m\n", "\u001b[0;31mUnicodeDecodeError\u001b[0m: 'utf-8' codec can't decode byte 0xe4 in position 10: invalid continuation byte"]}], "source": ["import perplexityai\n", "import os\n", "\n", "def get_user_input():\n", "    grad_school = input(\"Enter the name of the graduate school: \")\n", "    research_field = input(\"Enter your field of research: \")\n", "    cv_path = input(\"Enter the path to your CV file (or press Enter to skip): \")\n", "    statement_path = input(\"Enter the path to your personal statement file (or press Enter to skip): \")\n", "    email_example_path = input(\"Enter the path to an email example file (or press Enter to skip): \")\n", "    \n", "    return grad_school, research_field, cv_path, statement_path, email_example_path\n", "\n", "def read_file(file_path):\n", "    if file_path and os.path.exists(file_path):\n", "        with open(file_path, 'r') as file:\n", "            return file.read()\n", "    return \"\"\n", "\n", "def research_professors(grad_school, research_field, cv_content, statement_content):\n", "    query = f\"Research professors at {grad_school} doing research in {research_field}. Consider my background from this CV: {cv_content} and personal statement: {statement_content}. Provide a list of 3-5 professors whose research aligns with my interests.\"\n", "    \n", "    response = perplexity.query(query)\n", "    return response\n", "\n", "def generate_email(professor, grad_school, research_field, cv_content, statement_content, email_example):\n", "    query = f\"Write an email to Professor {professor} at {grad_school} inquiring about positions in their lab for the following fall. The email should mention my interest in {research_field} and incorporate relevant details from my CV: {cv_content} and personal statement: {statement_content}. Use this email example as a guide for tone and structure: {email_example}\"\n", "    \n", "    response = perplexity.query(query)\n", "    return response\n", "\n", "def main():\n", "    grad_school, research_field, cv_path, statement_path, email_example_path = get_user_input()\n", "    \n", "    cv_content = read_file(cv_path)\n", "    statement_content = read_file(statement_path)\n", "    email_example = read_file(email_example_path)\n", "    \n", "    professors = research_professors(grad_school, research_field, cv_content, statement_content)\n", "    print(\"\\nPotential professors for your research interests:\")\n", "    print(professors)\n", "    \n", "    for professor in professors.split('\\n'):\n", "        if professor:\n", "            email = generate_email(professor, grad_school, research_field, cv_content, statement_content, email_example)\n", "            print(f\"\\nEmail for {professor}:\")\n", "            print(email)\n", "            print(\"\\n---\\n\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting claude\n", "  Downloading claude-0.4.10.tar.gz (1.4 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m10.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25ldone\n", "\u001b[?25h  Getting requirements to build wheel ... \u001b[?25ldone\n", "\u001b[?25h  Preparing metadata (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25hCollecting aiohttp~=3.5 (from claude)\n", "  Downloading aiohttp-3.10.10-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (7.6 kB)\n", "Collecting Jinja2~=2.10 (from claude)\n", "  Downloading Jinja2-2.11.3-py2.py3-none-any.whl.metadata (3.5 kB)\n", "Collecting configpp~=0.3 (from claude)\n", "  Downloading configpp-0.5.0.tar.gz (354 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m354.4/354.4 kB\u001b[0m \u001b[31m18.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25ldone\n", "\u001b[?25h  Getting requirements to build wheel ... \u001b[?25ldone\n", "\u001b[?25h  Preparing metadata (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25hCollecting lxml~=4.4 (from claude)\n", "  Downloading lxml-4.9.4-cp312-cp312-manylinux_2_28_x86_64.whl.metadata (3.7 kB)\n", "Collecting pymemcache~=2.2 (from claude)\n", "  Downloading pymemcache-2.2.2-py2.py3-none-any.whl.metadata (10 kB)\n", "Collecting cssselect~=1.1 (from claude)\n", "  Downloading cssselect-1.2.0-py2.py3-none-any.whl.metadata (2.2 kB)\n", "Collecting cachetools~=3.1 (from claude)\n", "  Downloading cachetools-3.1.1-py2.py3-none-any.whl.metadata (4.6 kB)\n", "Collecting pychromecast~=3.2 (from claude)\n", "  Downloading PyChromecast-3.2.3-py2.py3-none-any.whl.metadata (7.6 kB)\n", "Collecting aiohttp-middlewares~=1.1 (from claude)\n", "  Downloading aiohttp_middlewares-1.2.1-py3-none-any.whl.metadata (4.4 kB)\n", "Collecting aiohappyeyeballs>=2.3.0 (from aiohttp~=3.5->claude)\n", "  Downloading aiohappyeyeballs-2.4.3-py3-none-any.whl.metadata (6.1 kB)\n", "Collecting aiosignal>=1.1.2 (from aiohttp~=3.5->claude)\n", "  Downloading aiosignal-1.3.1-py3-none-any.whl.metadata (4.0 kB)\n", "Requirement already satisfied: attrs>=17.3.0 in ./miniconda3/envs/mainenv/lib/python3.12/site-packages (from aiohttp~=3.5->claude) (23.2.0)\n", "Collecting frozenlist>=1.1.1 (from aiohttp~=3.5->claude)\n", "  Downloading frozenlist-1.4.1-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (12 kB)\n", "Collecting multidict<7.0,>=4.5 (from aiohttp~=3.5->claude)\n", "  Downloading multidict-6.1.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.0 kB)\n", "Collecting yarl<2.0,>=1.12.0 (from aiohttp~=3.5->claude)\n", "  Downloading yarl-1.15.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (54 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m54.3/54.3 kB\u001b[0m \u001b[31m6.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting async-timeout<5.0,>=3.0 (from aiohttp-middlewares~=1.1->claude)\n", "  Downloading async_timeout-4.0.3-py3-none-any.whl.metadata (4.2 kB)\n", "Collecting voluptuous~=0.11 (from configpp~=0.3->claude)\n", "  Downloading voluptuous-0.15.2-py3-none-any.whl.metadata (20 kB)\n", "Collecting command-tree~=0.7 (from configpp~=0.3->claude)\n", "  Downloading command-tree-0.7.1.tar.gz (15 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25ldone\n", "\u001b[?25hCollecting python-slugify~=1.2 (from configpp~=0.3->claude)\n", "  Downloading python-slugify-1.2.6.tar.gz (6.8 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25ldone\n", "\u001b[?25hRequirement already satisfied: python-dateutil~=2.7 in ./miniconda3/envs/mainenv/lib/python3.12/site-packages (from configpp~=0.3->claude) (2.8.2)\n", "Collecting ruamel.yaml~=0.15.44 (from configpp~=0.3->claude)\n", "  Downloading ruamel.yaml-0.15.100.tar.gz (318 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m318.6/318.6 kB\u001b[0m \u001b[31m20.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25ldone\n", "\u001b[?25hCollecting typing-inspect~=0.3 (from configpp~=0.3->claude)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting PyYAML~=5.1 (from configpp~=0.3->claude)\n", "  Downloading PyYAML-5.4.1.tar.gz (175 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m175.1/175.1 kB\u001b[0m \u001b[31m13.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25ldone\n", "\u001b[?25h  Getting requirements to build wheel ... \u001b[?25lerror\n", "  \u001b[1;31merror\u001b[0m: \u001b[1msubprocess-exited-with-error\u001b[0m\n", "  \n", "  \u001b[31m×\u001b[0m \u001b[32mGetting requirements to build wheel\u001b[0m did not run successfully.\n", "  \u001b[31m│\u001b[0m exit code: \u001b[1;36m1\u001b[0m\n", "  \u001b[31m╰─>\u001b[0m \u001b[31m[54 lines of output]\u001b[0m\n", "  \u001b[31m   \u001b[0m running egg_info\n", "  \u001b[31m   \u001b[0m writing lib3/PyYAML.egg-info/PKG-INFO\n", "  \u001b[31m   \u001b[0m writing dependency_links to lib3/PyYAML.egg-info/dependency_links.txt\n", "  \u001b[31m   \u001b[0m writing top-level names to lib3/PyYAML.egg-info/top_level.txt\n", "  \u001b[31m   \u001b[0m <PERSON><PERSON> (most recent call last):\n", "  \u001b[31m   \u001b[0m   File \"/home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/pip/_vendor/pyproject_hooks/_in_process/_in_process.py\", line 353, in <module>\n", "  \u001b[31m   \u001b[0m     main()\n", "  \u001b[31m   \u001b[0m   File \"/home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/pip/_vendor/pyproject_hooks/_in_process/_in_process.py\", line 335, in main\n", "  \u001b[31m   \u001b[0m     json_out['return_val'] = hook(**hook_input['kwargs'])\n", "  \u001b[31m   \u001b[0m                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  \u001b[31m   \u001b[0m   File \"/home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/pip/_vendor/pyproject_hooks/_in_process/_in_process.py\", line 118, in get_requires_for_build_wheel\n", "  \u001b[31m   \u001b[0m     return hook(config_settings)\n", "  \u001b[31m   \u001b[0m            ^^^^^^^^^^^^^^^^^^^^^\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/build_meta.py\", line 332, in get_requires_for_build_wheel\n", "  \u001b[31m   \u001b[0m     return self._get_build_requires(config_settings, requirements=[])\n", "  \u001b[31m   \u001b[0m            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/build_meta.py\", line 302, in _get_build_requires\n", "  \u001b[31m   \u001b[0m     self.run_setup()\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/build_meta.py\", line 318, in run_setup\n", "  \u001b[31m   \u001b[0m     exec(code, locals())\n", "  \u001b[31m   \u001b[0m   File \"<string>\", line 271, in <module>\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/__init__.py\", line 117, in setup\n", "  \u001b[31m   \u001b[0m     return distutils.core.setup(**attrs)\n", "  \u001b[31m   \u001b[0m            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/_distutils/core.py\", line 183, in setup\n", "  \u001b[31m   \u001b[0m     return run_commands(dist)\n", "  \u001b[31m   \u001b[0m            ^^^^^^^^^^^^^^^^^^\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/_distutils/core.py\", line 199, in run_commands\n", "  \u001b[31m   \u001b[0m     dist.run_commands()\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/_distutils/dist.py\", line 954, in run_commands\n", "  \u001b[31m   \u001b[0m     self.run_command(cmd)\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/dist.py\", line 950, in run_command\n", "  \u001b[31m   \u001b[0m     super().run_command(command)\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/_distutils/dist.py\", line 973, in run_command\n", "  \u001b[31m   \u001b[0m     cmd_obj.run()\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/command/egg_info.py\", line 311, in run\n", "  \u001b[31m   \u001b[0m     self.find_sources()\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/command/egg_info.py\", line 319, in find_sources\n", "  \u001b[31m   \u001b[0m     mm.run()\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/command/egg_info.py\", line 540, in run\n", "  \u001b[31m   \u001b[0m     self.add_defaults()\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/command/egg_info.py\", line 578, in add_defaults\n", "  \u001b[31m   \u001b[0m     sdist.add_defaults(self)\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/command/sdist.py\", line 108, in add_defaults\n", "  \u001b[31m   \u001b[0m     super().add_defaults()\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/_distutils/command/sdist.py\", line 238, in add_defaults\n", "  \u001b[31m   \u001b[0m     self._add_defaults_ext()\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/_distutils/command/sdist.py\", line 323, in _add_defaults_ext\n", "  \u001b[31m   \u001b[0m     self.filelist.extend(build_ext.get_source_files())\n", "  \u001b[31m   \u001b[0m                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  \u001b[31m   \u001b[0m   File \"<string>\", line 201, in get_source_files\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-build-env-1bspd937/overlay/lib/python3.12/site-packages/setuptools/_distutils/cmd.py\", line 107, in __getattr__\n", "  \u001b[31m   \u001b[0m     raise AttributeError(attr)\n", "  \u001b[31m   \u001b[0m AttributeError: cython_sources\n", "  \u001b[31m   \u001b[0m \u001b[31m[end of output]\u001b[0m\n", "  \n", "  \u001b[1;35mnote\u001b[0m: This error originates from a subprocess, and is likely not a problem with pip.\n", "\u001b[?25h\u001b[1;31merror\u001b[0m: \u001b[1msubprocess-exited-with-error\u001b[0m\n", "\n", "\u001b[31m×\u001b[0m \u001b[32mGetting requirements to build wheel\u001b[0m did not run successfully.\n", "\u001b[31m│\u001b[0m exit code: \u001b[1;36m1\u001b[0m\n", "\u001b[31m╰─>\u001b[0m See above for output.\n", "\n", "\u001b[1;35mnote\u001b[0m: This error originates from a subprocess, and is likely not a problem with pip.\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install claude"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"ename": "APIRemovedInV1", "evalue": "\n\nYou tried to access openai.Completion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAPIRemovedInV1\u001b[0m                            Traceback (most recent call last)", "Cell \u001b[0;32mIn[14], line 86\u001b[0m\n\u001b[1;32m     83\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m50\u001b[39m \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     85\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 86\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[14], line 74\u001b[0m, in \u001b[0;36mmain\u001b[0;34m()\u001b[0m\n\u001b[1;32m     60\u001b[0m     email_template \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m     61\u001b[0m \u001b[38;5;124m    Dear Professor [Name],\u001b[39m\n\u001b[1;32m     62\u001b[0m \u001b[38;5;124m    \u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     70\u001b[0m \u001b[38;5;124m    [Your Name]\u001b[39m\n\u001b[1;32m     71\u001b[0m \u001b[38;5;124m    \u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m     73\u001b[0m cv_text \u001b[38;5;241m=\u001b[39m extract_text_from_pdf(cv_path)\n\u001b[0;32m---> 74\u001b[0m cv_summary \u001b[38;5;241m=\u001b[39m \u001b[43manalyze_cv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcv_text\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     76\u001b[0m professors \u001b[38;5;241m=\u001b[39m research_professors(school, field)\n\u001b[1;32m     77\u001b[0m professor_list \u001b[38;5;241m=\u001b[39m re\u001b[38;5;241m.\u001b[39msplit(\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124md+\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m'\u001b[39m, professors)[\u001b[38;5;241m1\u001b[39m:]  \u001b[38;5;66;03m# Split by numbered list items\u001b[39;00m\n", "Cell \u001b[0;32mIn[14], line 29\u001b[0m, in \u001b[0;36manalyze_cv\u001b[0;34m(cv_text)\u001b[0m\n\u001b[1;32m     27\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21manalyze_cv\u001b[39m(cv_text):\n\u001b[1;32m     28\u001b[0m     prompt \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAnalyze this CV and summarize the key research experiences and skills:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mcv_text\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m---> 29\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mopenai\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mCompletion\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     30\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmodel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mperplexity/pplx-7b-online\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     31\u001b[0m \u001b[43m        \u001b[49m\u001b[43mprompt\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mprompt\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     32\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m500\u001b[39;49m\n\u001b[1;32m     33\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     34\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response\u001b[38;5;241m.\u001b[39mchoices[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mtext\u001b[38;5;241m.\u001b[39mstrip()\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/openai/lib/_old_api.py:39\u001b[0m, in \u001b[0;36mAPIRemovedInV1Proxy.__call__\u001b[0;34m(self, *_args, **_kwargs)\u001b[0m\n\u001b[1;32m     38\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__call__\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39m_args: Any, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m_kwargs: Any) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Any:\n\u001b[0;32m---> 39\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m APIRemovedInV1(symbol\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_symbol)\n", "\u001b[0;31mAPIRemovedInV1\u001b[0m: \n\nYou tried to access openai.Completion, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"]}], "source": ["import os\n", "import openai\n", "import PyPDF2\n", "import re\n", "\n", "# Set up Perplexity AI API\n", "openai.api_key = '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'\n", "openai.api_base = \"https://api.perplexity.ai\"\n", "\n", "def extract_text_from_pdf(file_path):\n", "    with open(file_path, 'rb') as file:\n", "        reader = PyPDF2.PdfReader(file)\n", "        text = \"\"\n", "        for page in reader.pages:\n", "            text += page.extract_text()\n", "    return text\n", "\n", "def research_professors(school, field):\n", "    prompt = f\"Find professors at {school} doing research in {field}. Include their names, research interests, and recent publications.\"\n", "    response = openai.Completion.create(\n", "        model=\"perplexity/pplx-7b-online\",\n", "        prompt=prompt,\n", "        max_tokens=1000\n", "    )\n", "    return response.choices[0].text.strip()\n", "\n", "def analyze_cv(cv_text):\n", "    prompt = f\"Analyze this CV and summarize the key research experiences and skills:\\n\\n{cv_text}\"\n", "    response = openai.Completion.create(\n", "        model=\"perplexity/pplx-7b-online\",\n", "        prompt=prompt,\n", "        max_tokens=500\n", "    )\n", "    return response.choices[0].text.strip()\n", "\n", "def generate_email(professor_info, cv_summary, email_template):\n", "    prompt = f\"\"\"\n", "    Generate an email to a professor inquiring about research positions based on this information:\n", "    \n", "    Professor: {professor_info}\n", "    Your CV summary: {cv_summary}\n", "    Email template: {email_template}\n", "    \n", "    The email should be personalized, mentioning specific aspects of the professor's research that align with your experience.\n", "    \"\"\"\n", "    response = openai.Completion.create(\n", "        model=\"perplexity/pplx-7b-online\",\n", "        prompt=prompt,\n", "        max_tokens=500\n", "    )\n", "    return response.choices[0].text.strip()\n", "\n", "def main():\n", "    school = input(\"Enter the graduate school name: \")\n", "    field = input(\"Enter your field of research: \")\n", "    cv_path = input(\"Enter the path to your CV PDF file: \")\n", "    email_template = input(\"Enter an example email or press Enter to use a default template: \")\n", "    \n", "    if not email_template:\n", "        email_template = \"\"\"\n", "        Dear Professor [Name],\n", "        \n", "        I am writing to inquire about potential research opportunities in your lab for the upcoming fall semester. [Your research interests] align closely with my background in [your background].\n", "        \n", "        [Mention specific aspects of their research and how it relates to your experience]\n", "        \n", "        I would be grateful for the opportunity to discuss any potential positions or projects within your lab. Thank you for your time and consideration.\n", "        \n", "        <PERSON><PERSON><PERSON>,\n", "        [Your Name]\n", "        \"\"\"\n", "    \n", "    cv_text = extract_text_from_pdf(cv_path)\n", "    cv_summary = analyze_cv(cv_text)\n", "    \n", "    professors = research_professors(school, field)\n", "    professor_list = re.split(r'\\d+\\.', professors)[1:]  # Split by numbered list items\n", "    \n", "    for professor in professor_list:\n", "        email = generate_email(professor, cv_summary, email_template)\n", "        print(f\"\\nEmail for {professor.split(',')[0]}:\\n\")\n", "        print(email)\n", "        print(\"\\n\" + \"=\"*50 + \"\\n\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'choices'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[19], line 58\u001b[0m\n\u001b[1;32m     55\u001b[0m     \u001b[38;5;28<PERSON>rint\u001b[39m(email)\n\u001b[1;32m     57\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 58\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[19], line 47\u001b[0m, in \u001b[0;36mmain\u001b[0;34m()\u001b[0m\n\u001b[1;32m     43\u001b[0m email_example \u001b[38;5;241m=\u001b[39m \u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEnter an example email: \u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     45\u001b[0m cv_content \u001b[38;5;241m=\u001b[39m extract_text_from_pdf(cv_path)\n\u001b[0;32m---> 47\u001b[0m professors \u001b[38;5;241m=\u001b[39m \u001b[43mresearch_professors\u001b[49m\u001b[43m(\u001b[49m\u001b[43mschool\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfield\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     48\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mProfessors found:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     49\u001b[0m \u001b[38;5;28mprint\u001b[39m(professors)\n", "Cell \u001b[0;32mIn[19], line 25\u001b[0m, in \u001b[0;36mresearch_professors\u001b[0;34m(school, field)\u001b[0m\n\u001b[1;32m     23\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mresearch_professors\u001b[39m(school, field):\n\u001b[1;32m     24\u001b[0m     prompt \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFind professors at \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mschool\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m doing research in \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfield\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m. Provide their names, research interests, and recent publications.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m---> 25\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mget_perplexity_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprompt\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[19], line 21\u001b[0m, in \u001b[0;36mget_perplexity_response\u001b[0;34m(prompt)\u001b[0m\n\u001b[1;32m     16\u001b[0m data \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m     17\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpplx-7b-online\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     18\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: [{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>e\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: prompt}]\n\u001b[1;32m     19\u001b[0m }\n\u001b[1;32m     20\u001b[0m response \u001b[38;5;241m=\u001b[39m requests\u001b[38;5;241m.\u001b[39mpost(url, json\u001b[38;5;241m=\u001b[39mdata, headers\u001b[38;5;241m=\u001b[39mheaders)\n\u001b[0;32m---> 21\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mchoices\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m[\u001b[38;5;241m0\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessage\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'choices'"]}], "source": ["import os\n", "import requests\n", "from pdfminer.high_level import extract_text\n", "\n", "PERPLEXITY_API_KEY = myapikey\n", "\n", "def extract_text_from_pdf(pdf_path):\n", "    return extract_text(pdf_path)\n", "\n", "def get_perplexity_response(prompt):\n", "    url = \"https://api.perplexity.ai/chat/completions\"\n", "    headers = {\n", "        \"Authorization\": f\"Bearer {PERPLEXITY_API_KEY}\",\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "    data = {\n", "        \"model\": \"pplx-7b-online\",\n", "        \"messages\": [{\"role\": \"user\", \"content\": prompt}]\n", "    }\n", "    response = requests.post(url, json=data, headers=headers)\n", "    return response.json()[\"choices\"][0][\"message\"][\"content\"]\n", "\n", "def research_professors(school, field):\n", "    prompt = f\"Find professors at {school} doing research in {field}. Provide their names, research interests, and recent publications.\"\n", "    return get_perplexity_response(prompt)\n", "\n", "def generate_email(professor, cv_content, personal_statement, email_example):\n", "    prompt = f\"\"\"\n", "    Generate an email to Professor {professor} inquiring about lab positions for the following fall.\n", "    Use this information:\n", "    CV: {cv_content}\n", "    Personal Statement: {personal_statement}\n", "    Email Example: {email_example}\n", "    Make the email personalized, mentioning specific research interests that align with the professor's work.\n", "    \"\"\"\n", "    return get_perplexity_response(prompt)\n", "\n", "def main():\n", "    school = input(\"Enter the graduate school name: \")\n", "    field = input(\"Enter your field of research: \")\n", "    cv_path = input(\"Enter the path to your CV file (PDF): \")\n", "    personal_statement = input(\"Enter your personal statement: \")\n", "    email_example = input(\"Enter an example email: \")\n", "\n", "    cv_content = extract_text_from_pdf(cv_path)\n", "\n", "    professors = research_professors(school, field)\n", "    print(\"\\nProfessors found:\")\n", "    print(professors)\n", "\n", "    professor_name = input(\"\\nEnter the name of the professor you'd like to email: \")\n", "    email = generate_email(professor_name, cv_content, personal_statement, email_example)\n", "\n", "    print(\"\\nGenerated Email:\")\n", "    print(email)\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Paste your email example below:\n", "\n", "(Press Ctrl+D on Unix/Linux/Mac or Ctrl+Z on Windows, then Enter to finish)\n"]}, {"ename": "KeyError", "evalue": "'choices'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[20], line 66\u001b[0m\n\u001b[1;32m     63\u001b[0m     \u001b[38;5;28mprint\u001b[39m(email)\n\u001b[1;32m     65\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;18m__name__\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__main__\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m---> 66\u001b[0m     \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[20], line 55\u001b[0m, in \u001b[0;36mmain\u001b[0;34m()\u001b[0m\n\u001b[1;32m     52\u001b[0m cv_content \u001b[38;5;241m=\u001b[39m extract_text_from_pdf(cv_path)\n\u001b[1;32m     53\u001b[0m sop_content \u001b[38;5;241m=\u001b[39m extract_text_from_pdf(sop_path)\n\u001b[0;32m---> 55\u001b[0m professors \u001b[38;5;241m=\u001b[39m \u001b[43mresearch_professors\u001b[49m\u001b[43m(\u001b[49m\u001b[43mschool\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfield\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     56\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mProfessors found:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     57\u001b[0m \u001b[38;5;28mprint\u001b[39m(professors)\n", "Cell \u001b[0;32mIn[20], line 25\u001b[0m, in \u001b[0;36mresearch_professors\u001b[0;34m(school, field)\u001b[0m\n\u001b[1;32m     23\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mresearch_professors\u001b[39m(school, field):\n\u001b[1;32m     24\u001b[0m     prompt \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFind professors at \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mschool\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m doing research in \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfield\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m. Provide their names, research interests, and recent publications.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m---> 25\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mget_perplexity_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprompt\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[20], line 21\u001b[0m, in \u001b[0;36mget_perplexity_response\u001b[0;34m(prompt)\u001b[0m\n\u001b[1;32m     16\u001b[0m data \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m     17\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpplx-7b-online\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     18\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: [{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>e\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: prompt}]\n\u001b[1;32m     19\u001b[0m }\n\u001b[1;32m     20\u001b[0m response \u001b[38;5;241m=\u001b[39m requests\u001b[38;5;241m.\u001b[39mpost(url, json\u001b[38;5;241m=\u001b[39mdata, headers\u001b[38;5;241m=\u001b[39mheaders)\n\u001b[0;32m---> 21\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mchoices\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m[\u001b[38;5;241m0\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessage\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'choices'"]}], "source": ["import os\n", "import requests\n", "from pdfminer.high_level import extract_text\n", "\n", "PERPLEXITY_API_KEY = myapikey\n", "\n", "def extract_text_from_pdf(pdf_path):\n", "    return extract_text(pdf_path)\n", "\n", "def get_perplexity_response(prompt):\n", "    url = \"https://api.perplexity.ai/chat/completions\"\n", "    headers = {\n", "        \"Authorization\": f\"Bearer {PERPLEXITY_API_KEY}\",\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "    data = {\n", "        \"model\": \"pplx-7b-online\",\n", "        \"messages\": [{\"role\": \"user\", \"content\": prompt}]\n", "    }\n", "    response = requests.post(url, json=data, headers=headers)\n", "    return response.json()[\"choices\"][0][\"message\"][\"content\"]\n", "\n", "def research_professors(school, field):\n", "    prompt = f\"Find professors at {school} doing research in {field}. Provide their names, research interests, and recent publications.\"\n", "    return get_perplexity_response(prompt)\n", "\n", "def generate_email(professor, cv_content, sop_content, email_example):\n", "    prompt = f\"\"\"\n", "    Generate an email to Professor {professor} inquiring about lab positions for the following fall.\n", "    Use this information:\n", "    CV: {cv_content}\n", "    Statement of Purpose: {sop_content}\n", "    Email Example: {email_example}\n", "    Make the email personalized, mentioning specific research interests that align with the professor's work.\n", "    \"\"\"\n", "    return get_perplexity_response(prompt)\n", "\n", "def get_multiline_input(prompt):\n", "    print(prompt)\n", "    print(\"(Press Ctrl+D on Unix/Linux/Mac or Ctrl+Z on Windows, then Enter to finish)\")\n", "    return \"\".join(iter(input, \"\"))\n", "\n", "def main():\n", "    school = input(\"Enter the graduate school name: \")\n", "    field = input(\"Enter your field of research: \")\n", "    cv_path = input(\"Enter the path to your CV file (PDF): \")\n", "    sop_path = input(\"Enter the path to your Statement of Purpose file (PDF): \")\n", "    \n", "    print(\"\\nPaste your email example below:\")\n", "    email_example = get_multiline_input(\"\")\n", "\n", "    cv_content = extract_text_from_pdf(cv_path)\n", "    sop_content = extract_text_from_pdf(sop_path)\n", "\n", "    professors = research_professors(school, field)\n", "    print(\"\\nProfessors found:\")\n", "    print(professors)\n", "\n", "    professor_name = input(\"\\nEnter the name of the professor you'd like to email: \")\n", "    email = generate_email(professor_name, cv_content, sop_content, email_example)\n", "\n", "    print(\"\\nGenerated Email:\")\n", "    print(email)\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'choices'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[21], line 45\u001b[0m\n\u001b[1;32m     42\u001b[0m interests \u001b[38;5;241m=\u001b[39m \u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEnter your research interests: \u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     43\u001b[0m experience \u001b[38;5;241m=\u001b[39m \u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEnter your research experience: \u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 45\u001b[0m \u001b[43mresearch_professors\u001b[49m\u001b[43m(\u001b[49m\u001b[43mschool\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfield\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minterests\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexperience\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[21], line 19\u001b[0m, in \u001b[0;36mresearch_professors\u001b[0;34m(school, field, interests, experience)\u001b[0m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mresearch_professors\u001b[39m(school, field, interests, experience):\n\u001b[1;32m     18\u001b[0m     query \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFind professors at \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mschool\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m who research topics in \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfield\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m related to \u001b[39m\u001b[38;5;132;01m{\u001b[39;00minterests\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m. Include their name, research focus, and recent publications.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m---> 19\u001b[0m     professors_info \u001b[38;5;241m=\u001b[39m \u001b[43mget_perplexity_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     21\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mProfessor Information:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     22\u001b[0m     \u001b[38;5;28mprint\u001b[39m(professors_info)\n", "Cell \u001b[0;32mIn[21], line 15\u001b[0m, in \u001b[0;36mget_perplexity_response\u001b[0;34m(query)\u001b[0m\n\u001b[1;32m     10\u001b[0m data \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m     11\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgpt-3.5-turbo\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     12\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: [{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>e\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: query}]\n\u001b[1;32m     13\u001b[0m }\n\u001b[1;32m     14\u001b[0m response \u001b[38;5;241m=\u001b[39m requests\u001b[38;5;241m.\u001b[39mpost(url, headers\u001b[38;5;241m=\u001b[39mheaders, data\u001b[38;5;241m=\u001b[39mjson\u001b[38;5;241m.\u001b[39mdumps(data))\n\u001b[0;32m---> 15\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mchoices\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m[\u001b[38;5;241m0\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmessage\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'choices'"]}], "source": ["import requests\n", "import json\n", "\n", "def get_perplexity_response(query):\n", "    url = \"https://api.perplexity.ai/chat/completions\"\n", "    headers = {\n", "        \"Authorization\": \"Bearer 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\",\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "    data = {\n", "        \"model\": \"gpt-3.5-turbo\",\n", "        \"messages\": [{\"role\": \"user\", \"content\": query}]\n", "    }\n", "    response = requests.post(url, headers=headers, data=json.dumps(data))\n", "    return response.json()['choices'][0]['message']['content']\n", "\n", "def research_professors(school, field, interests, experience):\n", "    query = f\"Find professors at {school} who research topics in {field} related to {interests}. Include their name, research focus, and recent publications.\"\n", "    professors_info = get_perplexity_response(query)\n", "    \n", "    print(\"Professor Information:\")\n", "    print(professors_info)\n", "    print(\"\\n\")\n", "    \n", "    professors = professors_info.split(\"\\n\\n\")\n", "    for professor in professors:\n", "        if professor.strip():\n", "            name = professor.split(\"\\n\")[0]\n", "            email_template = generate_email(name, interests, experience)\n", "            print(f\"Email Template for {name}:\")\n", "            print(email_template)\n", "            print(\"\\n\")\n", "\n", "def generate_email(professor_name, interests, experience):\n", "    query = f\"Write a professional email to Professor {professor_name} expressing interest in their research based on the following interests: {interests}. Mention the following research experience: {experience}. Ask if they will be taking new graduate students into their lab next fall.\"\n", "    email_template = get_perplexity_response(query)\n", "    return email_template\n", "\n", "# Main program\n", "school = input(\"Enter the name of the graduate school: \")\n", "field = input(\"Enter the research field: \")\n", "interests = input(\"Enter your research interests: \")\n", "experience = input(\"Enter your research experience: \")\n", "\n", "research_professors(school, field, interests, experience)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Professor Information:\n", "Several professors at the University of California, Los Angeles (UCLA) are involved in Experimental Condensed Matter Physics research related to 2D Materials, Spintronics, Quantum Information/Computing, Topological Materials, and ARPES. Here are some relevant faculty members:\n", "\n", "1. **<PERSON>**:\n", "   - **Research Focus**: Experimental Condensed Matter Physics, particularly in quasi-one dimensional and quasi-two dimensional crystals, organic conductors, and the effects of dimensionality, electronic correlations, and disorder.\n", "   - **Recent Publications**: Not listed in the provided sources.\n", "\n", "2. **<PERSON><PERSON>**:\n", "   - **Research Focus**: Experimental application of magnetic resonance under extreme conditions, including the study of spin density waves and related phases in organic conductors, and the development of nuclear magnetic resonance instrumentation for high magnetic fields.\n", "   - **Recent Publications**: Not listed in the provided sources.\n", "\n", "3. **<PERSON><PERSON><PERSON>**:\n", "   - **Research Focus**: Experimental Condensed Matter Physics, specifically in non-equilibrium states and properties of matter using ultrafast light pulses to induce and study charge density waves, superconductors, and other exotic phases. He also explores the role of Coulomb energy in strongly correlated electron systems.\n", "   - **Recent Publications**: Not listed in the provided sources.\n", "\n", "4. **<PERSON><PERSON>**:\n", "   - **Research Focus**: Condensed Matter Theory, particularly in topological phases and other topological aspects of condensed matter, including the study of systems with many particles where quantum effects are important.\n", "   - **Recent Publications**: Not listed in the provided sources.\n", "\n", "For more detailed information on recent publications, it is recommended to visit the faculty members' personal websites or academic databases such as Google Scholar or ResearchGate.\n", "\n", "### Additional Faculty Members to Consider:\n", "- **<PERSON>**: Professor, Experimental Plasma Physics, which may intersect with some aspects of condensed matter physics.\n", "- **<PERSON><PERSON><PERSON>**: Distinguished Professor, Theoretical Condensed Matter Physics, with a focus on quantum many-body systems and topological phases.\n", "- **<PERSON><PERSON>**: Distinguished Professor, Experimental Condensed Matter Physics, with research interests in quantum computing and topological materials.\n", "- **<PERSON><PERSON><PERSON>**: Professor, Theoretical Condensed Matter Physics, with a focus on spintronics and topological materials.\n", "\n", "Please note that the provided sources do not include recent publications for these faculty members. For the most current and comprehensive information, it is best to consult their personal websites or academic databases.\n", "\n", "\n", "Email Template for Several professors at the University of California, Los Angeles (UCLA) are involved in Experimental Condensed Matter Physics research related to 2D Materials, Spintronics, Quantum Information/Computing, Topological Materials, and ARPES. Here are some relevant faculty members::\n", "Subject: Expressed Interest in Experimental Condensed Matter Physics Research at UCLA\n", "\n", "Dear Professors <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>,\n", "\n", "I hope this email finds you well. I am writing to express my deep interest in the Experimental Condensed Matter Physics research conducted at the University of California, Los Angeles (UCLA), particularly in the areas of 2D Materials, Spintronics, Quantum Information/Computing, Topological Materials, and Angle-Resolved Photoemission Spectroscopy (ARPES).\n", "\n", "After reviewing the faculty research profiles on the UCLA Physics & Astronomy website, I was impressed by the breadth and depth of research projects in these areas. Specifically, I am drawn to the work on organic conductors and superconductors by Professor <PERSON>, the exploration of non-equilibrium states and properties of matter using ultrafast light pulses by Professor <PERSON>, the experimental application of magnetic resonance to condensed matter physics by Professor <PERSON>, and the theoretical study of topological phases and other topological aspects of condensed matter by Professor <PERSON>.\n", "\n", "My background includes experience in ARPES data analysis on topological semimetals and their electronic band structure. This experience has sparked a strong interest in further exploring the properties and applications of these materials. I am particularly intrigued by the potential of topological materials for quantum computing and spintronics applications.\n", "\n", "I am reaching out to inquire if any of you will be accepting new graduate students into your labs for the upcoming fall semester. I am eager to contribute to and learn from the cutting-edge research being conducted at UCLA. If there are any opportunities available, I would be grateful for the chance to discuss my qualifications and research interests further.\n", "\n", "Thank you for your time and consideration. I look forward to the possibility of hearing from you.\n", "\n", "Best regards,\n", "\n", "[Your Name]  \n", "[Your Contact Information]  \n", "[Your Background/Current Affiliation]  \n", "[Your Email Address]  \n", "[Your Phone Number]  \n", "[Your LinkedIn/ResearchGate Profile (if applicable)]\n", "\n", "\n", "<PERSON><PERSON> Template for 1. **<PERSON>**::\n", "Subject: Expressed Interest in Research Opportunities in 2D Materials and Spintronics\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is [Your Name], and I am reaching out to express my deep interest in your research group's work at UCLA, particularly in the areas of 2D materials, spintronics, quantum information/computing, and topological materials. Your group's contributions to understanding the roles of dimensionality, electronic correlations, and disorder in electronic materials align closely with my academic and professional aspirations.\n", "\n", "I am particularly intrigued by the potential applications of 2D materials in spintronics and quantum computing. My background includes experience with ARPES (Angle-Resolved Photoemission Spectroscopy) data analysis on topological semimetals and their electronic band structure. This experience has not only deepened my understanding of the electronic properties of topological materials but also sparked my interest in exploring their potential in quantum information technologies.\n", "\n", "Given my research experience and interests, I am eager to learn more about the opportunities available in your lab. Specifically, I would like to inquire if you will be taking new graduate students into your lab next fall. I am confident that working under your guidance would provide me with invaluable insights and hands-on experience in cutting-edge research areas.\n", "\n", "I would greatly appreciate any information you can provide about the application process, potential research projects, and any specific requirements or recommendations you have for prospective graduate students.\n", "\n", "Thank you for your time and consideration. I look forward to the possibility of discussing my interests further with you.\n", "\n", "Best regards,\n", "\n", "[Your Name]\n", "\n", "\n", "<PERSON><PERSON>mplate for 2. **<PERSON><PERSON>**::\n", "Subject: Expressed Interest in Research Opportunities in 2D Materials and Spintronics\n", "\n", "Dear Professor <PERSON><PERSON>,\n", "\n", "I hope this email finds you well. I am writing to express my profound interest in your research group's work on 2D materials, spintronics, quantum information/computing, and topological materials. Your contributions to these fields have been particularly inspiring to me, and I am eager to explore potential opportunities to contribute to and learn from your research endeavors.\n", "\n", "My background includes experience in ARPES data analysis on topological semimetals, focusing on their electronic band structure. This experience has not only deepened my understanding of the intricate properties of these materials but also sparked a keen interest in the broader applications of 2D materials and spintronics in quantum computing and information technology.\n", "\n", "Your research aligns closely with my academic and professional aspirations. I am particularly fascinated by the potential of 2D materials to revolutionize quantum computing and spintronics, and I believe that your lab's work on these topics is at the forefront of this exciting field.\n", "\n", "I am reaching out to inquire if you will be accepting new graduate students into your lab for the upcoming fall semester. I am highly motivated to pursue a graduate degree and believe that working under your supervision would provide me with invaluable opportunities to grow both academically and professionally.\n", "\n", "I would be honored to discuss my qualifications and research interests further with you. Please let me know if there are any additional steps I can take to express my interest or if you require any additional information from me.\n", "\n", "Thank you for your time and consideration. I look forward to the possibility of discussing this further.\n", "\n", "Best regards,\n", "\n", "[Your Name]\n", "\n", "\n", "<PERSON><PERSON> Template for 3. **<PERSON><PERSON><PERSON>**::\n", "Subject: Expressed Interest in Research Opportunities with Your Lab\n", "\n", "Dear Professor <PERSON><PERSON><PERSON>,\n", "\n", "I hope this email finds you well. My name is [Your Name], and I am reaching out to express my profound interest in the research activities conducted in your lab at UCLA. After reviewing your research profile and publications, I am particularly drawn to your work on ultrafast probes of materials, quantum phases of matter built from 2D van der Waals materials, and the exploration of topological materials.\n", "\n", "My background and research interests align closely with your lab's focus areas. Specifically, I have a strong interest in 2D materials, spintronics, quantum information/computing, and topological materials. My previous research experience includes ARPES data analysis on topological semimetals and their electronic band structure, which I believe would be a valuable asset to your research team.\n", "\n", "I am impressed by your lab's innovative use of ultrafast electron diffraction and time-resolved second harmonic generation to study non-equilibrium states of matter and the manipulation of topological defects in ordered phases. Your work on developing spectroscopic tools to measure Coulomb energy changes across phase transitions also resonates with my interests in understanding the fundamental properties of strongly correlated electron systems.\n", "\n", "I am currently exploring graduate programs and am particularly interested in joining your lab at UCLA. I would like to inquire if you will be accepting new graduate students into your lab next fall. If so, I would be honored to discuss my qualifications and research aspirations further with you.\n", "\n", "Thank you for your time and consideration. I look forward to the possibility of discussing this opportunity further.\n", "\n", "Best regards,\n", "\n", "[Your Name]  \n", "[Your Contact Information]  \n", "[Your University/Institution (if applicable)]  \n", "[Your Current Degree/Position (if applicable)]\n", "\n", "\n", "<PERSON><PERSON> Template for 4. **<PERSON><PERSON>**::\n", "Subject: Expressed Interest in Research Opportunities in 2D Materials and Spintronics\n", "\n", "Dear Professor <PERSON><PERSON>,\n", "\n", "I hope this email finds you well. My name is [Your Name], and I am reaching out to express my deep interest in the research conducted by your group, particularly in the areas of 2D materials, spintronics, quantum information/computing, and topological materials. Your work in these fields aligns closely with my academic and professional aspirations.\n", "\n", "I am particularly drawn to your contributions to the understanding of quantum spin Hall systems and the exploration of emergent phenomena in van der Waals heterostructures. Your expertise in strongly correlated systems and the application of topological ideas to analyze them resonates with my own research interests.\n", "\n", "My background includes experience in ARPES data analysis on topological semimetals and their electronic band structure. This experience has provided me with a solid foundation in understanding the experimental and theoretical aspects of topological materials, which I believe would be valuable in contributing to your research endeavors.\n", "\n", "I am eager to further my knowledge and skills in these areas and am considering pursuing graduate studies. I would like to inquire if your lab will be accepting new graduate students for the next fall semester. It would be an honor to have the opportunity to work under your supervision and contribute to the ongoing research projects in your group.\n", "\n", "Thank you for your time and consideration. I look forward to the possibility of discussing my application and research interests further.\n", "\n", "Best regards,\n", "\n", "[Your Name]  \n", "[Your Contact Information]  \n", "[Your University/Institution]  \n", "[Date: October 12, 2024]\n", "\n", "\n", "Email Template for For more detailed information on recent publications, it is recommended to visit the faculty members' personal websites or academic databases such as Google Scholar or ResearchGate.:\n", "Subject: Expressed Interest in Research Opportunities in 2D Materials and Quantum Computing\n", "\n", "Dear Professor [Professor's Name],\n", "\n", "I hope this email finds you well. I am writing to express my deep interest in the research conducted by your group, particularly in the areas of 2D materials, spintronics, quantum information/computing, and topological materials. Your work, as reflected in recent publications and academic databases such as Google Scholar and ResearchGate, aligns closely with my academic and professional aspirations.\n", "\n", "My background includes experience in analyzing ARPES data on topological semimetals and their electronic band structure. This experience has not only deepened my understanding of the intricate properties of topological materials but also sparked a keen interest in exploring their potential applications in quantum computing and spintronics.\n", "\n", "Your research group's focus on utilizing ARPES to investigate the electronic structure of topological quantum materials, as highlighted in recent studies, resonates with my research interests. The application of ARPES in understanding the band inversion and topological surface states in materials like Bi4Br4 and MnBi2Te4 is particularly fascinating and aligns with my goals of contributing to the advancement of quantum materials research.\n", "\n", "I am particularly intrigued by the potential of topological materials in quantum computing and spintronics, as discussed in recent reviews. The integration of quantum materials with spintronics, leading to enhanced device functionality and the realization of topological quantum computing, is an area I am eager to explore further.\n", "\n", "Given my research experience and interests, I am keen to learn more about potential opportunities to join your lab as a graduate student. I would greatly appreciate any information regarding whether you will be accepting new graduate students into your lab next fall.\n", "\n", "Thank you for your time and consideration. I look forward to the possibility of discussing my interests and qualifications further.\n", "\n", "Best regards,\n", "\n", "[Your Name]\n", "\n", "\n", "Email Template for ### Additional Faculty Members to Consider::\n", "Here is a professional email template that you can use to express interest in the research of additional faculty members:\n", "\n", "---\n", "\n", "Subject: Expressed Interest in Research Opportunities\n", "\n", "Dear Professor [Faculty Member's Name],\n", "\n", "I hope this email finds you well. My name is [Your Name], and I am reaching out to express my deep interest in your research work, particularly in the areas of 2D Materials, Spintronics, Quantum Information/Computing, Topological Materials, and ARPES.\n", "\n", "I am particularly drawn to your work because of my own research experience in ARPES data analysis on topological semimetals and their electronic band structure. This experience has not only deepened my understanding of the field but also fueled my passion for exploring the potential applications of these materials in quantum computing and information technologies.\n", "\n", "Your contributions to the field, as evident from your publications and research projects, are truly inspiring and align closely with my academic and professional aspirations. I am particularly impressed by your work on [specific research project or publication that resonates with you].\n", "\n", "I am currently in the process of applying to graduate programs and am eager to find a research environment that aligns with my interests and goals. I would be honored to have the opportunity to discuss my application and potential research opportunities in your lab.\n", "\n", "Could you please let me know if you will be taking new graduate students into your lab next fall? I would greatly appreciate any guidance or advice you might have regarding the application process and any specific requirements or expectations for your lab.\n", "\n", "Thank you for your time and consideration. I look forward to the possibility of discussing my application further.\n", "\n", "Best regards,\n", "\n", "[Your Name]\n", "\n", "---\n", "\n", "**Faculty Members to Consider:**\n", "\n", "1. **<PERSON>** - <PERSON><PERSON> Professor in Spintronics and Quantum Information, Pritzker School of Molecular Engineering at the University of Chicago; Senior Scientist and quantum group leader, Argonne National Laboratory.\n", "2. **<PERSON>** - <PERSON> Professor of Physics and Astronomy, Purdue University.\n", "3. **<PERSON>** - Associate Professor in The Chandra Family Department of Electrical and Computer Engineering at The University of Texas at Austin.\n", "4. **<PERSON><PERSON>** - <PERSON><PERSON> Professor of Physics, Professor of Materials Science/Engineering, Penn State.\n", "\n", "**Note:** Ensure to customize the email by replacing [Faculty Member's Name] and [Your Name] with the appropriate names and tailoring the content to reflect your genuine interest and experience.\n", "\n", "\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[24], line 61\u001b[0m\n\u001b[1;32m     58\u001b[0m interests \u001b[38;5;241m=\u001b[39m \u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEnter your research interests: \u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     59\u001b[0m experience \u001b[38;5;241m=\u001b[39m \u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEnter your research experience: \u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 61\u001b[0m \u001b[43mresearch_professors\u001b[49m\u001b[43m(\u001b[49m\u001b[43mschool\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfield\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minterests\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexperience\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[24], line 44\u001b[0m, in \u001b[0;36mresearch_professors\u001b[0;34m(school, field, interests, experience)\u001b[0m\n\u001b[1;32m     42\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m professor\u001b[38;5;241m.\u001b[39mstrip():\n\u001b[1;32m     43\u001b[0m     name \u001b[38;5;241m=\u001b[39m professor\u001b[38;5;241m.\u001b[39msplit(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)[\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m---> 44\u001b[0m     email_template \u001b[38;5;241m=\u001b[39m \u001b[43mgenerate_email\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minterests\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexperience\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     45\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m email_template:\n\u001b[1;32m     46\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEmail Template for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[0;32mIn[24], line 52\u001b[0m, in \u001b[0;36mgenerate_email\u001b[0;34m(professor_name, interests, experience)\u001b[0m\n\u001b[1;32m     50\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mgenerate_email\u001b[39m(professor_name, interests, experience):\n\u001b[1;32m     51\u001b[0m     query \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWrite a professional email to Professor \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mprofessor_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m expressing interest in their research based on the following interests: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00minterests\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m. Mention the following research experience: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mexperience\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m. Ask if they will be taking new graduate students into their lab next fall.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m---> 52\u001b[0m     email_template \u001b[38;5;241m=\u001b[39m \u001b[43mget_perplexity_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     53\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m email_template\n", "Cell \u001b[0;32mIn[24], line 16\u001b[0m, in \u001b[0;36mget_perplexity_response\u001b[0;34m(query)\u001b[0m\n\u001b[1;32m     10\u001b[0m payload \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m     11\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mllama-3.1-sonar-huge-128k-online\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     12\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: [{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>e\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: query}]\n\u001b[1;32m     13\u001b[0m }\n\u001b[1;32m     15\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 16\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mrequests\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpost\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpayload\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     17\u001b[0m     response\u001b[38;5;241m.\u001b[39mraise_for_status()\n\u001b[1;32m     18\u001b[0m     result \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mjson()\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/requests/api.py:115\u001b[0m, in \u001b[0;36mpost\u001b[0;34m(url, data, json, **kwargs)\u001b[0m\n\u001b[1;32m    103\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(url, data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, json\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    104\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"Sends a POST request.\u001b[39;00m\n\u001b[1;32m    105\u001b[0m \n\u001b[1;32m    106\u001b[0m \u001b[38;5;124;03m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    112\u001b[0m \u001b[38;5;124;03m    :rtype: requests.Response\u001b[39;00m\n\u001b[1;32m    113\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 115\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mpost\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/requests/api.py:59\u001b[0m, in \u001b[0;36mrequest\u001b[0;34m(method, url, **kwargs)\u001b[0m\n\u001b[1;32m     55\u001b[0m \u001b[38;5;66;03m# By using the 'with' statement we are sure the session is closed, thus we\u001b[39;00m\n\u001b[1;32m     56\u001b[0m \u001b[38;5;66;03m# avoid leaving sockets open which can trigger a ResourceWarning in some\u001b[39;00m\n\u001b[1;32m     57\u001b[0m \u001b[38;5;66;03m# cases, and look like a memory leak in others.\u001b[39;00m\n\u001b[1;32m     58\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m sessions\u001b[38;5;241m.\u001b[39mSession() \u001b[38;5;28;01mas\u001b[39;00m session:\n\u001b[0;32m---> 59\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msession\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/requests/sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[0;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[1;32m    584\u001b[0m send_kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    585\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m: timeout,\n\u001b[1;32m    586\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m: allow_redirects,\n\u001b[1;32m    587\u001b[0m }\n\u001b[1;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[0;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43msend_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/requests/sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[0;34m(self, request, **kwargs)\u001b[0m\n\u001b[1;32m    700\u001b[0m start \u001b[38;5;241m=\u001b[39m preferred_clock()\n\u001b[1;32m    702\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[0;32m--> 703\u001b[0m r \u001b[38;5;241m=\u001b[39m \u001b[43madapter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    705\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[1;32m    706\u001b[0m elapsed \u001b[38;5;241m=\u001b[39m preferred_clock() \u001b[38;5;241m-\u001b[39m start\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/requests/adapters.py:486\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    483\u001b[0m     timeout \u001b[38;5;241m=\u001b[39m TimeoutSauce(connect\u001b[38;5;241m=\u001b[39mtimeout, read\u001b[38;5;241m=\u001b[39mtimeout)\n\u001b[1;32m    485\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 486\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    487\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    488\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    489\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    490\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    491\u001b[0m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    492\u001b[0m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    493\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    494\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    495\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    496\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    497\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    498\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    500\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m    501\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(err, request\u001b[38;5;241m=\u001b[39mrequest)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/urllib3/connectionpool.py:793\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[1;32m    790\u001b[0m response_conn \u001b[38;5;241m=\u001b[39m conn \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m release_conn \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    792\u001b[0m \u001b[38;5;66;03m# Make the request on the HTTPConnection object\u001b[39;00m\n\u001b[0;32m--> 793\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    794\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    795\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    796\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    797\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    798\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    799\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    800\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    801\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    802\u001b[0m \u001b[43m    \u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresponse_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    803\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    804\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    805\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    806\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    808\u001b[0m \u001b[38;5;66;03m# Everything went great!\u001b[39;00m\n\u001b[1;32m    809\u001b[0m clean_exit \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/urllib3/connectionpool.py:537\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[0m\n\u001b[1;32m    535\u001b[0m \u001b[38;5;66;03m# Receive the response from the server\u001b[39;00m\n\u001b[1;32m    536\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 537\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    538\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (BaseSSLError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    539\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_raise_timeout(err\u001b[38;5;241m=\u001b[39me, url\u001b[38;5;241m=\u001b[39murl, timeout_value\u001b[38;5;241m=\u001b[39mread_timeout)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/urllib3/connection.py:466\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    463\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mresponse\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m HTTPResponse\n\u001b[1;32m    465\u001b[0m \u001b[38;5;66;03m# Get the response from http.client.HTTPConnection\u001b[39;00m\n\u001b[0;32m--> 466\u001b[0m httplib_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetresponse\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    468\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    469\u001b[0m     assert_header_parsing(httplib_response\u001b[38;5;241m.\u001b[39mmsg)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/http/client.py:1423\u001b[0m, in \u001b[0;36mHTTPConnection.getresponse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1421\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1422\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1423\u001b[0m         \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbegin\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1424\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m:\n\u001b[1;32m   1425\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclose()\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/http/client.py:331\u001b[0m, in \u001b[0;36mHTTPResponse.begin\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    329\u001b[0m \u001b[38;5;66;03m# read until we get a non-100 response\u001b[39;00m\n\u001b[1;32m    330\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 331\u001b[0m     version, status, reason \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_read_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    332\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m status \u001b[38;5;241m!=\u001b[39m CONTINUE:\n\u001b[1;32m    333\u001b[0m         \u001b[38;5;28;01mbreak\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/http/client.py:292\u001b[0m, in \u001b[0;36mHTTPResponse._read_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    291\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_read_status\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m--> 292\u001b[0m     line \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mstr\u001b[39m(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mreadline\u001b[49m\u001b[43m(\u001b[49m\u001b[43m_MAXLINE\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124miso-8859-1\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    293\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(line) \u001b[38;5;241m>\u001b[39m _MAXLINE:\n\u001b[1;32m    294\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m LineTooLong(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstatus line\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/socket.py:707\u001b[0m, in \u001b[0;36mSocketIO.readinto\u001b[0;34m(self, b)\u001b[0m\n\u001b[1;32m    705\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m    706\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 707\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrecv_into\u001b[49m\u001b[43m(\u001b[49m\u001b[43mb\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    708\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m timeout:\n\u001b[1;32m    709\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_timeout_occurred \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/ssl.py:1252\u001b[0m, in \u001b[0;36mSSLSocket.recv_into\u001b[0;34m(self, buffer, nbytes, flags)\u001b[0m\n\u001b[1;32m   1248\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m flags \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m   1249\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m   1250\u001b[0m           \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnon-zero flags not allowed in calls to recv_into() on \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m\n\u001b[1;32m   1251\u001b[0m           \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m)\n\u001b[0;32m-> 1252\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnbytes\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbuffer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1253\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1254\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39mrecv_into(buffer, nbytes, flags)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/ssl.py:1104\u001b[0m, in \u001b[0;36mSSLSocket.read\u001b[0;34m(self, len, buffer)\u001b[0m\n\u001b[1;32m   1102\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1103\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m buffer \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m-> 1104\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sslobj\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbuffer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1105\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1106\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_sslobj\u001b[38;5;241m.\u001b[39mread(\u001b[38;5;28mlen\u001b[39m)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import requests\n", "import json\n", "\n", "def get_perplexity_response(query):\n", "    url = \"https://api.perplexity.ai/chat/completions\"\n", "    headers = {\n", "        \"Authorization\": \"Bearer 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\",\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "    payload = {\n", "        \"model\": \"llama-3.1-sonar-huge-128k-online\",\n", "        \"messages\": [{\"role\": \"user\", \"content\": query}]\n", "    }\n", "    \n", "    try:\n", "        response = requests.post(url, json=payload, headers=headers)\n", "        response.raise_for_status()\n", "        result = response.json()\n", "        return result['choices'][0]['message']['content']\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"Request failed: {e}\")\n", "        return None\n", "    except KeyError as e:\n", "        print(f\"Unexpected response structure: {e}\")\n", "        print(f\"Full response: {result}\")\n", "        return None\n", "\n", "def research_professors(school, field, interests, experience):\n", "    query = f\"Find professors at {school} who research topics in {field} related to {interests}. Include their name, research focus, and recent publications.\"\n", "    professors_info = get_perplexity_response(query)\n", "    \n", "    if professors_info is None:\n", "        print(\"Unable to retrieve professor information.\")\n", "        return\n", "    \n", "    print(\"Professor Information:\")\n", "    print(professors_info)\n", "    print(\"\\n\")\n", "    \n", "    professors = professors_info.split(\"\\n\\n\")\n", "    for professor in professors:\n", "        if professor.strip():\n", "            name = professor.split(\"\\n\")[0]\n", "            email_template = generate_email(name, interests, experience)\n", "            if email_template:\n", "                print(f\"Email Template for {name}:\")\n", "                print(email_template)\n", "                print(\"\\n\")\n", "\n", "def generate_email(professor_name, interests, experience):\n", "    query = f\"Write a professional email to Professor {professor_name} expressing interest in their research based on the following interests: {interests}. Mention the following research experience: {experience}. Ask if they will be taking new graduate students into their lab next fall.\"\n", "    email_template = get_perplexity_response(query)\n", "    return email_template\n", "\n", "# Main program\n", "school = input(\"Enter the name of the graduate school: \")\n", "field = input(\"Enter the research field: \")\n", "interests = input(\"Enter your research interests: \")\n", "experience = input(\"Enter your research experience: \")\n", "\n", "research_professors(school, field, interests, experience)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'perplexity'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[28], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mperplexity\u001b[39;00m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m Perplexity\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'perplexity'"]}], "source": ["from perplexity import Perplexity"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: perplexityai in ./miniconda3/envs/mainenv/lib/python3.12/site-packages (1.0.5)\n", "Requirement already satisfied: requests in ./miniconda3/envs/mainenv/lib/python3.12/site-packages (from perplexityai) (2.31.0)\n", "Requirement already satisfied: websocket-client in ./miniconda3/envs/mainenv/lib/python3.12/site-packages (from perplexityai) (1.8.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in ./miniconda3/envs/mainenv/lib/python3.12/site-packages (from requests->perplexityai) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in ./miniconda3/envs/mainenv/lib/python3.12/site-packages (from requests->perplexityai) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in ./miniconda3/envs/mainenv/lib/python3.12/site-packages (from requests->perplexityai) (2.2.1)\n", "Requirement already satisfied: certifi>=2017.4.17 in ./miniconda3/envs/mainenv/lib/python3.12/site-packages (from requests->perplexityai) (2024.2.2)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install perplexityai"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting git+https://github.com/nathanrchn/perplexityai.git\n", "  Cloning https://github.com/nathanrchn/perplexityai.git to /tmp/pip-req-build-9arxq5ap\n", "  Running command git clone --filter=blob:none --quiet https://github.com/nathanrchn/perplexityai.git /tmp/pip-req-build-9arxq5ap\n", "  Resolved https://github.com/nathanrchn/perplexityai.git to commit d61c40b109a4614dea12ec91bed580cc64bc791c\n", "  Preparing metadata (setup.py) ... \u001b[?25lerror\n", "  \u001b[1;31merror\u001b[0m: \u001b[1msubprocess-exited-with-error\u001b[0m\n", "  \n", "  \u001b[31m×\u001b[0m \u001b[32mpython setup.py egg_info\u001b[0m did not run successfully.\n", "  \u001b[31m│\u001b[0m exit code: \u001b[1;36m1\u001b[0m\n", "  \u001b[31m╰─>\u001b[0m \u001b[31m[20 lines of output]\u001b[0m\n", "  \u001b[31m   \u001b[0m <PERSON><PERSON> (most recent call last):\n", "  \u001b[31m   \u001b[0m   File \"<string>\", line 2, in <module>\n", "  \u001b[31m   \u001b[0m   File \"<pip-setuptools-caller>\", line 34, in <module>\n", "  \u001b[31m   \u001b[0m   File \"/tmp/pip-req-build-9arxq5ap/setup.py\", line 6, in <module>\n", "  \u001b[31m   \u001b[0m     setup(\n", "  \u001b[31m   \u001b[0m   File \"/home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/setuptools/__init__.py\", line 103, in setup\n", "  \u001b[31m   \u001b[0m     return distutils.core.setup(**attrs)\n", "  \u001b[31m   \u001b[0m            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  \u001b[31m   \u001b[0m   File \"/home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/setuptools/_distutils/core.py\", line 147, in setup\n", "  \u001b[31m   \u001b[0m     _setup_distribution = dist = klass(attrs)\n", "  \u001b[31m   \u001b[0m                                  ^^^^^^^^^^^^\n", "  \u001b[31m   \u001b[0m   File \"/home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/setuptools/dist.py\", line 303, in __init__\n", "  \u001b[31m   \u001b[0m     _Distribution.__init__(self, dist_attrs)\n", "  \u001b[31m   \u001b[0m   File \"/home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/setuptools/_distutils/dist.py\", line 258, in __init__\n", "  \u001b[31m   \u001b[0m     getattr(self.metadata, \"set_\" + key)(val)\n", "  \u001b[31m   \u001b[0m   File \"/home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/setuptools/_distutils/dist.py\", line 1255, in set_requires\n", "  \u001b[31m   \u001b[0m     distutils.versionpredicate.VersionPredicate(v)\n", "  \u001b[31m   \u001b[0m   File \"/home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/setuptools/_distutils/versionpredicate.py\", line 122, in __init__\n", "  \u001b[31m   \u001b[0m     raise ValueError(\"expected parenthesized list: %r\" % paren)\n", "  \u001b[31m   \u001b[0m ValueError: expected parenthesized list: '-client'\n", "  \u001b[31m   \u001b[0m \u001b[31m[end of output]\u001b[0m\n", "  \n", "  \u001b[1;35mnote\u001b[0m: This error originates from a subprocess, and is likely not a problem with pip.\n", "\u001b[?25h\u001b[1;31merror\u001b[0m: \u001b[1mmetadata-generation-failed\u001b[0m\n", "\n", "\u001b[31m×\u001b[0m Encountered error while generating package metadata.\n", "\u001b[31m╰─>\u001b[0m See above for output.\n", "\n", "\u001b[1;35mnote\u001b[0m: This is an issue with the package mentioned above, not pip.\n", "\u001b[1;36mhint\u001b[0m: See above for details.\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install git+https://github.com/nathanrchn/perplexityai.gitimport requests\n", "import json\n", "\n", "def get_perplexity_response(query):\n", "    url = \"https://api.perplexity.ai/chat/completions\"\n", "    headers = {\n", "        \"Authorization\": \"Bearer 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\",\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "    payload = {\n", "        \"model\": \"llama-3.1-sonar-huge-128k-online\",\n", "        \"messages\": [{\"role\": \"user\", \"content\": query}]\n", "    }\n", "    \n", "    try:\n", "        response = requests.post(url, json=payload, headers=headers)\n", "        response.raise_for_status()\n", "        result = response.json()\n", "        return result['choices'][0]['message']['content']\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"Request failed: {e}\")\n", "        return None\n", "    except KeyError as e:\n", "        print(f\"Unexpected response structure: {e}\")\n", "        print(f\"Full response: {result}\")\n", "        return None\n", "\n", "def research_professors(school, field, interests, experience):\n", "    query = f\"Find professors at {school} who research topics in {field} related to {interests}. Include their name, research focus, and recent publications.\"\n", "    professors_info = get_perplexity_response(query)\n", "    \n", "    if professors_info is None:\n", "        print(\"Unable to retrieve professor information.\")\n", "        return\n", "    \n", "    print(\"Professor Information:\")\n", "    print(professors_info)\n", "    print(\"\\n\")\n", "    \n", "    professors = professors_info.split(\"\\n\\n\")\n", "    for professor in professors:\n", "        if professor.strip():\n", "            name = professor.split(\"\\n\")[0]\n", "            email_template = generate_email(name, interests, experience)\n", "            if email_template:\n", "                print(f\"Email Template for {name}:\")\n", "                print(email_template)\n", "                print(\"\\n\")\n", "\n", "def generate_email(professor_name, interests, experience):\n", "    query = f\"Write a professional email to Professor {professor_name} expressing interest in their research based on the following interests: {interests}. Mention the following research experience: {experience}. Ask if they will be taking new graduate students into their lab next fall.\"\n", "    email_template = get_perplexity_response(query)\n", "    return email_template\n", "\n", "# Main program\n", "def main():\n", "    school = \"Stanford University\"\n", "    field = \"Computer Science\"\n", "    interests = \"Machine Learning and Artificial Intelligence\"\n", "    experience = \"Internship at Google, research project on natural language processing\"\n", "    \n", "    research_professors(school, field, interests, experience)\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Based on the search results, several researchers at the University of California, Berkeley conduct experimental condensed matter physics research:\n", "\n", "1. <PERSON> is listed as a faculty member in the Department of Physics at UC Berkeley who does experimental condensed matter physics research[8].\n", "\n", "2. <PERSON> is also mentioned as a faculty member at UC Berkeley working in experimental condensed matter physics[8].\n", "\n", "3. <PERSON>, although not currently at Berkeley, received his PhD in Physics from UC Berkeley in 2012 and his research interests involve experimental condensed matter physics[2].\n", "\n", "4. The Berkeley physics department has a large community of researchers in condensed matter physics and materials physics. The experimental research activities cover several central topics in condensed matter physics, including:\n", "\n", "- Quantum information\n", "- Physics of nanomaterials  \n", "- Magnetism\n", "- Quantum materials\n", "- Optical properties\n", "- Photoemission\n", "- Superconductivity\n", "- New materials[1]\n", "\n", "5. <PERSON>, although no longer active, was credited with building the solid-state (now condensed matter) physics component of Berkeley's physics department. He played a key role in hiring faculty and worked closely with experimentalists conducting groundbreaking research in areas like semiconductors, magnetism, and nuclear magnetic resonance[4].\n", "\n", "The condensed matter physics research at UC Berkeley appears to be a significant and diverse area, with both current faculty members and a strong historical foundation in experimental work in this field.\n", "Based on the search results provided, there isn't specific information about individual researchers working on 2D materials, topological materials, spintronics, or quantum computing. However, I can provide some general insights about the research areas mentioned:\n", "\n", "## Spintronics and Quantum Computing\n", "\n", "Several research groups and centers are focused on spintronics and quantum computing:\n", "\n", "- The Center for Spintronics and Quantum Computation at the University of California - Santa <PERSON> brings together researchers from physics, materials science, electrical engineering, and computer science to work on spin-based electronics and quantum computation[2].\n", "\n", "- The Emergent Quantum Spintronics Research Unit at RIKEN, led by <PERSON><PERSON><PERSON>, aims to understand quantum and emergent properties in spintronics and develop novel spintronic phenomena in magnetic materials[4].\n", "\n", "- The Center for Quantum Spintronics (QuSpin) at NTNU focuses on using quantum entities like electron spin as information carriers for developing more efficient electronic devices[7].\n", "\n", "## 2D Materials and Topological Materials\n", "\n", "While not explicitly mentioned for specific researchers, these topics are relevant to the field:\n", "\n", "- 2D materials are drawing significant attention in spintronics due to their unique spin-dependent properties, such as long spin relaxation times and strong spin-orbit coupling[3].\n", "\n", "- Topological materials are being studied in the context of quantum critical metals and their potential applications in electronic devices[6].\n", "\n", "It's important to note that many researchers likely work across multiple areas, as these fields are interconnected in quantum materials research. The search results don't provide enough detail to attribute specific research focuses to individual scientists.\n"]}], "source": ["from perplexity import Perplexity\n", "import json\n", "perplexity = Perplexity(\"<EMAIL>\", )\n", "answer = perplexity.search(\"Who does experimental condensed matter physics research at the University of California, Berkeley?\")\n", "for a in answer:\n", "    if a['status'] == 'completed':\n", "        completed_data = json.loads(a['text'])\n", "        print(completed_data['answer'])\n", "\n", "answer = perplexity.search(\"Who of these people works on 2D materials and topological materials, and who works on spintronics or quantum computing?\")\n", "for a in answer:\n", "    if a['status'] == 'completed':\n", "        completed_data = json.loads(a['text'])\n", "        print(completed_data['answer'])\n", "perplexity.close()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mThe kernel failed to start as the Python Environment 'perp (Python 3.12.5)' is no longer available. Consider selecting another kernel or refreshing the list of Python Environments."]}], "source": ["pip install git+https://github.com/reteps/perplexityai.git"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mThe kernel failed to start as the Python Environment 'perp (Python 3.12.5)' is no longer available. Consider selecting another kernel or refreshing the list of Python Environments."]}], "source": ["pip install git+https://github.com/reteps/perplexityai.git"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["from perplexity import *\n", "perplexity= Perplexity('tylerjhad<PERSON><EMAIL>')\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Based on the search results, several researchers at the University of California, Berkeley conduct experimental condensed matter physics research:\n", "\n", "## Faculty Members\n", "\n", "- <PERSON> (Department of Physics)[8]\n", "- <PERSON> (Department of Physics)[8]\n", "\n", "## Research Areas\n", "\n", "Berkeley has a large community of researchers in condensed matter physics and materials physics with diverse interests[1]. Some key areas of experimental condensed matter research at Berkeley include:\n", "\n", "- Quantum information\n", "- Physics of nanomaterials  \n", "- Magnetism\n", "- Quantum materials\n", "- Optical properties\n", "- Photoemission\n", "- Superconductivity\n", "- New materials\n", "\n", "The experimentalists work closely with theorists at Berkeley to develop and validate new theoretical approaches in condensed matter physics[1].\n", "\n", "## Facilities\n", "\n", "Researchers at Berkeley collaborate with scientists at the nearby Lawrence Berkeley National Laboratory (LBNL), providing access to additional facilities and expertise[1].\n", "\n", "## Historical Context\n", "\n", "<PERSON> is credited with building the solid-state (now condensed-matter) physics component of Berkeley's physics department. He played a key role in hiring faculty, working with experimentalists, and establishing both undergraduate and graduate courses in condensed matter physics[4].\n", "\n", "Berkeley has a strong tradition of experimentalists and theorists working closely together to make advances in condensed matter physics. This collaborative approach has led to groundbreaking research in areas like semiconductors, magnetism, and nuclear magnetic resonance[4].\n"]}, {"ename": "AttributeError", "evalue": "'Perplexity' object has no attribute 'continue_search'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[18], line 7\u001b[0m\n\u001b[1;32m      5\u001b[0m         completed_data \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mloads(a[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtext\u001b[39m\u001b[38;5;124m'\u001b[39m])\n\u001b[1;32m      6\u001b[0m         \u001b[38;5;28mprint\u001b[39m(completed_data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124manswer\u001b[39m\u001b[38;5;124m'\u001b[39m])\n\u001b[0;32m----> 7\u001b[0m answer \u001b[38;5;241m=\u001b[39m \u001b[43mperplexity\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcontinue_search\u001b[49m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWho of these people works on 2D materials and topological materials, and who works on spintronics or quantum computing?\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      8\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m answer:\n\u001b[1;32m      9\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m a[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstatus\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcompleted\u001b[39m\u001b[38;5;124m'\u001b[39m:\n", "\u001b[0;31mAttributeError\u001b[0m: 'Perplexity' object has no attribute 'continue_search'"]}], "source": ["import json\n", "answer = perplexity.search(\"Who does experimental condensed matter physics research at the University of California, Berkeley?\")\n", "for a in answer:\n", "    if a['status'] == 'completed':\n", "        completed_data = json.loads(a['text'])\n", "        print(completed_data['answer'])\n", "answer = perplexity.continue_search(\"Who of these people works on 2D materials and topological materials, and who works on spintronics or quantum computing?\")\n", "for a in answer:\n", "    if a['status'] == 'completed':\n", "        completed_data = json.loads(a['text'])\n", "        print(completed_data['answer'])"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"ename": "AssertionError", "evalue": "already searching", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAssertionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[27], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m perplexity\u001b[38;5;241m.\u001b[39m_start_interaction()\n\u001b[0;32m----> 2\u001b[0m \u001b[43mperplexity\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_s\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mBruh\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/perplexityai-0.1-py3.12.egg/perplexity/perplexity.py:159\u001b[0m, in \u001b[0;36mPerplexity._s\u001b[0;34m(self, query, mode, search_focus, attachments, language, in_page, in_domain)\u001b[0m\n\u001b[1;32m    158\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_s\u001b[39m(\u001b[38;5;28mself\u001b[39m, query: \u001b[38;5;28mstr\u001b[39m, mode: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mconcise\u001b[39m\u001b[38;5;124m\"\u001b[39m, search_focus: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minternet\u001b[39m\u001b[38;5;124m\"\u001b[39m, attachments: \u001b[38;5;28mlist\u001b[39m[\u001b[38;5;28mstr\u001b[39m] \u001b[38;5;241m=\u001b[39m [], language: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124men-GB\u001b[39m\u001b[38;5;124m\"\u001b[39m, in_page: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m, in_domain: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 159\u001b[0m     \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfinished, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124malready searching\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    160\u001b[0m     \u001b[38;5;28;01massert\u001b[39;00m mode \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mconcise\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcopilot\u001b[39m\u001b[38;5;124m\"\u001b[39m], \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minvalid mode\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    161\u001b[0m     \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(attachments) \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m4\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtoo many attachments: max 4\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "\u001b[0;31mAssertionError\u001b[0m: already searching"]}], "source": ["perplexity._start_interaction()\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "PerplexityClient.__init__() missing 1 required positional argument: 'key'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[29], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m PerplexityClient\n\u001b[0;32m----> 3\u001b[0m client \u001b[38;5;241m=\u001b[39m \u001b[43mPerplexityClient\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      4\u001b[0m result \u001b[38;5;241m=\u001b[39m client\u001b[38;5;241m.\u001b[39mquery(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mShow me how to declare a list in Python\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28mprint\u001b[39m(result)\n", "\u001b[0;31mTypeError\u001b[0m: PerplexityClient.__init__() missing 1 required positional argument: 'key'"]}], "source": ["from perplexipy import PerplexityClient\n", "\n", "client = PerplexityClient()\n", "result = client.query('Show me how to declare a list in Python')\n", "print(result)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Professor Information:\n", "Several faculty members at the University of California, Los Angeles (UCLA) are involved in Experimental Condensed Matter Physics research related to two-dimensional materials, quantum computing, quantum devices, spintronics, direct electronic measurement methods, and topological quantum materials. Here are a few relevant professors:\n", "\n", "1. **<PERSON>**:\n", "   - **Research Focus**: Experimental Condensed Matter Physics, particularly in quasi-one dimensional and quasi-two dimensional crystals, organic conductors, and high-Tc superconducting cuprates. His research involves the use of nuclear magnetic resonance (NMR) and magnetotransport techniques to study the properties of these materials under various conditions such as high pressure and magnetic fields.\n", "\n", "2. **<PERSON><PERSON><PERSON>**:\n", "   - **Research Focus**: Experimental Condensed Matter Physics, focusing on non-equilibrium states and properties of matter, particularly in two-dimensional materials and quantum systems. His lab uses ultrafast electron diffraction and time-resolved second harmonic generation to study and control various phases of matter, including charge density waves and superconductors.\n", "\n", "3. **<PERSON><PERSON>**:\n", "   - **Research Focus**: Experimental Condensed Matter Physics, with a focus on magnetic resonance techniques applied to condensed matter physics problems. His research includes studying spin density waves and related phases in organic conductors, and developing nuclear magnetic resonance instrumentation for high magnetic field experiments.\n", "\n", "While these professors do not specifically mention quantum computing, quantum devices, or topological quantum materials in their provided research descriptions, their work in experimental condensed matter physics, particularly in two-dimensional materials and spintronics-related areas, aligns with the broader field of interest.\n", "\n", "**Recent Publications** are not listed in the provided sources, but you can find them by visiting the professors' websites or searching academic databases like Google Scholar or arXiv using their names.\n", "\n", "For more detailed information on recent publications and specific research projects, it is recommended to visit the UCLA Physics & Astronomy department's website or contact the professors directly. \n", "\n", "**Additional Note**: The UCLA Physics & Astronomy department has a broad range of research activities, including experimental condensed matter physics, which covers topics such as novel electronic materials, correlated electron systems, unconventional superconductivity, and quantum magnetism. However, specific faculty members and their recent publications are best found through the department's faculty listings and individual faculty websites.\n", "\n", "\n", "<PERSON><PERSON> Template for Several faculty members at the University of California, Los Angeles (UCLA) are involved in Experimental Condensed Matter Physics research related to two-dimensional materials, quantum computing, quantum devices, spintronics, direct electronic measurement methods, and topological quantum materials. Here are a few relevant professors::\n", "Here is a professional email tailored to the specific research interests and faculty members at UCLA:\n", "\n", "---\n", "\n", "**Email to Professor <PERSON><PERSON><PERSON>**\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program, I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work on non-equilibrium states of matter, particularly using ultrafast electron diffraction and time-resolved second harmonic generation, aligns closely with my research interests in condensed matter physics. I am also intrigued by your focus on exotic and out-of-equilibrium states of matter, which resonates with my current research experience in topological semimetals. I would be very interested in participating in developments for further techniques in direct electronic measurements and exploring the properties of various materials under non-equilibrium conditions.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would like to inquire if you will be taking new graduate students into your lab next fall.\n", "\n", "Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "**Email to Professor <PERSON>**\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program, I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work on unconventional superconductors and the use of Zeeman-perturbed quadrupole resonance to detect time-reversal symmetry breaking aligns closely with my research interests in condensed matter physics. I am particularly interested in the experimental techniques you employ to study these phenomena and how they could be applied to further understand topological quantum materials.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would like to inquire if you will be taking new graduate students into your lab next fall.\n", "\n", "Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "**Email to Other Relevant Faculty Members**\n", "\n", "Please adjust the second part of the email to match the specific research interests and projects of the faculty member you are contacting, using the information provided in the links. Ensure to highlight how your current research experience and interests align with their work and express your interest in contributing to their group. \n", "\n", "For example, if contacting a faculty member involved in quantum computing and quantum devices, you might write:\n", "\n", "\"Your work on quantum computing and quantum devices, particularly in the context of two-dimensional materials and topological quantum materials, aligns closely with my research interests in condensed matter physics. I am intrigued by the potential applications of these materials in quantum computing and would be very interested in participating in developments for further techniques in direct electronic measurements and exploring the properties of various materials under non-equilibrium conditions.\"\n", "\n", "Remember to attach your CV and inquire about potential openings for new graduate students in their lab next fall. \n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "**General Structure for Other Faculty Members**\n", "\n", "Dear Professor [Faculty Member's Name],\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program, I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work on [Specific Area of Research Interest, e.g., two-dimensional materials, quantum computing, spintronics, direct electronic measurement methods, topological quantum materials] aligns closely with my research interests in condensed matter physics. I am particularly interested in [Specific Aspect of Their Research That Aligns with Your Interests].\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would like to inquire if you will be taking new graduate students into your lab next fall.\n", "\n", "Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "Please ensure to customize the email for each faculty member based on their specific research interests and projects.\n", "\n", "\n", "<PERSON><PERSON> Template for 1. **<PERSON>**::\n", "Given the information about Professor <PERSON>, it appears that he is not involved in research related to two-dimensional materials, quantum computing, quantum devices, spintronics, direct electronic measurement methods, or topological quantum materials. Instead, his work focuses on the importance of play in human and animal behavior. Therefore, it would not be appropriate to send an email expressing interest in joining his lab for research in those areas.\n", "\n", "However, to follow the instructions and provide a response based on the example given, here is a rewritten email that acknowledges the mismatch in research interests:\n", "\n", "---\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I initially thought your research might align with my interests.\n", "\n", "Upon further review, I realize that your work focuses on the importance of play in human and animal behavior, which is quite different from my research background in condensed matter physics. I appreciate the significance of your research in understanding the role of play in cognitive and emotional development, but I do not see a direct connection to my current research interests.\n", "\n", "I've attached my CV for your consideration, though I understand that my background may not be a good fit for your lab. I wish to express my admiration for your contributions to the field of play research and its broader implications for human well-being.\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "Please note that this email is not intended to be sent as it does not align with Professor <PERSON>'s research interests. It is crucial to research and identify professors whose work closely matches your own research interests before reaching out.\n", "\n", "\n", "<PERSON><PERSON> Template for 2. **<PERSON><PERSON><PERSON>**::\n", "Subject: Expressed Interest in Joining Your Research Group\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work on exotic and out-of-equilibrium states of matter, particularly using ultrafast electron diffraction and time-resolved second harmonic generation, aligns closely with my research interests in condensed matter physics. I am particularly intrigued by the potential applications of these techniques in studying two-dimensional materials and topological quantum materials. My current research experience in analyzing band structures and exploring topological phenomena in quantum materials makes me believe that I could contribute meaningfully to your group.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would like to inquire if you will be taking new graduate students into your lab next fall. Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "[Attach CV]\n", "\n", "\n", "<PERSON><PERSON>mplate for 3. **<PERSON><PERSON>**::\n", "Subject: Expressed Interest in Joining Your Research Group\n", "\n", "Dear Professor <PERSON><PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work in experimental condensed matter physics, particularly in the application of magnetic resonance techniques to study spin density waves and vortex motion in superconductors, aligns with my interests in quantum materials and direct electronic measurement methods. I am particularly intrigued by the potential applications of these techniques to topological quantum materials. I would be very interested in participating in developments for further techniques in direct electronic measurements and exploring how these methods can be applied to study quantum phenomena in condensed matter systems.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would like to inquire if you will be taking new graduate students into your lab next fall.\n", "\n", "Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "Attachments: CV - <PERSON>.pdf\n", "\n", "---\n", "\n", "Note: The provided information about Professor <PERSON><PERSON>'s research does not mention two-dimensional materials, nanoSQUID Microscopy, or direct electronic band measurements. Therefore, the email has been rewritten to focus on his actual research interests in experimental condensed matter physics, particularly in magnetic resonance techniques and their applications to quantum materials.\n", "\n", "\n", "Em<PERSON> Template for While these professors do not specifically mention quantum computing, quantum devices, or topological quantum materials in their provided research descriptions, their work in experimental condensed matter physics, particularly in two-dimensional materials and spintronics-related areas, aligns with the broader field of interest.:\n", "Subject: Expressed Interest in Research Opportunities in Your Lab\n", "\n", "Dear Professor [Professor's Name],\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work in experimental condensed matter physics, particularly in two-dimensional materials and spintronics-related areas, aligns closely with my research interests and current research experience in condensed matter physics. I am particularly intrigued by the potential applications of these materials in quantum computing and quantum devices, and I believe that your research in these areas could provide valuable insights into the development of topological quantum materials. I would be very interested in participating in developments for further techniques in direct electronic measurement methods and exploring the properties of these materials.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would like to inquire if you will be taking new graduate students into your lab next fall.\n", "\n", "Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "\n", "Email Template for **Recent Publications** are not listed in the provided sources, but you can find them by visiting the professors' websites or searching academic databases like Google Scholar or arXiv using their names.:\n", "Given the research interests and the professors listed in the provided sources, the most relevant professor to contact based on the specified interests (two-dimensional materials, quantum computing and quantum devices, spintronics, direct electronic measurement methods, topological quantum materials) would be **<PERSON>**, who is involved in condensed matter theory and computational physics, which can relate to some of these areas.\n", "\n", "Here is a professional email tailored to Professor <PERSON>:\n", "\n", "---\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work in condensed matter theory and computational physics aligns closely with my research interests, particularly in the context of two-dimensional materials and topological quantum materials. I am also very intrigued by the potential applications of computational methods in understanding quantum phenomena and spintronics. I would be very interested in participating in developments for further techniques in direct electronic band measurements and exploring how computational physics can contribute to advancements in quantum computing and quantum devices.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would like to inquire if you will be taking new graduate students into your lab next fall. Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "This email maintains the structure and tone of the example provided while tailoring the content to Professor <PERSON>'s research interests and your own background and interests.\n", "\n", "\n", "Email Template for For more detailed information on recent publications and specific research projects, it is recommended to visit the UCLA Physics & Astronomy department's website or contact the professors directly. :\n", "Given the research interests and the structure of the example email, here is a professional email tailored to the specific interests and the faculty at UCLA Physics & Astronomy:\n", "\n", "---\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work on experimental condensed matter physics, particularly in the area of unconventional superconductors and the exploration of chiral states, aligns closely with my research interests. I am also intrigued by your project on Zeeman-perturbed quadrupole resonance as a method for detecting time reversal symmetry breaking (TRSB). My background in ARPES and experience with data analysis and machine learning techniques could be valuable in contributing to your research on topological quantum materials and spintronics. I would be very interested in participating in developments for further techniques in direct electronic measurements and exploring the applications of quantum computing and quantum devices in your research.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would appreciate knowing if you will be taking new graduate students into your lab next fall. Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "This email is tailored to Professor <PERSON>'s research interests as mentioned in the UCLA Physics & Astronomy department's research areas and projects. The specific mention of experimental condensed matter physics, unconventional superconductors, and Zeeman-perturbed quadrupole resonance is relevant to Professor <PERSON>'s work and aligns with the provided research interests.\n", "\n", "\n", "Email Template for **Additional Note**: The UCLA Physics & Astronomy department has a broad range of research activities, including experimental condensed matter physics, which covers topics such as novel electronic materials, correlated electron systems, unconventional superconductivity, and quantum magnetism. However, specific faculty members and their recent publications are best found through the department's faculty listings and individual faculty websites.:\n", "Given the specific interests in two-dimensional materials, quantum computing, quantum devices, spintronics, direct electronic measurement methods, and topological quantum materials, it's essential to identify a faculty member whose research aligns with these areas. However, the provided links do not directly mention faculty members with these specific research interests. Therefore, I will craft a general email that can be adapted to a faculty member whose research aligns with these interests.\n", "\n", "---\n", "\n", "Dear Professor [Faculty Member's Name],\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program, I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work on [specific area of research, e.g., two-dimensional materials, quantum computing, spintronics] aligns closely with my research interests and my current research experience in condensed matter physics. I am particularly intrigued by the potential applications of [specific technique or area of interest, e.g., direct electronic band measurements, topological quantum materials]. I would be very interested in participating in developments for further techniques in these areas.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would appreciate it if you could inform me whether you will be taking new graduate students into your lab next fall.\n", "\n", "Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "To find the appropriate faculty member, I recommend visiting the UCLA Physics & Astronomy department's faculty listings and individual faculty websites to identify researchers whose work aligns with your interests. Once you have identified the relevant faculty member, you can replace [Faculty Member's Name] and [specific area of research] with the appropriate information.\n", "\n", "\n"]}], "source": ["import requests\n", "import json\n", "import PyPDF2\n", "\n", "\n", "def get_perplexity_response(query):\n", "    url = \"https://api.perplexity.ai/chat/completions\"\n", "    headers = {\n", "        \"Authorization\": \"Bearer 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\",\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "    payload = {\n", "        \"model\": \"llama-3.1-sonar-huge-128k-online\",\n", "        \"messages\": [{\"role\": \"user\", \"content\": query}]\n", "    }\n", "    \n", "    try:\n", "        response = requests.post(url, json=payload, headers=headers)\n", "        response.raise_for_status()\n", "        result = response.json()\n", "        return result['choices'][0]['message']['content']\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"Request failed: {e}\")\n", "        return None\n", "    except KeyError as e:\n", "        print(f\"Unexpected response structure: {e}\")\n", "        print(f\"Full response: {result}\")\n", "        return None\n", "\n", "import requests\n", "import json\n", "import PyPDF2\n", "\n", "\n", "def read_cv(cv_path):\n", "    try:\n", "        if cv_path.lower().endswith('.pdf'):\n", "            with open(cv_path, 'rb') as file:\n", "                reader = PyPDF2.PdfReader(file)\n", "                text = \"\"\n", "                for page in reader.pages:\n", "                    text += page.extract_text() + \"\\n\"\n", "                return text\n", "        else:\n", "            with open(cv_path, 'r', encoding='utf-8') as file:\n", "                return file.read()\n", "    except FileNotFoundError:\n", "        print(f\"CV file not found at {cv_path}\")\n", "        return None\n", "    except Exception as e:\n", "        print(f\"Error reading CV file: {e}\")\n", "        return None\n", "\n", "\n", "\n", "# Main pr\n", "\n", "def research_professors(school, field, interests, cv_path, example_email):\n", "    cv_content = read_cv(cv_path)\n", "    if cv_content is None:\n", "        return\n", "\n", "    query = f\"Use the internet to find information on professors at {school}, ensure they are faculty in the physic department at that school who research topics in {field} related to {interests}. Include their name, research focus, and recent publications.\"\n", "    professors_info = get_perplexity_response(query)\n", "    \n", "    if professors_info is None:\n", "        print(\"Unable to retrieve professor information.\")\n", "        return\n", "    \n", "    print(\"Professor Information:\")\n", "    print(professors_info)\n", "    print(\"\\n\")\n", "    \n", "    professors = professors_info.split(\"\\n\\n\")\n", "    for professor in professors:\n", "        if professor.strip():\n", "            name = professor.split(\"\\n\")[0]\n", "            email_template = generate_email(name, interests, cv_content, example_email)\n", "            if email_template:\n", "                print(f\"Email Template for {name}:\")\n", "                print(email_template)\n", "                print(\"\\n\")\n", "\n", "def generate_email(professor_name, interests, cv_content, example_email):\n", "    query = f\"\"\"\n", "    Write a professional email to Professor {professor_name} expressing interest in their research based on the following interests: {interests}. \n", "    Use the following CV content for context on research experience: \n", "    {cv_content}\n", "    \n", "    Base the structure and tone of the email on this example while keeping the first section of the email exactly the same, and using the information gathered about the professors research to rewrite the second part, only mentioning parts of the provided research interests revelant to their research:\n", "    {example_email}\n", "    \n", "    Ask if they will be taking new graduate students into their lab next fall.\n", "    \"\"\"\n", "    email_template = get_perplexity_response(query)\n", "    return email_template\n", "\n", "# Main program\n", "def main():\n", "    school = \"University of California, Los Angeles\"\n", "    field = \"Experimental Condensed Matter Physics\"\n", "    interests = \"Two-dimensional materials, Quantum Computing and Quantum Devices, Spintronics, Direct Electronic Measurement Methods, topological quantum materials\"\n", "    cv_path = \"/home/<USER>/Documents/HadsellT-CV.pdf\"  # Replace with the actual path to your CV\n", "    \n", "    example_email = \"\"\"\n", "    Dear Professor <PERSON>,\n", "\n", "    I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "    Your work on two-dimensional materials aligns closely with my research interests, and my current research experience in condensed matter physics. I am also very intrigued in your nanoSQUID Microscopy methods. I would be very interested in participating in developments for further techniques in direct electronic band measurements.\n", "\n", "    I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Thank you for your time!\n", "\n", "    Best regards,\n", "\n", "\n", "    <PERSON> (he/him/they/them)\n", "    M.S. Physics Candidate\n", "    California State University, Long Beach\n", "\n", "    \"\"\"\n", "    \n", "    research_professors(school, field, interests, cv_path, example_email)\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mDEPRECATION: Loading egg at /home/<USER>/miniconda3/envs/mainenv/lib/python3.12/site-packages/perplexityai-0.1-py3.12.egg is deprecated. pip 24.3 will enforce this behaviour change. A possible replacement is to use pip for package installation.. Discussion can be found at https://github.com/pypa/pip/issues/12330\u001b[0m\u001b[33m\n", "\u001b[0mRequirement already satisfied: PyPDF2 in ./miniconda3/envs/mainenv/lib/python3.12/site-packages (3.0.1)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install PyPDF2\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["Professor Information:\n", "Several professors at the University of California, <PERSON> (UCSB) are involved in research related to Experimental Condensed Matter Physics, focusing on topics such as Two-dimensional materials, Quantum Computing, Quantum Information, Spintronics, and Direct Electronic Measurement Methods. Here are some key faculty members and their research areas:\n", "\n", "1. **<PERSON>**:\n", "   - **Research Focus**: Spintronics, Quantum Information, and Two-dimensional materials.\n", "   - **Recent Publications**: His work includes research on spin dynamics in two-dimensional materials and their applications in quantum computing and information processing.\n", "\n", "2. **<PERSON><PERSON>**:\n", "   - **Research Focus**: Quantum Information Science, Experimental Condensed Matter Physics, and Nanoscale Magnetic Resonance.\n", "   - **Recent Publications**: Her research includes developing techniques for direct electronic measurement methods and exploring quantum phenomena in condensed matter systems.\n", "\n", "3. **<PERSON>**:\n", "   - **Research Focus**: Theoretical Condensed Matter Physics, with interests in quantum phases of matter, including topological phases and unconventional superconductivity.\n", "   - **Recent Publications**: His work includes theoretical studies on quantum phases and their implications for quantum computing and information processing.\n", "\n", "4. **<PERSON><PERSON><PERSON>**:\n", "   - **Research Focus**: Theoretical Condensed Matter Physics, focusing on strongly correlated electron systems, unconventional quantum phase transitions, and topological phases.\n", "   - **Recent Publications**: His research includes theoretical investigations of quantum many-body systems and their potential applications in quantum information and computing.\n", "\n", "5. **<PERSON><PERSON>**:\n", "   - **Research Focus**: Theoretical Condensed Matter Physics, with a focus on quantum field theory, quantum information, and condensed matter physics.\n", "   - **Recent Publications**: His work includes theoretical studies on quantum phases and their implications for quantum computing and information processing.\n", "\n", "These faculty members are part of the Department of Physics at UCSB and have contributed significantly to the fields of Experimental Condensed Matter Physics, Quantum Computing, and Quantum Information.\n", "\n", "**References**:\n", "- https://en.wikipedia.org/wiki/List_of_University_of_California,_Santa_Barbara_faculty\n", "- https://history.aip.org/phn/21604002.html\n", "- https://en.wikipedia.org/wiki/UCSB_Physics_Department\n", "- https://sites.google.com/view/xucenkewebsite/home\n", "- https://www.physics.ucsb.edu/people/faculty\n", "\n", "**Note**: The specific recent publications are not listed here due to the nature of the query, but the provided links and faculty profiles can be used to access their recent publications.\n", "\n", "\n", "Email Template for Several professors at the University of California, <PERSON> (UCSB) are involved in research related to Experimental Condensed Matter Physics, focusing on topics such as Two-dimensional materials, Quantum Computing, Quantum Information, Spintronics, and Direct Electronic Measurement Methods. Here are some key faculty members and their research areas::\n", "Subject: Expressed Interest in Experimental Condensed Matter Physics Research at UCSB\n", "\n", "Dear Professors,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your labs, as I'm very interested in the opportunity to join your groups as a Ph.D. student next fall.\n", "\n", "Your research in Experimental Condensed Matter Physics at UCSB, particularly in areas such as two-dimensional materials, quantum computing, quantum information, spintronics, and direct electronic measurement methods, aligns closely with my research interests and current experience in condensed matter physics. I am particularly intrigued by the potential applications of these topics in advancing our understanding of quantum phenomena and developing new materials and devices. My background in ARPES and experience with data analysis and machine learning techniques could be valuable contributions to your research efforts.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your groups if you have a moment. Additionally, I would like to inquire if you will be taking new graduate students into your labs next fall.\n", "\n", "Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "[Attach CV]\n", "\n", "\n", "<PERSON><PERSON>late for 1. **<PERSON>**::\n", "Subject: Expressed Interest in Research Opportunities in Your Lab\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work in quantum spintronics, particularly in understanding and controlling the spins of electrons, nuclei, and photons in semiconductors and molecules, aligns closely with my research interests in condensed matter physics. I am also very intrigued by your research on quantum information processing and its potential applications in computing, communication, and sensing. My current research experience in ARPES and my background in data analysis and machine learning could complement your group's efforts in exploring and controlling quantum states in various materials. I would be very interested in participating in developments for further techniques in direct electronic measurement methods and contributing to your ongoing projects in spintronics and quantum information engineering.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would like to inquire if you will be taking new graduate students into your lab next fall. Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "[Attach CV]\n", "\n", "\n", "<PERSON><PERSON>late for 2. **<PERSON><PERSON>**::\n", "Subject: Expressed Interest in Joining Your Research Group\n", "\n", "Dear Professor <PERSON><PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work on quantum sensing and imaging, particularly in the context of solid-state quantum sensors and the nitrogen-vacancy (NV) center in diamond, aligns closely with my research interests in quantum computing and quantum information. I am also very intrigued by your research on spintronics and the application of quantum sensing techniques to study condensed matter systems. My current research experience in condensed matter physics, specifically in analyzing band structures in two-dimensional materials like PdTe2 and PtTe2, complements your group's focus on quantum effects on the nanoscale. I would be very interested in participating in developments for further techniques in direct electronic measurement methods, especially those leveraging quantum phenomena.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would like to inquire if you will be taking new graduate students into your lab next fall. Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "\n", "<PERSON><PERSON> Template for 3. **<PERSON>**::\n", "Subject: Expressed Interest in Joining Your Research Group\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work on two-dimensional materials, particularly in the context of Van <PERSON> materials and the exploration of exotic quantum phases, resonates deeply with my research interests and current experience in condensed matter physics. I am also intrigued by your research on quantum spin liquids and topological spintronics, which aligns with my broader interests in quantum computing and quantum information. Additionally, your collaborations with experimentalists such as <PERSON> and <PERSON><PERSON>, focusing on real-world measurements in the lab, are particularly appealing to me. I would be very interested in contributing to and learning from these efforts, especially in developing direct electronic measurement methods.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Specifically, I would like to inquire if you will be taking new graduate students into your lab next fall.\n", "\n", "Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "[Attach CV]\n", "\n", "\n", "<PERSON><PERSON>late for 4. **<PERSON><PERSON><PERSON>**::\n", "Subject: Expressed Interest in Joining Your Research Group\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work on two-dimensional materials, particularly in the context of quantum criticality and topological phases, aligns closely with my research interests. I am also very intrigued by your theoretical studies on twisted multilayer graphene and the potential for topological superconductivity, which have significant implications for quantum computing and quantum information. My current research experience in condensed matter physics, including the use of ARPES for studying band structures, would be a valuable foundation for contributing to your research group. I am particularly interested in exploring how direct electronic measurement methods can be applied to study quantum phenomena in 2D materials.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would like to inquire if you will be taking new graduate students into your lab next fall.\n", "\n", "Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "Attachments: CV - <PERSON>.pdf\n", "\n", "\n", "<PERSON><PERSON> Template for 5. **<PERSON><PERSON>**::\n", "Subject: Expressed Interest in Joining Your Research Group\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work at the intersection of quantum condensed matter physics and quantum information science, particularly in areas such as far-from-equilibrium quantum dynamics and quantum error correction, aligns closely with my research interests. My current research experience in condensed matter physics, focusing on the analysis of band structures in two-dimensional materials like PdTe2 and PtTe2, has provided me with a strong foundation that I believe would be valuable in your lab. I am particularly intrigued by your research on Majorana fermions and their applications in quantum computing and error correction, as well as your recent work on adaptive quantum dynamics and mixed-state long-range order. I would be very interested in participating in developments for further techniques in quantum information processing and exploring the potential of these systems for robust quantum computation.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would appreciate knowing if you will be taking new graduate students into your lab next fall.\n", "\n", "Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "[Attach CV]\n", "\n", "\n", "Email Template for These faculty members are part of the Department of Physics at UCSB and have contributed significantly to the fields of Experimental Condensed Matter Physics, Quantum Computing, and Quantum Information.:\n", "Here is a professional email tailored to the faculty members at UCSB with research interests in Experimental Condensed Matter Physics, Quantum Computing, and Quantum Information:\n", "\n", "---\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work on van der W<PERSON>s heterostructures and quantum Hall phases resonates deeply with my research interests in two-dimensional materials and condensed matter physics. I am particularly fascinated by your use of nanofabrication and electronic measurement techniques to investigate the properties of electronic states in quantum materials, especially the interplay between symmetry, topology, and correlations in low-dimensional systems. I would be very interested in contributing to and learning from your research on these topics, particularly in the development of novel heterostructure fabrication techniques and cryogenic measurements.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would appreciate it if you could let me know if you will be taking new graduate students into your lab next fall. Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "This email maintains the same structure and tone as the example provided, while incorporating specific details about Professor <PERSON>'s research interests and how they align with your own research background and interests.\n", "\n", "\n", "Email Template for **References**::\n", "Here is a professional email tailored to the provided research interests and CV content:\n", "\n", "---\n", "\n", "Dear Professor [Professor's Name],\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work on two-dimensional materials, quantum computing, and quantum information aligns closely with my research interests and current research experience in condensed matter physics. I am particularly intrigued by the potential applications of spintronics and direct electronic measurement methods in these fields. My experience with ARPES and data analysis using Python could be valuable in contributing to your research projects. I would be very interested in participating in developments for further techniques in direct electronic band measurements and exploring how these methods can be applied to quantum materials.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Additionally, I would appreciate knowing if you will be taking new graduate students into your lab next fall.\n", "\n", "Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "Please note that the professor's name should be replaced with the actual name of the professor you are contacting. Also, ensure that the email is tailored to the specific research interests and projects of the professor you are contacting, as the example provided does not specify a particular professor's research.\n", "\n", "\n", "Email Template for **Note**: The specific recent publications are not listed here due to the nature of the query, but the provided links and faculty profiles can be used to access their recent publications.:\n", "Given the specific research interests and the faculty profiles provided, the most relevant professor to contact would be **<PERSON>** from California State University, Long Beach, who specializes in condensed matter theory and computational physics. Here is a tailored email based on the provided information:\n", "\n", "---\n", "\n", "Dear Professor <PERSON>,\n", "\n", "I hope this email finds you well. My name is <PERSON>, I completed my B.S. in Astrophysics at UCLA in 2023 and I am currently finishing my M.S. in Physics at California State University, Long Beach. During my current program I have been conducting research on topological semimetals using ARPES with <PERSON><PERSON> <PERSON> as part of the Nanoelectronics group here at CSULB. I'm writing to inquire about potential openings in your lab, as I'm very interested in the opportunity to join your group as a Ph.D. student next fall.\n", "\n", "Your work in condensed matter theory and computational physics, particularly in areas that could intersect with quantum computing and quantum information, aligns closely with my research interests. My current research experience in condensed matter physics, including the study of band structures in PdTe2, PtTe2, and Cr-alloyed PtTe2 using ARPES, has provided me with a strong foundation in experimental techniques. I am also very intrigued by the potential applications of computational methods in understanding spintronics and direct electronic measurement methods. I would be very interested in participating in developments for further techniques in these areas.\n", "\n", "I've attached my CV for your consideration. I'd be very interested in discussing your work and my potential to contribute to your group if you have a moment. Specifically, I would like to inquire if you will be taking new graduate students into your lab next fall. Thank you for your time!\n", "\n", "Best regards,\n", "\n", "<PERSON> (he/him/they/them)\n", "M.S. Physics Candidate\n", "California State University, Long Beach\n", "\n", "---\n", "\n", "This email is tailored to Professor <PERSON>'s research interests and highlights the relevant aspects of your CV. It also includes the specific inquiry about potential openings for new graduate students in the lab next fall.\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "mainenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}