This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024/Arch Linux) (preloaded format=xelatex 2024.12.1)  14 DEC 2024 10:51
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/home/<USER>/WorkSpace/PhDApplications/CalTechSOP
(/home/<USER>/WorkSpace/PhDApplications/CalTechSOP.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count184
\c@section=\count185
\c@subsection=\count186
\c@subsubsection=\count187
\c@paragraph=\count188
\c@subparagraph=\count189
\c@figure=\count190
\c@table=\count191
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (/usr/share/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (/usr/share/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count192
\Gm@cntv=\count193
\c@Gm@tempcnt=\count194
\Gm@bindingoffset=\dimen141
\Gm@wd@mp=\dimen142
\Gm@odd@mp=\dimen143
\Gm@even@mp=\dimen144
\Gm@layoutwidth=\dimen145
\Gm@layoutheight=\dimen146
\Gm@layouthoffset=\dimen147
\Gm@layoutvoffset=\dimen148
\Gm@dimlist=\toks18
) (/usr/share/texmf-dist/tex/latex/fontspec/fontspec.sty (/usr/share/texmf-dist/tex/latex/l3packages/xparse/xparse.sty (/usr/share/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (/usr/share/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count195
\l__pdf_internal_box=\box51
\g__pdf_backend_object_int=\count196
\g__pdf_backend_annotation_int=\count197
\g__pdf_backend_link_int=\count198
))
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: fontspec 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
 (/usr/share/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count199
\l__fontspec_language_int=\count266
\l__fontspec_strnum_int=\count267
\l__fontspec_tmp_int=\count268
\l__fontspec_tmpa_int=\count269
\l__fontspec_tmpb_int=\count270
\l__fontspec_tmpc_int=\count271
\l__fontspec_em_int=\count272
\l__fontspec_emdef_int=\count273
\l__fontspec_strong_int=\count274
\l__fontspec_strongdef_int=\count275
\l__fontspec_tmpa_dim=\dimen149
\l__fontspec_tmpb_dim=\dimen150
\l__fontspec_tmpc_dim=\dimen151
 (/usr/share/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (/usr/share/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (/usr/share/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
)

Package fontspec Info: Font family 'TimesNewRoman(0)' created for font 'Times
(fontspec)             New Roman' with options [Ligatures=TeX].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"Times New
(fontspec)             Roman/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"Times New
(fontspec)             Roman/B/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"Times New
(fontspec)             Roman/I/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"Times New
(fontspec)             Roman/BI/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.: 

 (/usr/share/texmf-dist/tex/latex/titling/titling.sty
Package: titling 2009/09/04 v2.1d maketitle typesetting
\thanksmarkwidth=\skip50
\thanksmargin=\skip51
\droptitle=\skip52
)

Package fontspec Info: Could not resolve font "Playfair Display/B" (it
(fontspec)             probably doesn't exist).


Package fontspec Info: Font family 'PlayfairDisplay(0)' created for font
(fontspec)             'Playfair Display' with options [].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"Playfair
(fontspec)             Display/OT:script=latn;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: <->"Playfair
(fontspec)             Display/OT:script=latn;language=dflt;+smcp;"
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"Playfair
(fontspec)             Display/I/OT:script=latn;language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->"Playfair
(fontspec)             Display/I/OT:script=latn;language=dflt;+smcp;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"Playfair
(fontspec)             Display/BI/OT:script=latn;language=dflt;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->"Playfair
(fontspec)             Display/BI/OT:script=latn;language=dflt;+smcp;"

 (./CalTechSOP.aux)
\openout1 = `CalTechSOP.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 24.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 24.
 (/usr/share/texmf-dist/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 24.
LaTeX Font Info:    ... okay on input line 24.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 24.
LaTeX Font Info:    ... okay on input line 24.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 472.03123pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 652.70622pt, 71.13188pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=472.03123pt
* \textheight=652.70622pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 24.
LaTeX Font Info:    Redeclaring math accent \acute on input line 24.
LaTeX Font Info:    Redeclaring math accent \grave on input line 24.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 24.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 24.
LaTeX Font Info:    Redeclaring math accent \bar on input line 24.
LaTeX Font Info:    Redeclaring math accent \breve on input line 24.
LaTeX Font Info:    Redeclaring math accent \check on input line 24.
LaTeX Font Info:    Redeclaring math accent \hat on input line 24.
LaTeX Font Info:    Redeclaring math accent \dot on input line 24.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 24.
LaTeX Font Info:    Redeclaring math symbol \colon on input line 24.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 24.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 24.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 24.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 24.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 24.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 24.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 24.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 24.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 24.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 24.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 24.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 24.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 24.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 24.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/TimesNewRoman(0)/m/n on input line 24.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 24.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRoman(0)/m/n on input line 24.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/TimesNewRoman(0)/m/n --> TU/TimesNewRoman(0)/m/n on input line 24.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/TimesNewRoman(0)/m/it on input line 24.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRoman(0)/b/n on input line 24.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 24.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 24.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/TimesNewRoman(0)/m/n --> TU/TimesNewRoman(0)/b/n on input line 24.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/TimesNewRoman(0)/b/it on input line 24.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 24.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 24.
[1

]
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <12> on input line 41.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <8> on input line 41.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <6> on input line 41.

Overfull \hbox (8.69873pt too wide) in paragraph at lines 43--44
[]\TU/TimesNewRoman(0)/m/n/12 Caltech’s leadership in quantum materials research, particularly through the work of Prof. Hsieh’s,
 []

[2] (./CalTechSOP.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********
 ) 
Here is how much of TeX's memory you used:
 3740 strings out of 476772
 111628 string characters out of 5803394
 1932839 words of memory out of 5000000
 25754 multiletter control sequences out of 15000+600000
 560973 words of font info for 66 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 79i,6n,93p,1929b,236s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on CalTechSOP.pdf (2 pages).
