This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024/Arch Linux) (preloaded format=pdflatex 2024.8.31)  15 OCT 2024 19:45
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/home/<USER>/WorkSpace/PhDApplications/PersonalStatement.tex
(/home/<USER>/WorkSpace/PhDApplications/PersonalStatement.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count188
\c@section=\count189
\c@subsection=\count190
\c@subsubsection=\count191
\c@paragraph=\count192
\c@subparagraph=\count193
\c@figure=\count194
\c@table=\count195
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (/usr/share/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (/usr/share/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count196
\Gm@cntv=\count197
\c@Gm@tempcnt=\count198
\Gm@bindingoffset=\dimen141
\Gm@wd@mp=\dimen142
\Gm@odd@mp=\dimen143
\Gm@even@mp=\dimen144
\Gm@layoutwidth=\dimen145
\Gm@layoutheight=\dimen146
\Gm@layouthoffset=\dimen147
\Gm@layoutvoffset=\dimen148
\Gm@dimlist=\toks18
) (/usr/share/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
) (/usr/share/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count199
\l__pdf_internal_box=\box51
) (./PersonalStatement.aux)
\openout1 = `PersonalStatement.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 6.
LaTeX Font Info:    ... okay on input line 6.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

[1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}] [2] (./PersonalStatement.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********
 ) 
Here is how much of TeX's memory you used:
 1074 strings out of 476076
 18462 string characters out of 5793775
 1936187 words of memory out of 5000000
 23240 multiletter control sequences out of 15000+600000
 558972 words of font info for 39 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 57i,5n,65p,498b,151s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb>
Output written on PersonalStatement.pdf (2 pages, 36299 bytes).
PDF statistics:
 21 PDF objects out of 1000 (max. 8388607)
 12 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

