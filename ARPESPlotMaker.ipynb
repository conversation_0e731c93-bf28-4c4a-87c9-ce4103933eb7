{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/pyarpesenv/lib/python3.8/site-packages/arpes/config.py:54: UserWarning: Could not find local configuration file. If you don't have one, you can safely ignore this message.\n", "  warnings.warn(msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Activating auto-logging. Current session state plus future input saved.\n", "Filename       : logs/unnamed_2024-06-21_10-16-37.log\n", "Mode           : backup\n", "Output logging : False\n", "Raw input log  : False\n", "Timestamping   : <PERSON><PERSON><PERSON>\n", "State          : active\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5ebf4e7b80e34e278102d2bdda9028f3", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(interactive(children=(IntSlider(value=1, description='Scan Index', layout=Layout(width='50%'), …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import numpy as np\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox\n", "from matplotlib.colors import Normalize, ListedColormap\n", "from IPython.display import display\n", "\n", "# Your existing code to load the data\n", "from arpes.load_pxt import *\n", "from arpes.io import *\n", "from arpes import *\n", "from arpes.utilities import *\n", "import warnings\n", "from matplotlib import MatplotlibDeprecationWarning\n", "\n", "if not hasattr(np, 'complex'):\n", "    np.complex = np.complex128\n", "\n", "# Set the font family for all text elements\n", "plt.rcParams['font.family'] = 'sans-serif'\n", "plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\n", "plt.rcParams.update({\n", "    \"text.usetex\": True,\n", "    \"font.family\": \"sans-serif\",\n", "    \"font.sans-serif\": \"Helvetica\",\n", "})\n", "\n", "def load_data_files(folder_path):\n", "    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n", "    data_files.sort()  # Sort files alphabetically\n", "    return [os.path.join(folder_path, f) for f in data_files]\n", "# Suppress specific warnings\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n", "warnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n", "def kspace(data_files, work_function):\n", "    # Constants\n", "    hbar = 6.582119569e-16  # eV*s\n", "    m_e = 9.1093837015e-31  # kg\n", "\n", "    # Pre-calculate all plots\n", "    all_plots = []\n", "    max_data_value = 0\n", "\n", "    for file_path in data_files:\n", "        data = read_single_pxt(file_path)\n", "        E_photon = data.attrs['hv']  # Photon energy from the attributes\n", "\n", "        # Calculate kinetic energy and momentum\n", "        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n", "        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n", "\n", "        # Get the energy values\n", "        energy_values = -data.eV.values  # Negative energy values\n", "\n", "        all_plots.append({\n", "            'k_parallel': k_parallel,\n", "            'energy_values': energy_values,\n", "            'data_values': data.values,\n", "            'file_name': os.path.basename(file_path)\n", "        })\n", "\n", "        max_data_value = max(max_data_value, np.max(data.values))\n", "\n", "    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n", "        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n", "\n", "        fig, ax = plt.subplots(figsize=(10, 8))\n", "    \n", "        # Create a custom normalization\n", "        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n", "    \n", "        # Create a colormap with the specified range\n", "        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n", "        cmap = ListedColormap(cmap_array)\n", "    \n", "        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], \n", "                           shading='auto', cmap=cmap, norm=norm)\n", "        cbar = fig.colorbar(im, ax=ax)\n", "        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n", "        cbar.ax.tick_params(labelsize=10)\n", "        \n", "        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n", "        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n", "        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n", "        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits\n", "        \n", "        # Adjust tick label font size\n", "        ax.tick_params(axis='both', which='major', labelsize=10)\n", "        \n", "        plt.tight_layout()\n", "        return fig\n", "\n", "    def save_plot(b):\n", "        # Get current slider values directly from the widgets\n", "        current_values = {\n", "            'scan_index': interactive_plot.children[0].value,\n", "            'vmin': interactive_plot.children[1].value,\n", "            'vmax': interactive_plot.children[2].value,\n", "            'scale': interactive_plot.children[3].value,\n", "            'cmap_start': interactive_plot.children[4].value,\n", "            'cmap_end': interactive_plot.children[5].value\n", "        }\n", "        \n", "        # Generate the plot with current values\n", "        fig = plot_arpes(**current_values)\n", "        \n", "        # Save the plot\n", "        filename = f\"ARPES_plot_scan_{current_values['scan_index']}.png\"\n", "        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n", "        plt.close(fig)\n", "        print(f\"Plot saved as {filename}\")\n", "    # Create the interactive widget\n", "    interactive_plot = interactive(plot_arpes,\n", "                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n", "                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),\n", "                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),\n", "                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),\n", "                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),\n", "                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))\n", "\n", "    # Create a save button\n", "    save_button = Button(description=\"Save Plot\")\n", "    save_button.on_click(save_plot)\n", "\n", "    # Combine the interactive plot and the save button\n", "    output = VBox([interactive_plot, save_button])\n", "    return output\n", "# Usage\n", "folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\n", "data_files = load_data_files(folder_path)\n", "work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n", "\n", "interactive_plot_with_save = kspace(data_files, work_function)\n", "display(interactive_plot_with_save)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5fb6f68ba71a4b60aa9c1ae4ca9fa7dd", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(interactive(children=(IntSlider(value=1, description='Scan Index', layout=Layout(width='50%'), …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import numpy as np\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox\n", "from matplotlib.colors import Normalize, ListedColormap\n", "from IPython.display import display\n", "\n", "# Your existing code to load the data\n", "from arpes.load_pxt import *\n", "from arpes.io import *\n", "from arpes import *\n", "from arpes.utilities import *\n", "import warnings\n", "from matplotlib import MatplotlibDeprecationWarning\n", "\n", "if not hasattr(np, 'complex'):\n", "    np.complex = np.complex128\n", "\n", "# Set the font family for all text elements\n", "plt.rcParams['font.family'] = 'sans-serif'\n", "plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\n", "plt.rcParams.update({\n", "    \"text.usetex\": True,\n", "    \"font.family\": \"sans-serif\",\n", "    \"font.sans-serif\": \"Helvetica\",\n", "})\n", "\n", "def load_data_files(folder_path):\n", "    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n", "    data_files.sort()  # Sort files alphabetically\n", "    return [os.path.join(folder_path, f) for f in data_files]\n", "# Suppress specific warnings\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n", "warnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n", "\n", "def mdc_plot(data_files, work_function):\n", "    # Constants\n", "    hbar = 6.582119569e-16  # eV*s\n", "    m_e = 9.1093837015e-31  # kg\n", "\n", "    # Pre-calculate all plots\n", "    all_plots = []\n", "    global_energy_min = float('inf')\n", "    global_energy_max = float('-inf')\n", "\n", "    for file_path in data_files:\n", "        data = read_single_pxt(file_path)\n", "        E_photon = data.attrs['hv']  # Photon energy from the attributes\n", "\n", "        # Calculate kinetic energy and momentum\n", "        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n", "        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n", "\n", "        # Get the energy values\n", "        energy_values = -data.eV.values  # Negative energy values\n", "\n", "        all_plots.append({\n", "            'k_parallel': k_parallel,\n", "            'energy_values': energy_values,\n", "            'data_values': data.values,\n", "            'file_name': os.path.basename(file_path)\n", "        })\n", "\n", "        # Update global energy range\n", "        global_energy_min = min(global_energy_min, np.min(energy_values))\n", "        global_energy_max = max(global_energy_max, np.max(energy_values))\n", "\n", "    def plot_mdc(scan_index, mdc_energy):\n", "        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n", "\n", "        fig, ax = plt.subplots(figsize=(10, 6))\n", "\n", "        # Find the closest energy value to mdc_energy\n", "        energy_index = np.argmin(np.abs(plot_data['energy_values'] - mdc_energy))\n", "        actual_energy = plot_data['energy_values'][energy_index]\n", "\n", "        # Extract MDC\n", "        mdc = plot_data['data_values'][energy_index, :]\n", "        k_parallel = plot_data['k_parallel'][energy_index, :]\n", "\n", "        # Plot MDC\n", "        ax.plot(k_parallel, mdc)\n", "        ax.set_xlabel(r'$k_\\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')\n", "        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')\n", "        ax.set_title(f'MDC at E = {actual_energy:.2f} eV - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n", "\n", "        # Adjust tick label font size\n", "        ax.tick_params(axis='both', which='major', labelsize=10)\n", "\n", "        plt.tight_layout()\n", "        return fig\n", "\n", "    def save_plot(b):\n", "        # Get current slider values directly from the widgets\n", "        current_values = {\n", "            'scan_index': interactive_plot.children[0].value,\n", "            'mdc_energy': interactive_plot.children[1].value\n", "        }\n", "\n", "        # Generate the plot with current values\n", "        fig = plot_mdc(**current_values)\n", "\n", "        # Save the plot\n", "        filename = f\"MDC_plot_scan_{current_values['scan_index']}_energy_{current_values['mdc_energy']:.2f}.png\"\n", "        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n", "        plt.close(fig)\n", "        print(f\"Plot saved as {filename}\")\n", "\n", "    # Create the interactive widget\n", "    interactive_plot = interactive(plot_mdc,\n", "                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n", "                                   mdc_energy=FloatSlider(value=(global_energy_min + global_energy_max) / 2,\n", "                                                          min=global_energy_min,\n", "                                                          max=global_energy_max,\n", "                                                          step=(global_energy_max - global_energy_min) / 100,\n", "                                                          description='MDC Energy (eV)',\n", "                                                          continuous_update=True))\n", "\n", "    # Create a save button\n", "    save_button = Button(description=\"Save Plot\")\n", "    save_button.on_click(save_plot)\n", "\n", "    # Combine the interactive plot and the save button\n", "    output = VBox([interactive_plot, save_button])\n", "    return output\n", "\n", "# Usage\n", "folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\n", "data_files = load_data_files(folder_path)\n", "work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n", "\n", "interactive_plot_with_save = mdc_plot(data_files, work_function)\n", "display(interactive_plot_with_save)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "18b5203ac5c44841aa92a1abde0710f5", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(interactive(children=(IntSlider(value=1, description='Scan Index', layout=Layout(width='50%'), …"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Video saved as ARPES_video.mp4\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Video saved as ARPES_video.mp4\n"]}], "source": ["import os\n", "import numpy as np\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox\n", "from matplotlib.colors import Normalize, ListedColormap\n", "from IPython.display import display\n", "import matplotlib.animation as animation\n", "from arpes.load_pxt import *\n", "from arpes.io import *\n", "from arpes import *\n", "from arpes.utilities import *\n", "import warnings\n", "from matplotlib import MatplotlibDeprecationWarning\n", "\n", "if not hasattr(np, 'complex'):\n", "    np.complex = np.complex128\n", "\n", "# Set the font family for all text elements\n", "plt.rcParams['font.family'] = 'sans-serif'\n", "plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\n", "plt.rcParams.update({\n", "    \"text.usetex\": True,\n", "    \"font.family\": \"sans-serif\",\n", "    \"font.sans-serif\": \"Helvetica\",\n", "    \"text.color\": \"black\",  # Set default text color to black\n", "    \"axes.labelcolor\": \"black\",  # Set default axes label color to black\n", "})\n", "\n", "def load_data_files(folder_path):\n", "    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n", "    data_files.sort()  # Sort files alphabetically\n", "    return [os.path.join(folder_path, f) for f in data_files]\n", "\n", "# Suppress specific warnings\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n", "warnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n", "\n", "def determine_scan_type(data_files):\n", "    scan_types = []\n", "    previous_polar = None\n", "    previous_hv = None\n", "\n", "    for file_path in data_files:\n", "        data = read_single_pxt(file_path)\n", "        if 'polar' in data.attrs and 'hv' in data.attrs:\n", "            current_polar = data.attrs['polar']\n", "            current_hv = data.attrs['hv']\n", "            if previous_polar is not None and previous_hv is not None:\n", "                if current_polar != previous_polar:\n", "                    scan_types.append(('polar', current_polar))\n", "                elif current_hv != previous_hv:\n", "                    scan_types.append(('hv', current_hv))\n", "                else:\n", "                    scan_types.append(('unknown', None))\n", "            else:\n", "                scan_types.append(('unknown', None))\n", "            previous_polar = current_polar\n", "            previous_hv = current_hv\n", "        else:\n", "            scan_types.append(('unknown', None))\n", "\n", "    return scan_types\n", "\n", "def kspace(data_files, work_function):\n", "    # Constants\n", "    hbar = 6.582119569e-16  # eV*s\n", "    m_e = 9.1093837015e-31  # kg\n", "\n", "    # Pre-calculate all plots\n", "    all_plots = []\n", "    max_data_value = 0\n", "\n", "    for file_path in data_files:\n", "        data = read_single_pxt(file_path)\n", "        E_photon = data.attrs['hv']  # Photon energy from the attributes\n", "\n", "        # Calculate kinetic energy and momentum\n", "        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n", "        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n", "\n", "        # Get the energy values\n", "        energy_values = -data.eV.values  # Negative energy values\n", "\n", "        all_plots.append({\n", "            'k_parallel': k_parallel,\n", "            'energy_values': energy_values,\n", "            'data_values': data.values,\n", "            'file_name': os.path.basename(file_path)\n", "        })\n", "\n", "        max_data_value = max(max_data_value, np.max(data.values))\n", "\n", "    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n", "        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n", "\n", "        fig, ax = plt.subplots(figsize=(10, 8))\n", "\n", "        # Create a custom normalization\n", "        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n", "\n", "        # Create a colormap with the specified range\n", "        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n", "        cmap = ListedColormap(cmap_array)\n", "\n", "        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n", "                           shading='auto', cmap=cmap, norm=norm)\n", "        cbar = fig.colorbar(im, ax=ax)\n", "        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n", "        cbar.ax.tick_params(labelsize=10)\n", "\n", "        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n", "        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n", "        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n", "        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits\n", "\n", "        # Adjust tick label font size\n", "        ax.tick_params(axis='both', which='major', labelsize=10)\n", "\n", "        plt.tight_layout()\n", "        return fig\n", "\n", "    def save_plot(b):\n", "        # Get current slider values directly from the widgets\n", "        current_values = {\n", "            'scan_index': interactive_plot.children[0].value,\n", "            'vmin': interactive_plot.children[1].value,\n", "            'vmax': interactive_plot.children[2].value,\n", "            'scale': interactive_plot.children[3].value,\n", "            'cmap_start': interactive_plot.children[4].value,\n", "            'cmap_end': interactive_plot.children[5].value\n", "        }\n", "\n", "        # Generate the plot with current values\n", "        fig = plot_arpes(**current_values)\n", "\n", "        # Save the plot\n", "        filename = f\"ARPES_plot_scan_{current_values['scan_index']}.png\"\n", "        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n", "        plt.close(fig)\n", "        print(f\"Plot saved as {filename}\")\n", "\n", "    def make_video(b):\n", "        # Get current slider values directly from the widgets\n", "        current_values = {\n", "            'vmin': interactive_plot.children[1].value,\n", "            'vmax': interactive_plot.children[2].value,\n", "            'scale': interactive_plot.children[3].value,\n", "            'cmap_start': interactive_plot.children[4].value,\n", "            'cmap_end': interactive_plot.children[5].value\n", "        }\n", "\n", "        # Determine scan types\n", "        scan_types = determine_scan_type(data_files)\n", "\n", "        # Create the video\n", "        fig, ax = plt.subplots(figsize=(10, 8))\n", "\n", "        # Create a custom normalization\n", "        norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax']/current_values['scale'])\n", "\n", "        # Create a colormap with the specified range\n", "        cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))\n", "        cmap = ListedColormap(cmap_array)\n", "\n", "        # Initialize the plot with the first dataset\n", "        plot_data = all_plots[0]\n", "        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n", "                           shading='auto', cmap=cmap, norm=norm)\n", "        cbar = fig.colorbar(im, ax=ax)\n", "        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n", "        cbar.ax.tick_params(labelsize=10)\n", "\n", "        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n", "        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n", "        ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')\n", "\n", "        ims = []\n", "\n", "        for i, (plot_data, (scan_type, scan_value)) in enumerate(zip(all_plots, scan_types)):\n", "            im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n", "                               shading='auto', cmap=cmap, norm=norm)\n", "\n", "            # Add text for scan type and value\n", "            if scan_type != 'unknown':\n", "                text = ax.text(0.5, -0.1, f'{scan_type}: {scan_value:.2f}',  # Position text below the plot\n", "                               transform=ax.transAxes, color='black',  # Set text color to black\n", "                               horizontalalignment='center', verticalalignment='top',\n", "                               fontsize=10, fontweight='bold')\n", "                ims.append([im, text])\n", "            else:\n", "                ims.append([im])\n", "\n", "        ani = animation.ArtistAnimation(fig, ims, interval=500, blit=True)\n", "        ani.save('ARPES_video.mp4')\n", "        plt.close(fig)\n", "        print(\"Video saved as ARPES_video.mp4\")\n", "\n", "    # Create the interactive widget\n", "    interactive_plot = interactive(plot_arpes,\n", "                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n", "                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),\n", "                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),\n", "                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),\n", "                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),\n", "                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))\n", "\n", "    # Create a save button\n", "    save_button = Button(description=\"Save Plot\")\n", "    save_button.on_click(save_plot)\n", "\n", "    # Create a video button\n", "    video_button = Button(description=\"Make Video\")\n", "    video_button.on_click(make_video)\n", "\n", "    # Combine the interactive plot, save button, and video button\n", "    output = VBox([interactive_plot, save_button, video_button])\n", "\n", "    return output\n", "\n", "# Usage\n", "folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\n", "data_files = load_data_files(folder_path)\n", "work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n", "\n", "# Determine scan types\n", "scan_types = determine_scan_type(data_files)\n", "\n", "interactive_plot_with_save = kspace(data_files, work_function)\n", "display(interactive_plot_with_save)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 2}