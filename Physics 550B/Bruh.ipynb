{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from sympy import *"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["m,k,T,v,ve = symbols('m,k_B,T,v,v_e')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["P = m/sqrt(2*pi*k*T)*E**(-m*v**2/(2*k*T))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\sqrt{2} m \\int\\limits_{v_{e}}^{\\infty} e^{- \\frac{m v^{2}}{2 T k_{B}}}\\, dv}{2 \\sqrt{\\pi} \\sqrt{T k_{B}}}$"], "text/plain": ["sqrt(2)*m*Integral(exp(-m*v**2/(2*T*k_B)), (v, v_e, oo))/(2*sqrt(pi)*sqrt(T*k_B))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["integrate(P,(v,ve,oo)).doit()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 6.58705707982755 \\cdot 10^{-353}$"], "text/plain": ["6.58705707982755e-353"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["P.subs(T,300).subs(m,5.3*10**(-23)).subs(k,1.3807*10**(-16)).subs(v,11*10**5).evalf()"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- \\frac{Z e^{2} \\left(- R^{2} + 3 R a_{0} + 3 a_{0}^{2}\\right)}{R^{2} a_{0}}\n"]}], "source": ["# Define symbols; assume a0, alpha, beta, and k are positive real parameters.\n", "r, theta, phi, a0, alpha, beta, k, R = symbols('r theta phi a0 alpha beta k R', positive=False, real=True)\n", "Z,e = symbols('Z e')\n", "# Define the integrand from the homework integral:\n", "integrand = exp(-2*r/a0) * (alpha*r**2 + beta*r**4 + k*r) * sin(theta)\n", "\n", "# Setup the full triple integral with the given prefactor:\n", "I = (1/pi) * (1/a0)**3 * integrate(\n", "    integrate(\n", "        integrate(integrand, (r, 0, R)),\n", "        (theta, 0, pi)\n", "    ),\n", "    (phi, 0, 2*pi)\n", ")\n", "\n", "# Simplify the result:\n", "I_simplified = simplify(I)\n", "print_latex(I_simplified.subs(beta,Z*e**2/(2*R**3)).subs(k,Z*e**2).subs(alpha,-3*Z*e**2/(2*R)).expand().factor().subs(2*R/a0, 0).expand().factor())\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{2 \\cdot \\left(\\frac{3 a_{0}^{5} \\beta}{2} + \\frac{a_{0}^{3} \\alpha}{2} + \\frac{a_{0}^{2} k}{2}\\right)}{a_{0}^{3}}$"], "text/plain": ["2*(3*a0**5*beta/2 + a0**3*alpha/2 + a0**2*k/2)/a0**3"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["I"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left(\\alpha r^{2} + \\beta r^{4} + k r\\right) e^{- \\frac{2 r}{a_{0}}} \\sin{\\left(\\theta \\right)}$"], "text/plain": ["(alpha*r**2 + beta*r**4 + k*r)*exp(-2*r/a0)*sin(theta)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["integrand"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "mainenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}