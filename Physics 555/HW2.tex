\documentclass[12pt]{article}%
\usepackage{amsfonts}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{comment}
\usepackage[a4paper, top=2.5cm, bottom=2.5cm, left=2.2cm, right=2.2cm]{geometry}
\def\shrinkage{2.1mu}
\def\vecsign{\mathchar"017E}
\def\dvecsign{\smash{\stackon[-1.95pt]{\mkern-\shrinkage\vecsign}{\rotatebox{180}{$\mkern-\shrinkage\vecsign$}}}}
\def\dvec#1{\def\useanchorwidth{T}\stackon[-4.2pt]{#1}{\,\dvecsign}}
\usepackage{stackengine}
\stackMath
\usepackage{cancel}
\usepackage{amsmath}
\usepackage{physics}
\usepackage{bbm}
\usepackage{tikz}
\usepackage{changepage}
\usetikzlibrary{shapes.misc}
\usepackage{amssymb}
\usepackage{graphicx}%
\setcounter{MaxMatrixCols}{30}
\newtheorem{theorem}{Theorem}
\newtheorem{acknowledgement}[theorem]{Acknowledgement}
\newtheorem{algorithm}[theorem]{Algorithm}
\newtheorem{axiom}{Axiom}
\newtheorem{case}[theorem]{Case}
\newtheorem{claim}[theorem]{Claim}
\newtheorem{conclusion}[theorem]{Conclusion}
\newtheorem{condition}[theorem]{Condition}
\newtheorem{conjecture}[theorem]{Conjecture}
\newtheorem{corollary}[theorem]{Corollary}
\newtheorem{criterion}[theorem]{Criterion}
\newtheorem{definition}[theorem]{Definition}
\newtheorem{example}[theorem]{Example}
\newtheorem{exercise}[theorem]{Exercise}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{notation}[theorem]{Notation}
\newtheorem{problem}[theorem]{Problem}
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{remark}[theorem]{Remark}
\newtheorem{summary}[theorem]{Summary}
\newenvironment{proof}[1][Proof]{\textbf{#1.} }{\ \rule{0.5em}{0.5em}}
\newenvironment{solution}[1][Solution]{\textbf{#1}}{\ \rule{0.5em}{0.5em}}
\newcommand{\Q}{\mathbb{Q}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\Z}{\mathbb{Z}}
\newcommand{\N}{\mathbb{N}}
\newcommand{\Lagr}{\mathcal{L}}
\newcommand{\lagr}{\mathcal{L}_0}
\newcommand{\lb}{\left\{}
\newcommand{\rb}{\right\}}
\newcommand{\set}[1]{\left\{#1\right\}}
\newcommand{\pr}[1]{\left(\{#1\right\})}
\newcommand{\dbar}[1]{\bar{\bar{#1}}}
\newcommand{\tbar}[1]{\bar{\bar{\bar{#1}}}}
\title{Physics 555 Homework 2}
\author{Tyler Hadsell}
\begin{document}
\maketitle

\begin{problem}
In class we worked out the following expressions for plane wave solutions of the Dirac equation, those of positive energy $\psi(x)=u_{\uparrow \downarrow}(p) e^{-i p \cdot x}$ and of negative energy $\psi(x)=v_{\uparrow \downarrow}(p) e^{i p \cdot x}$ where

\[
u_{\uparrow}(0)=\left(\begin{array}{l}
1 \\
0 \\
0 \\
0
\end{array}\right), \quad u_{\downarrow}(0)=\left(\begin{array}{l}
0 \\
1 \\
0 \\
0
\end{array}\right), \quad v_{\uparrow}(0)=\left(\begin{array}{l}
0 \\
0 \\
1 \\
0
\end{array}\right), \quad v_{\downarrow}(0)=\left(\begin{array}{l}
0 \\
0 \\
0 \\
1
\end{array}\right)
\]

are the positive (resp. negative) energy spin-up and spin-down spinors at zero momentum. These spinors were found in the Dirac or standard representation for the $\gamma$ matrices,

$
\gamma^{0}=\left(\begin{array}{cc}
I_{2} & 0 \\
0 & -I_{2}
\end{array}\right), \quad \gamma=\left(\begin{array}{cc}
0 & \boldsymbol{\sigma} \\
-\boldsymbol{\sigma} & 0
\end{array}\right), \quad \gamma^{5}=i \gamma^{0} \gamma^{1} \gamma^{2} \gamma^{3}
$

where we have written the matrices in block diagonal form, $I_{2}$ is the $2 \times 2$ identity matrix, and $\boldsymbol{\sigma}=\left(\sigma_{1}, \sigma_{2}, \sigma_{3}\right)$ are the Pauli spin matrices. The Weyl or chiral representation is also commonly used, in part because we can express spinors as

\begin{equation*}
\psi=\binom{\psi_{L}}{\psi_{R}}
\end{equation*}

where $\psi_{L}$ and $\psi_{R}$ are left- and right-handed 2 component spinors.

(a) Using the change-of-representation matrix

$
S=\frac{1}{\sqrt{2}}\left(\begin{array}{cc}
I_{2} & I_{2} \\
-I_{2} & I_{2}
\end{array}\right)
$

find the $\gamma$ matrices as well as the spinors $u_{\uparrow \downarrow}, v_{\uparrow \downarrow}$ in the chiral representation. What is $\gamma^{5}$ in this representation? (Change of representation is like a change of basis. The new matrices are found from the old by $S^{\dagger} \gamma S$, and new spinors by $S \psi$.)

(b) In the chiral representation, how do $u_{\uparrow \downarrow}, v_{\uparrow \downarrow}$ transform under $\gamma^{5}$ ? What are the left- and right-handed components?

(c) Consider a general spinor $\psi$ in the chiral representation, so that it may be written as in (1.2). When viewed as 4-spinors, show $\psi_{L}, \psi_{R}$ are eigenstates of chirality $\gamma^{5}$. What are their eigenvalues?

(d) Define the left- and right-hand chiral projection operators by

$
P_{L} \equiv \frac{1}{2}\left(1-\gamma^{5}\right), \quad P_{R} \equiv \frac{1}{2}\left(1+\gamma^{5}\right)
$

Show $P_{L}, P_{R}$ are indeed projection operators, that is,

$
P_{L}^{2}=P_{L}, \quad P_{R}^{2}=P_{R}, \quad P_{L}+P_{R}=1, \quad P_{R} P_{L}=0
$

(e) For a spinor $\psi$ and its 'adjoint' $\bar{\psi}=\psi^{\dagger} \gamma^{0}$ show that $P_{L}$ projects out only $\psi_{L}$ and $\bar{\psi}_{R}$ while $P_{R}$ projects out only $\psi_{R}$ and $\bar{\psi}_{L}$.
\end{problem}
\begin{solution}\\ 
    a.\\
    We can begin by finding our transformed $\gamma^0$ matrix:
    $$\gamma^{0'} = S^\dagger \gamma^0 S = \frac{1}{2} \begin{pmatrix} I_2 & -I_2 \\ I_2 & I_2 \end{pmatrix} \begin{pmatrix} I_2 & 0 \\ 0 & -I_2 \end{pmatrix} \begin{pmatrix} I_2 & I_2 \\ -I_2 & I_2 \end{pmatrix} = \begin{pmatrix} 0 & I_2 \\ I_2 & 0 \end{pmatrix}$$
    Similarly, the other matrices are:
    $$\gamma^{1'} = \frac{1}{2}\begin{pmatrix} I_2 & -I_2 \\ I_2 & I_2 \end{pmatrix} \begin{pmatrix} 0 & \sigma^1 \\ -\sigma^1 & 0 \end{pmatrix} \begin{pmatrix} I_2 & I_2 \\ -I_2 & I_2 \end{pmatrix} = \begin{pmatrix} 0 & \sigma^1 \\ -\sigma^1 & 0 \end{pmatrix}$$
    $$\gamma^{2'} = \frac{1}{2}\begin{pmatrix} I_2 & -I_2 \\ I_2 & I_2 \end{pmatrix} \begin{pmatrix} 0 & \sigma^2 \\ -\sigma^2 & 0 \end{pmatrix} \begin{pmatrix} I_2 & I_2 \\ -I_2 & I_2 \end{pmatrix} = \begin{pmatrix} 0 & \sigma^2 \\ -\sigma^2 & 0 \end{pmatrix}$$
    $$ \gamma^{3'} = \frac{1}{2}\begin{pmatrix} I_2 & -I_2 \\ I_2 & I_2 \end{pmatrix} \begin{pmatrix} 0 & \sigma^3 \\ -\sigma^3 & 0 \end{pmatrix} \begin{pmatrix} I_2 & I_2 \\ -I_2 & I_2 \end{pmatrix} = \begin{pmatrix} 0 & \sigma^3 \\ -\sigma^3 & 0 \end{pmatrix}$$
    And $$\gamma^{5'} = i\gamma^{0'}\gamma^{1'}\gamma^{2'}\gamma^{3'} = \begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix}$$
    The spinors transform as:
    $$u'_\uparrow = \frac{1}{\sqrt{2}}\begin{pmatrix} I_2 & I_2 \\ -I_2 & I_2 \end{pmatrix} \begin{pmatrix}1\\0\\0\\0\end{pmatrix} = \frac{1}{\sqrt{2}}\begin{pmatrix}1 & 0 & 1 & 0\\ 0& 1 & 0 &1 \\ -1 & 0 & 1 & 0 \\ 0 & -1 & 0 & 1\end{pmatrix} \begin{pmatrix} 1\\0\\0\\0 \end{pmatrix}= \frac{1}{\sqrt{2}}\begin{pmatrix}1\\0\\-1\\0\end{pmatrix}$$
    $$u'_\downarrow = \frac{1}{\sqrt{2}}\begin{pmatrix} I_2 & I_2 \\ -I_2 & I_2 \end{pmatrix} \begin{pmatrix}0\\1\\0\\0\end{pmatrix} = \frac{1}{\sqrt{2}}\begin{pmatrix}1 & 0 & 1 & 0\\ 0& 1 & 0 &1 \\ -1 & 0 & 1 & 0 \\ 0 & -1 & 0 & 1\end{pmatrix} \begin{pmatrix} 0 \\ 1\\0\\0 \end{pmatrix}= \frac{1}{\sqrt{2}}\begin{pmatrix}0\\1\\0\\-1\end{pmatrix}$$
    $$v'_\uparrow = \frac{1}{\sqrt{2}}\begin{pmatrix} I_2 & I_2 \\ -I_2 & I_2 \end{pmatrix} \begin{pmatrix}0\\0\\1\\0\end{pmatrix} = \frac{1}{\sqrt{2}}\begin{pmatrix}1 & 0 & 1 & 0\\ 0& 1 & 0 &1 \\ -1 & 0 & 1 & 0 \\ 0 & -1 & 0 & 1\end{pmatrix} \begin{pmatrix} 0 \\ 0\\1\\0 \end{pmatrix}= \frac{1}{\sqrt{2}}\begin{pmatrix}1\\0\\1\\0\end{pmatrix}$$
    $$v'_\downarrow = \frac{1}{\sqrt{2}}\begin{pmatrix} I_2 & I_2 \\ -I_2 & I_2 \end{pmatrix} \begin{pmatrix}0\\0\\0\\1\end{pmatrix} = \frac{1}{\sqrt{2}}\begin{pmatrix}1 & 0 & 1 & 0\\ 0& 1 & 0 &1 \\ -1 & 0 & 1 & 0 \\ 0 & -1 & 0 & 1\end{pmatrix} \begin{pmatrix} 0 \\ 0\\0\\1 \end{pmatrix}= \frac{1}{\sqrt{2}}\begin{pmatrix}0\\1\\0\\1\end{pmatrix}$$
    Hence we have shown how various objects transfrom under the change of basis to the Chiral Representation.\\ 
    b.\\ 
    Under $\gamma^5$, the spinors transform as:
    $$u'_\uparrow \rightarrow \gamma^{5'} u'_\uparrow = \begin{pmatrix} -1 & 0 & 0 & 0\\ 0 & -1 & 0 & 0\\ 0 & 0 & 1 & 0 \\ 0 & 0 & 0 & 1\end{pmatrix} \frac{1}{\sqrt{2}}\begin{pmatrix}1\\0\\-1\\0\end{pmatrix} = \frac{1}{\sqrt{2}}\begin{pmatrix}-1\\0\\-1\\0\end{pmatrix} = -v'_\uparrow$$
    $$u'_\downarrow \rightarrow \gamma^{5'} u'_\downarrow = \begin{pmatrix} -1 & 0 & 0 & 0\\ 0 & -1 & 0 & 0\\ 0 & 0 & 1 & 0 \\ 0 & 0 & 0 & 1\end{pmatrix} \frac{1}{\sqrt{2}}\begin{pmatrix}0\\1\\0\\-1\end{pmatrix} = \frac{1}{\sqrt{2}}\begin{pmatrix}0\\-1\\0\\-1\end{pmatrix} = -v'_\downarrow$$
    $$v'_\uparrow \rightarrow \gamma^{5'} v'_\uparrow = \begin{pmatrix} -1 & 0 & 0 & 0\\ 0 & -1 & 0 & 0\\ 0 & 0 & 1 & 0 \\ 0 & 0 & 0 & 1\end{pmatrix} \frac{1}{\sqrt{2}}\begin{pmatrix}1\\0\\1\\0\end{pmatrix} = \frac{1}{\sqrt{2}}\begin{pmatrix}-1\\0\\1\\0\end{pmatrix} = -u'_\uparrow$$
    $$v'_\downarrow \rightarrow \gamma^{5'} v'_\downarrow = \begin{pmatrix} -1 & 0 & 0 & 0\\ 0 & -1 & 0 & 0\\ 0 & 0 & 1 & 0 \\ 0 & 0 & 0 & 1\end{pmatrix} \frac{1}{\sqrt{2}}\begin{pmatrix}0\\1\\0\\1\end{pmatrix} = \frac{1}{\sqrt{2}}\begin{pmatrix}0\\-1\\0\\1\end{pmatrix} = -u'_\downarrow$$

    Writing the $\psi_L$ and $\psi_R$ components  of the spinors explicitly:
    $$\gamma^5u'_{L\uparrow} = \frac{1}{\sqrt{2}}\begin{pmatrix}-1\\0\end{pmatrix}, \qquad \gamma^5u'_{R\uparrow} = \frac{1}{\sqrt{2}}\begin{pmatrix}-1\\0\end{pmatrix}$$
    $$\gamma^5u'_{L\downarrow} = \frac{1}{\sqrt{2}}\begin{pmatrix}0\\-1\end{pmatrix}, \qquad \gamma^5u'_{R\downarrow} = \frac{1}{\sqrt{2}}\begin{pmatrix}0\\-1\end{pmatrix}$$
    $$\gamma^5v'_{L\uparrow} = \frac{1}{\sqrt{2}}\begin{pmatrix}-1\\0\end{pmatrix}, \qquad \gamma^5v'_{R\uparrow} = \frac{1}{\sqrt{2}}\begin{pmatrix}1\\0\end{pmatrix}$$
    $$\gamma^5v'_{L\downarrow} = \frac{1}{\sqrt{2}}\begin{pmatrix}0\\-1\end{pmatrix}, \qquad \gamma^5v'_{R\downarrow} = \frac{1}{\sqrt{2}}\begin{pmatrix}0\\1\end{pmatrix}$$
    c.\\ 
    Let us write a general spinor:
    $$\psi' = \begin{pmatrix}
        \psi'_{L\uparrow} \\ \psi'_{L\downarrow} \\ \psi'_{R\uparrow} \\ \psi'_{R\downarrow} \end{pmatrix}$$
        Transforming using $\gamma^5$
        $$\gamma^{5'}\psi' = \begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix} \begin{pmatrix} \psi'_L \\ \psi'_R \end{pmatrix} = \begin{pmatrix} -\psi'_L \\ \psi'_R \end{pmatrix} = -\begin{pmatrix}\psi'_L \\ 0 \end{pmatrix} + \begin{pmatrix} 0 \\ \psi'_R \end{pmatrix}$$
        $$\gamma^{5'}\psi' = A_L\lambda_L\begin{pmatrix} \psi'_{L\uparrow} \\ \psi'_{L\downarrow} \\ 0 \\ 0 \end{pmatrix}\begin{pmatrix} \psi^{'*}_{L\uparrow} & \psi^{'*}_{L\downarrow} & 0 & 0 \end{pmatrix} + A_R\lambda_R \begin{pmatrix} 0 \\ 0 \\ \psi'_{R\uparrow} \\ \psi'_{R\downarrow} \end{pmatrix}\begin{pmatrix} 0 & 0 & \psi^{'*}_{R\uparrow} & \psi^{'*}_{R\downarrow} \end{pmatrix}$$
        Where $A_L$ and $A_R$ are the normalization constants and $\lambda_L$ and $\lambda_R$ are the chirality eigenvalues for the left and right components respectively.
        $$\lambda_L = -1, \qquad \lambda_R = 1$$
        d.\\ 
        We will first show the first requirement that $P_L^2 = P_L$ and $P_R^2 = P_R$:
        $$P_L^2 = \left( \frac{1-\gamma^5}{2} \right)^2 = \frac{1-\gamma^5}{2} \frac{1-\gamma^5}{2} = \frac{1+ \gamma^5\gamma^5 - 2\gamma^5}{4}$$$$ = \frac{1 + \begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix} \begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix} - 2\begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix}}{4} = \frac{2 - 2\begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix}}{4} = \frac{1-\gamma^5}{2} = P_L$$
        $$P_R^2 = \left( \frac{1+\gamma^5}{2} \right)^2 = \frac{1+\gamma^5}{2} \frac{1+\gamma^5}{2} = \frac{1+ \gamma^5\gamma^5 + 2\gamma^5}{4}$$$$ = \frac{1 + \begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix} \begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix} + 2\begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix}}{4} = \frac{2 + 2\begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix}}{4} = \frac{1+\gamma^5}{2} = P_R$$
        Next we will show the orthogonality and completeness relations:
        $$P_L + P_R = \frac{1-\gamma^5}{2} + \frac{1+\gamma^5}{2} = 1$$
        $$P_L P_R = \frac{1-\gamma^5}{2} \frac{1+\gamma^5}{2} = \frac{1 - \gamma^5 + \gamma^5 -\gamma^{5^2}}{4} = \frac{1-1 - \gamma^5  + \gamma^5}{4} = 0$$
        e.\\ 
        To complete this part we will again write out an arbitrary spinor and its adjoint in terms of left and right handed components:
        $$\psi = \begin{pmatrix} \psi_L \\ \psi_R \end{pmatrix}, \qquad \psi^\dagger = (\psi_L^\dagger, \psi_R^\dagger)$$
        Now lets apply $P_L$ to the spinor:
        $$P_L\psi = \left(\frac{1-\gamma^5}{2}\right)\begin{pmatrix} \psi_L \\ \psi_R \end{pmatrix} = \frac{1-\begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix}}{2}\begin{pmatrix} \psi_L \\ \psi_R \end{pmatrix} = \begin{pmatrix} \psi_L \\ 0 \end{pmatrix}$$
        And $P_R$ to the spinor:
        $$P_R\psi = \left(\frac{1+\gamma^5}{2}\right)\begin{pmatrix} \psi_L \\ \psi_R \end{pmatrix} = \frac{1+\begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix}}{2}\begin{pmatrix} \psi_L \\ \psi_R \end{pmatrix} = \begin{pmatrix} 0 \\ \psi_R \end{pmatrix}$$
        Now we will apply these to the spinor's space-time adjoint
        $$\bar \psi P_L = \psi^\dagger \gamma^0 \left(\frac{1-\gamma^5}{2}\right) = \begin{pmatrix} \psi^\dagger_L & \psi^\dagger_R \end{pmatrix} \begin{pmatrix} 0 & I_2 \\ I_2 & 0 \end{pmatrix} \frac{1-\begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix}}{2} $$
        $$ = \frac{1}{2}\begin{pmatrix} \psi^\dagger_R & \psi^\dagger_L \end{pmatrix}\left(\mathbbm{1} - \begin{pmatrix} -I_2 & 0\\ 0 & I_2\end{pmatrix}\right)$$
        $$ = \begin{pmatrix} \psi^\dagger_R & 0 \end{pmatrix} = \begin{pmatrix} \psi^\dagger_R & 0 \end{pmatrix}\gamma^0 \gamma^0  = \begin{pmatrix} 0 & \psi^\dagger_R \end{pmatrix}\gamma^0 = \bar\psi_R$$
        For the $P_R$ projection:
        $$\bar \psi P_R = \psi^\dagger \gamma^0 \left(\frac{1+\gamma^5}{2}\right) = \begin{pmatrix} \psi^\dagger_L & \psi^\dagger_R \end{pmatrix} \begin{pmatrix} 0 & I_2 \\ I_2 & 0 \end{pmatrix} \frac{1+\begin{pmatrix} -I_2 & 0 \\ 0 & I_2 \end{pmatrix}}{2} $$
        $$ = \frac{1}{2}\begin{pmatrix} \psi^\dagger_R&\psi^\dagger_L  \end{pmatrix}\left(\mathbbm{1} + \begin{pmatrix} -I_2 & 0\\ 0 & I_2\end{pmatrix}\right) = \begin{pmatrix} \psi^\dagger_L&0 \end{pmatrix}\gamma^0 = \bar\psi_L$$
    \end{solution}
    
    \begin{problem}
    Let
    
    $
    U=\frac{1}{\sqrt{2}}\left(\begin{array}{cc}
    I_{2} & \sigma_{2} \\
    \sigma_{2} & -I_{2}
    \end{array}\right)
    $
    
    Show $U$ is Hermitian and unitary. Using $U$ define a new representation for the $\gamma$ matrices
    
    $
    \tilde{\gamma}^{\mu}=U \gamma^{\mu} U^{\dagger}
    $
    
    where the $\gamma^{\mu}$ are in the Dirac representation. Find the $\tilde{\gamma}^{\mu}$ and $\tilde{\gamma}^{5}$ in this representation. Rewrite the Dirac equation in the $\tilde{\gamma}$ representation. What do you observe when you compare the Dirac equation in this representation to the Dirac equation in the Dirac representation?
    \end{problem}
    \begin{solution}\\
    First we will start by showing that $U$ is Hermitian:
    We want to show the following $$U^\dagger = \left(U^*\right)^T = U$$
    Where $^*$ denotes complex conjugation and $T$ denotes transpose.
    We have:
    $$U = \frac{1}{\sqrt{2}}\begin{pmatrix} I_2 & \sigma_2 \\ \sigma_2 & -I_2 \end{pmatrix}$$
    Taking the complex conjugate:
    $$U^* = \frac{1}{\sqrt{2}}\begin{pmatrix} I_2 & \sigma_2^* \\ \sigma_2^* & -I_2 \end{pmatrix} $$
    The conjugate of the $2$x$2$ identity matrix $I_2$ is itself. The conjugate of the Pauli matrix can we shown as follows:
    $$\sigma_2= \begin{pmatrix} 0 & -i \\ i & 0 \end{pmatrix}$$
    Taking the complex conjugate:
    $$\sigma_2^* = \begin{pmatrix} 0 & i \\ -i & 0 \end{pmatrix} = -\sigma_2$$
    Therefore,
    $$U^* = \frac{1}{\sqrt{2}}\begin{pmatrix} I_2 & -\sigma_2 \\ -\sigma_2 & -I_2 \end{pmatrix}$$
    Now lets write write out the transpose:
    $$\left(U^*\right)^T = \frac{1}{\sqrt{2}}\begin{pmatrix} I_2^T & -\sigma_2^T \\ -\sigma_2^T & -I_2^T \end{pmatrix}$$
    The identity matrix is unchanged by transpose, and the Pauli matrices are changed in the following way:
    $$\sigma_2^T = \begin{pmatrix} 0 & i \\ -i & 0 \end{pmatrix} = -\sigma_2$$
    Therefore,
    $$\left(U^*\right)^T = \frac{1}{\sqrt{2}}\begin{pmatrix} I_2 & \sigma_2 \\ \sigma_2 & -I_2 \end{pmatrix} = U$$
    Which shows that $U$ is Hermitian.
    Next we will show $U$ is unitary:
    We want to show $UU^\dagger = U^\dagger U = \mathbbm{1}$.
    We have:
    $$UU^\dagger = UU = U^2 = \frac{1}{2}\begin{pmatrix} I_2 & \sigma_2 \\ \sigma_2 & -I_2 \end{pmatrix}\begin{pmatrix} I_2 & \sigma_2 \\ \sigma_2 & -I_2 \end{pmatrix}$$
    $$= \frac{1}{2}\begin{pmatrix} I_2^2+\sigma_2^2 & I_2\sigma_2-\sigma_2I_2 \\ \sigma_2I_2-I_2\sigma_2 & -I_2^2-\sigma_2^2 \end{pmatrix}$$
    We recall that the identity and the Pauli matrices satisfy the following identities:
    $$I_2^2 = I_2, \qquad \sigma_2^2 = I_2$$
    Using these, we can simplify the matrix multiplication to obtain:
    $$U^2 = \frac{1}{2}\begin{pmatrix} 2I_2 & 0 \\ 0 & 2I_2 \end{pmatrix} = \mathbbm{1}$$
    Hence $U$ is unitary.
    Next we define the new gamma matrices:
    $$\tilde{\gamma}^{\mu} = U\gamma^{\mu}U^{\dagger}$$
    Where the gamma matrices $\gamma^{\mu}$ are in the Dirac representation. Explicitly,
    $$\gamma^0 = \begin{pmatrix} I_2 & 0 \\ 0 & -I_2 \end{pmatrix}, \qquad \gamma^i = \begin{pmatrix} 0 & \sigma_i \\ -\sigma_i & 0 \end{pmatrix}$$
    Now we apply our transformation:
    $$\tilde{\gamma}^0 = U\gamma^0U^{\dagger} = \frac{1}{\sqrt{2}}\begin{pmatrix} I_2 & \sigma_2 \\ \sigma_2 & -I_2 \end{pmatrix}\begin{pmatrix} I_2 & 0 \\ 0 & -I_2 \end{pmatrix}\frac{1}{\sqrt{2}}\begin{pmatrix} I_2 & \sigma_2 \\ \sigma_2 & -I_2 \end{pmatrix}$$
    $$= \frac{1}{2} \begin{pmatrix} I_2 & -\sigma_2 \\ \sigma_2 & I_2 \end{pmatrix}\begin{pmatrix} I_2 & \sigma_2 \\ \sigma_2 & -I_2 \end{pmatrix} = \begin{pmatrix} 0 & \sigma_2 \\ \sigma_2 & 0 \end{pmatrix}$$
    Similarly,
    $$\tilde{\gamma}^i = U\gamma^iU^{\dagger} = \frac{1}{\sqrt{2}}\begin{pmatrix} I_2 & \sigma_2 \\ \sigma_2 & -I_2 \end{pmatrix}\begin{pmatrix} 0 & \sigma_i \\ -\sigma_i & 0 \end{pmatrix}\frac{1}{\sqrt{2}}\begin{pmatrix} I_2 & \sigma_2 \\ \sigma_2 & -I_2 \end{pmatrix}$$
    $$= \frac{1}{2} \begin{pmatrix} -\sigma_2\sigma_i & \sigma_i \\ \sigma_i & \sigma_2\sigma_i \end{pmatrix}\begin{pmatrix} I_2 & \sigma_2 \\ \sigma_2 & -I_2 \end{pmatrix}= \frac{1}{2}\begin{pmatrix} -\sigma_2\sigma_i + \sigma_i\sigma_2 & -\sigma_2\sigma_i \sigma_2 - \sigma_i \\ \sigma_i + \sigma_2\sigma_i\sigma_2 & \sigma_i\sigma_2 - \sigma_2\sigma_i \end{pmatrix}$$
    $$= \begin{pmatrix} \sigma_i \sigma_2 & 0 \\ 0 & \sigma_i\sigma_2 \end{pmatrix}$$
    We notice the following:
    $$\gamma^0 \gamma^2 = \begin{pmatrix} 0 & \sigma_2 \\ \sigma_2 & 0 \end{pmatrix} = \tilde{\gamma}^0$$
    $$\gamma^i \gamma^2 = \begin{pmatrix} -\sigma_i \sigma_2 & 0 \\ 0 & - \sigma_i\sigma_2 \end{pmatrix} = -\tilde{\gamma}^i$$
    Hence the Dirac equation in the new basis is:
    $$i\left(\gamma^0 \partial_0 - \gamma^i\partial_i \right)\gamma^2\psi = m\psi$$
\end{solution}

    
    \begin{problem}
    Show that the following Hamiltonian $H$ and Hamiltonian density $\mathcal{H}$
    
    $
    H=\int_{\mathbb{R}^{3}} d^{3} \boldsymbol{x} \mathcal{H}=\int_{\mathbb{R}^{3}} d^{3} \boldsymbol{x} \psi^{*}\left[-\frac{\hbar^{2} \nabla^{2}}{2 m}+V\right] \psi
    $
    
    corresponds to the Schrödinger lagrangian density
    
    $
    \mathcal{L}_{S E}=i \hbar \psi^{*} \frac{\partial}{\partial t} \psi+\frac{\hbar^{2}}{2 m} \psi^{*} \nabla^{2} \psi-V \psi^{*} \psi
    $
    \end{problem}
    \begin{solution}\\
       We begin with the equation to convert from a Hamiltonian to a Lagrangian:
       $$\mathcal H = \frac{\partial \Lagr}{\partial \dot \psi} \dot \psi - \Lagr$$
       We then use the Schrodinger Lagrangian equation:
       $$\mathcal H = i\hbar \psi^* \dot \psi -i\hbar \psi^* \dot \psi  - \frac{\hbar^{2}}{2 m} \psi^* \nabla^{2} \psi + V \psi^* \psi$$
       $$\mathcal H = \psi^* \left[-\frac{\hbar^2}{2m}\nabla^2 \psi + V\right]\psi$$
       Now that we have obtained our Hamiltonian density we can integrate over the entire space to obtain the Hamiltonian:
       $$H = \int_{\mathbb{R}^3} d^{3} \boldsymbol{x} \mathcal{H} = \int_{\mathbb{R}^3} d^{3} \boldsymbol{x} \psi^* \left[-\frac{\hbar^2}{2m}\nabla^2 \psi + V\right]\psi$$
    \end{solution}
    
        \begin{problem}
        What state results from applying the operator $\exp \left(\frac{i}{\hbar} \pi \sigma_{z}\right)$ to the bispinor $\binom{1}{0}:$
    
        $
        \exp \left(\frac{i}{\hbar} \pi \sigma_{z}\right)\binom{1}{0}=?
        $
    
        where $\sigma_{z}=\left(\begin{array}{cc}1 & 0 \\ 0 & -1\end{array}\right)$.
        \end{problem}
        \begin{solution}\\
            The problem incorrectly writes the exponential operator, as an exponential should have a dimensionless argument, hence I assume the problem meant the exponential argument to be the following:
            $$\exp\left(i\pi \sigma_z\right)$$
            Now we apply it to the bispinor:
            $$\exp\left(i\pi \sigma_z\right) \begin{pmatrix} 1 \\ 0 \end{pmatrix} = \left(1 + i\pi \begin{pmatrix} 1 & 0 \\ 0 & -1 \end{pmatrix} - \frac{\pi^2}{2}\begin{pmatrix} -1 & 0 \\ 0 & 1 \end{pmatrix} + \dots\right) \begin{pmatrix} 1 \\ 0 \end{pmatrix} $$
            $$= \left(\cos{\pi}\begin{pmatrix} 1 & 0\\ 0 & -1\end{pmatrix} + i \sin{\pi}\begin{pmatrix} 1 & 0\\ 0 & -1\end{pmatrix}\right) \begin{pmatrix} 1 \\ 0 \end{pmatrix} = \begin{pmatrix} -1 & 0 \\ 0 & 1 \end{pmatrix}\begin{pmatrix} 1 \\ 0 \end{pmatrix}$$
            $$= \begin{pmatrix} -1 \\ 0 \end{pmatrix}$$
        \end{solution}
        
        \begin{problem}
        How do spherical coordinates $(r, \theta, \varphi)$ change under parity $P$ transformations? (Space-inversion)
        
        $
        \begin{aligned}
        & r \xrightarrow{P} ? \\
        & \theta \xrightarrow{P} ? \\
        & \varphi \xrightarrow{P} ?
        \end{aligned}
        $
        \end{problem}
        \begin{solution}\\
            We understand the following property of the parity operator:
            $$\hat P \bar r = - \bar r$$
            Hence we can write the following:
            $$\hat P r = \hat P \sqrt{x^2 + y^2 + z^2} = \sqrt{x^2 + y^2 + z^2} = r$$
            Therefore, $r$ is invariant under parity transformations.
            Next for $\theta$ we have:
            $$\hat P \theta = \hat P \cos^{-1}\left(\frac{z}{\sqrt{x^2 + y^2 + z^2}}\right) = \cos^{-1}\left(\frac{-z}{\sqrt{x^2 + y^2 + z^2}}\right) = \pi - \theta$$
            Therefore, $\theta$ changes sign under parity transformations.
            Finally, for $\varphi$ we have:
            $$\hat P \varphi = \hat P \tan^{-1}\left(\frac{y}{x}\right) = \tan^{-1}\left(\frac{-y}{x}\right) = \varphi + \pi \text{ for } \varphi \in [0, \pi) \text{ and } \varphi - \pi \text{ for } \varphi \in [\pi, 2\pi)$$
            Therefore, $\varphi$ changes sign under parity transformations.
        \end{solution}
        
        \begin{problem}
        Show under parity transformations $P=\gamma^{0}$
        
        \[
        \begin{array}{r}
        \bar{\psi} \psi \xrightarrow{P} ?  \\
        \bar{\psi} \gamma^{5} \psi \xrightarrow{P} ? \\
        \bar{\psi} \gamma^{\mu} \psi \xrightarrow{P} ? \\
        \bar{\psi} \gamma^{\mu} \gamma^{5} \psi \xrightarrow{P} ?
        \end{array}
        \]
        
        that scalars and axial vectors do not change sign, while vectors and pseudoscalars do.
        \end{problem}
        \begin{solution}\\
        First we will write down the following anticommutation relations:
        $$\{ \gamma^\mu, \gamma^\nu \} = 2g^{\mu\nu}$$
        This implies that $$\gamma^0 \gamma^0 = \mathbbm{1}$$
        $$\gamma^i \gamma^j = -\gamma^j \gamma^i \text{ for } i \neq j$$
        $$\gamma^i \gamma^i = -\mathbbm{1}$$
        Next we find the following anticommutation relation for $\gamma^5$:
        $$\gamma^5 \gamma^5 = \mathbbm{1}$$
        $$\gamma^5 \gamma^\mu = -\gamma^\mu \gamma^5$$
        Now we can write the following:
        $$\bar \psi P P \psi = \bar \psi \gamma^0 \gamma^0 \psi = \bar \psi \psi$$
        Hence, sclars do not change sign under parity transformations.
        Next we have:
        $$\bar \psi P \gamma^5 P\psi = \bar \psi \gamma^0 \gamma^5 \gamma^0 \psi = -\bar \psi \gamma^0 \gamma^0\gamma^5 \psi = -\bar \psi \gamma^5 \psi$$
        Hence, psuedoscalars do indeed change sign under parity transformations.
        Next we have:
        $$\bar \psi P \gamma^\mu P \psi = \bar \psi \gamma^0 \gamma^\mu \gamma^0 \psi = -\bar \psi \gamma^\mu \psi$$
        Hence, vectors do indeed change sign under parity transformations.
        Finally, we have:
        $$\bar \psi P \gamma^\mu \gamma^5 P \psi = \bar \psi \gamma^0 \gamma^\mu \gamma^5 \gamma^0 \psi = -\bar \psi \gamma^0 \gamma^\mu \gamma^ 0\gamma^5 \psi = \bar \psi \gamma^\mu \gamma^5 \psi$$
        Hence, axial vectors do not change sign under parity transformations.


        \end{solution}
        
                \begin{problem}
                Derive the Dirac algebra $\left\{\gamma^{\mu}, \gamma^{\nu}\right\}=2 g^{\mu \nu}$ from the requirement that the Dirac equation
        
                \begin{equation*}
                -i \gamma^{\mu} \partial_{\mu} \psi+m \psi=0
                \end{equation*}
        
                and the Klein-Gordon equation
        
                \begin{equation*}
                \square^{2} \psi=m^{2} \psi
                \end{equation*}
        
                are consistent.
                \end{problem}
                \begin{solution}\\
            First let use write down two operators $D$ and $K$ such that:
            $$D = \frac{i}{m}\gamma^\mu \partial_\mu$$
            $$K = -\frac{1}{m^2}\square^2$$
            Now to ensure that the Dirac equation and the Klein-Gordon equation are consistent we require the following:
            $$D^2 = K$$
            $$D^2 = -\frac{1}{m^2}\left(\gamma^\mu \partial_\mu\right)^2 =-\frac{1}{m^2}\left(\gamma^\mu \partial_\mu \gamma^\nu \partial_\nu\right) = -\frac{1}{m^2}\left(\gamma^\mu \gamma^\nu \partial_\mu \partial_\nu\right)$$
            $$K = -\frac{1}{m^2}\left(\square^2\right) = -\frac{1}{m^2}\left(\partial_\mu \partial^\mu\right) = -\frac{1}{m^2}\left(\partial_\mu \partial^\mu\right)$$
            Hence, we have:
            $$\frac{1}{m^2}\left(\gamma^\mu \gamma^\nu \partial_\mu \partial_\nu\right) = \frac{1}{m^2}\left(\partial_\mu \partial^\mu\right)$$
            $$\gamma^\mu \gamma^\nu \partial_\mu \partial_\nu = \partial_\mu \partial^\mu$$
            We can now use the following identity:
            $$\gamma^\mu \gamma^\nu = \frac{1}{2}\left(\gamma^\mu \gamma^\nu + \gamma^\nu \gamma^\mu\right)$$
            $$\gamma^\mu \gamma^\nu \partial_\mu \partial_\nu = \frac{1}{2}\left(\gamma^\mu \gamma^\nu \partial_\mu \partial_\nu + \gamma^\nu \gamma^\mu \partial_\mu \partial_\nu\right)$$
            Our equation then becomes:
            $$\frac{1}{2}\left(\gamma^\mu \gamma^\nu \partial_\mu \partial_\nu + \gamma^\nu \gamma^\mu \partial_\mu \partial_\nu\right) = \partial_\mu \partial^\mu$$
            We can then factor our the derivatives to the right of the paranetheses and obtain:
            $$\frac{1}{2}\left(\gamma^\mu \gamma^\nu + \gamma^\nu \gamma^\mu\right)\partial_\mu \partial_\nu = \partial_\mu \partial^\mu$$
            We recognize that the inside of the paranetheses is the anticommutation relation for the gamma matrices:
            $$\frac{1}{2}\{ \gamma^\mu, \gamma^\nu \} \partial_\mu \partial_\nu = \partial_\mu \partial^\mu$$
            Since we have two covariant derivatives on the left hand side and on the right hand side we have one covariant and one contravariant derivative we require the left hand side behaves as a raising operator of some sort. Hence, we can write the following:
            $$\frac{1}{2}\{ \gamma^\mu, \gamma^\nu \}\partial_\nu = \partial^\mu$$
            We then use the following identity and property of the metric tensor:
            $$g^{\mu\nu}\partial_\nu = \partial^\mu$$
            Hence, we have:
            $$\frac{1}{2}\{ \gamma^\mu, \gamma^\nu \} = g^{\mu\nu}$$
            and we now reach our final result:
            $$\{ \gamma^\mu, \gamma^\nu \} = 2g^{\mu\nu}$$
                    \end{solution}
    
    
\end{document}