\documentclass[12pt]{article}%
\usepackage{amsfonts}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{comment}
\usepackage[a4paper, top=2.5cm, bottom=2.5cm, left=2.2cm, right=2.2cm]%
{geometry}
\def\shrinkage{2.1mu}
\def\vecsign{\mathchar"017E}
\def\dvecsign{\smash{\stackon[-1.95pt]{\mkern-\shrinkage\vecsign}{\rotatebox{180}{$\mkern-\shrinkage\vecsign$}}}}
\def\dvec#1{\def\useanchorwidth{T}\stackon[-4.2pt]{#1}{\,\dvecsign}}
\usepackage{stackengine}
\stackMath
\usepackage{times}
\usepackage{cancel}
\usepackage{amsmath}
\usepackage{tikz} 
\usepackage{changepage}
\usetikzlibrary{shapes.misc}
\usepackage{amssymb}
\usepackage{graphicx}%
\setcounter{MaxMatrixCols}{30}
\newtheorem{theorem}{Theorem}
\newtheorem{acknowledgement}[theorem]{Acknowledgement}
\newtheorem{algorithm}[theorem]{Algorithm}
\newtheorem{axiom}{Axiom}
\newtheorem{case}[theorem]{Case}
\newtheorem{claim}[theorem]{Claim}
\newtheorem{conclusion}[theorem]{Conclusion}
\newtheorem{condition}[theorem]{Condition}
\newtheorem{conjecture}[theorem]{Conjecture}
\newtheorem{corollary}[theorem]{Corollary}
\newtheorem{criterion}[theorem]{Criterion}
\newtheorem{definition}[theorem]{Definition}
\newtheorem{example}[theorem]{Example}
\newtheorem{exercise}[theorem]{Exercise}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{notation}[theorem]{Notation}
\newtheorem{problem}[theorem]{Problem}
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{remark}[theorem]{Remark}
\newtheorem{summary}[theorem]{Summary}
\newenvironment{proof}[1][Proof]{\textbf{#1.} }{\ \rule{0.5em}{0.5em}}
\newenvironment{solution}[1][Solution]{\textbf{#1}}{\ \rule{0.5em}{0.5em}}

\newcommand{\Q}{\mathbb{Q}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\Z}{\mathbb{Z}}
\newcommand{\N}{\mathbb{N}}
\newcommand{\Lagr}{\mathcal{L}}
\newcommand{\lagr}{\mathcal{L}_0}
\newcommand{\lb}{\left\{}
\newcommand{\rb}{\right\}}
\newcommand{\set}[1]{\left\{#1\right\}}
\newcommand{\pr}[1]{\left(\{#1\right\})}
\newcommand{\dbar}[1]{\bar{\bar{#1}}}
\newcommand{\tbar}[1]{\bar{\bar{\bar{#1}}}}
\title{Physics 510 Homework 1}
\author{Tyler Hadsell}
\begin{document}
\maketitle
\begin{problem}
\end{problem}
\begin{solution}\\
    We can begin by using Newton's second law:
    $$\bar F = \frac{d\bar p }{dt}$$
    For the case of constant mass we can simplify:
    $$\bar F = m\frac{d\bar v}{dt}$$
    We can omit the vector notation for this problem as we are only concerned with the motion in one dimension:
    $$F = m\frac{dv}{dt}$$
    We recognize that the only force in this problem is the drag force and substitute it in:
    $$-kv = m \frac{dv}{dt}$$
    We can rearrange the equation to obtain the following:
    $$-\frac{k}{m} dt = \frac{dv}{v}$$
    We can now integrate both sides to obtain an equation in terms of $v$ and $t$.
    $$-\frac{kt}{m} = \ln\frac{v(t)}{v_0}$$
    We can then solve the equation to obtain $v(t)$:
    $$v(t) = v_0 e^{-\frac{kt}{m}}$$
    To obtain our position as a function of time we can simply integrate the function a second time:
    $$\int_0^t v(t) dt = \int_0^t v_0 e^{-\frac{kt}{m}} dt $$
    $$\int_{x_0}^x dx = \int_0^t v_0 e^{-\frac{kt}{m}} dt $$
    $$x(t) - x_0 = -\frac{mv_0}{k}e^{-\frac{kt}{m}} + \frac{mv_0}{k}$$
    $$x(t) = x_0 + \frac{mv_0}{k}\left(1 - e^{-\frac{kt}{m}}\right)$$
    We have obtained the equations of motion of our one dimensional particle experiencing a drag force $-kv$
\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
    We may again begin this problem via Newton's second law where we omit the vector notation to consider only the motion in one dimension:
    $$F = \frac{dp}{dt}$$
    We recall that the system being studied has only the thrust force from the rocket propulsion acting on the system. This force is given by $F_t = -\frac{dm}{dt}v'$ where $v'$ is the constant backwards relative velocity of the fuel expulsion.
    $$-\frac{dm}{dt}v' = m\frac{dv}{dt}$$
    We can simplify this expression to obtain the following:
    $$-v'dm = mdv$$
    Next we rearrange to obtain an integration equation:
    $$-v' \frac{dm}{m} = dv$$
    We then integrate both sides to obtain the following equation (we assume the rocket begins at rest relative to our reference frame):
    $$-v' \ln \frac{m(t)}{m_0} = v(t)$$
    We recall that the fuel is expelled from the rocket at a constant rate $\mu$ and hence our $m(t)$ equation must take the following form:
    $$m(t) = m_0 - \mu t$$
    We substitute this into our velocity equation to obtain the following:
    $$-v'\ln\left(1-\frac{\mu t}{m_0}\right) = v(t)$$
    We can integrate this equation with respect to $t$ to obtain the position as a function of time:
    $$\int_0^t -v' \ln\left(1-\frac{\mu t}{m_0}\right) dt = x(t)$$
    We employ $u$ subsitution in the following way to obtain a more simple integral:
    $$u = 1-\frac{\mu t}{m_0}$$
    $$du = -\frac{\mu }{m_0} dt$$
    $$\frac{m_0 v'}{\mu}\int_1^{1-\frac{\mu t}{m_0}} \ln \left(u\right) du = x(u)$$
    We recall the following formula:
    $$\int\ln\left(x\right) dx = x\ln \left(x\right) - x + C$$
    And therefore we finalize our equation of motion:
    $$\frac{m_0 v'}{\mu} \left(1-\frac{\mu t}{m_0}\right)\ln{\left(1-\frac{\mu t}{m_0}\right)} + v't = x(t)$$
    $$v'\left(\left( \frac{m_0}{\mu} - t\right)\ln\left(1-\frac{\mu t}{m_0}\right) + t\right) = x(t)$$
\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
We will begin this problem by listing out the relevant forces and their points of application:
$$\bar F_a = -mg\hat y$$
$$\bar F_b = -Mg\hat y$$
$$\bar F_c = -T\hat x$$
$$\bar F_d = -Mg\hat y$$
$$\bar F_e = T\hat x$$
$$\bar x_a = \ell \sin\alpha \hat y$$
$$\bar x_b = \frac{\ell}{2}\cos\alpha \hat x + \frac{\ell}{2}\sin\alpha \hat y$$
$$\bar x_c = \ell\cos\alpha\hat x$$
$$\bar x_d = -\frac{\ell}{2}\cos\alpha \hat x + \frac{\ell}{2}\sin\alpha \hat y$$
$$\bar x_e = -\ell\cos\alpha \hat x$$
We will then utilize our virtual work principle (Einstein summation notation is used):
$$\bar F_i \cdot \delta \bar x_i = \delta W = 0$$
$$\bar F_a \cdot \delta \bar x_a + \bar F_b \cdot \delta \bar x_b + \bar F_c \cdot \delta \bar x_c + \bar F_d \cdot \delta \bar x_d + \bar F_e \cdot \delta \bar x_e = \delta W$$
We will now list out or virual displacement vectors:
$$\delta \bar x_a = \delta\alpha\ell\cos\alpha \hat y$$
$$\delta \bar x_b = \left(-\frac{\ell}{2}\sin\alpha\hat x + \frac{\ell}{2}\cos\alpha\hat y\right)\delta\alpha$$
$$\delta \bar x_c = -\delta \alpha\ell\sin\alpha \hat x$$
$$\delta \bar x_d = \delta\alpha\left(\frac{\ell}{2}\sin\alpha \hat x + \frac{\ell}{2}\cos\alpha \hat y\right)$$
$$\delta \bar x_d = \delta\alpha\ell\sin\alpha\hat x$$
Now we will substitute in our virtual work equation to obtain the following:
$$\left(-mg\ell\cos\alpha -\frac{Mg\ell}{2}\cos\alpha + T\ell\sin\alpha - \frac{Mg\ell}{2}\cos\alpha + T\ell\sin\alpha\right)\delta\alpha = \delta W$$
$$\left(-mg\ell\cos\alpha - Mg\ell\cos\alpha + 2T\ell\sin\alpha\right)\delta\alpha = \delta W$$
We set our virtual work to zero and remove our virtual displacement to obtain our final equation:
$$2T\ell\sin\alpha = g\ell\cos\alpha\left(m+M\right)$$
$$T = \frac{g}{2}\cot\alpha\left(m+M\right)$$
\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
In this problem we will first begin by defining a coordinate $z$ that acts as a measure of how far vertically down the cone our ring currently sits:
We can do this by utilizing the cone angle $\alpha$ as well as the current radius of our ring $r$:
$$\frac{r}{z} = \tan\alpha$$
Additionally, for the purpose of virtual work done by the ring's tension, will also define a circumference value $\ell$:
$$\ell = 2\pi r$$
We now define our forces in the following way:
$$F_t = T\hat \ell$$
$$F_g = mg\hat z$$
We notice that the force of gravity is positively oriented in the $z$ direction since $z$ is taken as the vertical distance from the top of the cone downward.
Now we create our virtual displacements:
$$\delta \ell = 2\pi \delta r$$
$$\delta z = \frac{\delta r}{\tan\alpha}$$
Setting up our virtual work principle equation we obtain the following:
$$T\delta\ell + mg\delta z = \delta W$$
$$2\pi T \delta r + \frac{mg}{\tan\alpha} \delta r = \delta W$$
Setting our virtual work to zero and removing our virtual displacements we obtain the following:
$$2\pi T = -\frac{mg}{\tan\alpha}$$
Rearranging for our tension we obtain the following expression for the tension in the ring:
$$T = -\frac{mg}{2\pi\tan\alpha}$$

\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
    We will begin this problem by defining the radius of our ring $r$ in terms of the radius of the sphere $R$ and a polar angle $\theta$:
    $$r = R\sin\theta$$
    Additionally, we will define the vertical distance $z$ above the center of the sphere in terms of these same parameters:
    $$z = R\cos\theta$$
We will again, for the purposes of virtual work, define a circumference of the ring $\ell$ in terms of $r$:
    $$\ell = 2\pi r $$

Next we will write out or virtual displacements:
$$\frac{\delta \ell}{2\pi} = \delta r$$
$$\delta \alpha R \cos\alpha = \delta r$$
$$-\delta\alpha R\sin\alpha = \delta z$$
We can now set up our virtual work principle equation:
$$T \delta \ell - mg\delta z = \delta W$$
Substituting our virtual displacement values in we obtain the following:
$$\left(2\pi TR\cos\alpha + mgR\sin\alpha\right)\delta\alpha = \delta W$$
Again we set our virtual work to zero and remove our virtual displacement to obtain the following solution for the tension in the ring:
$$2\pi T R \cos\alpha = -mgR\sin\alpha$$
$$T = \frac{-mg \tan\alpha}{2\pi}$$
\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
    We will begin this problem by writing out the position vector in spherical coordinates. This requires we utilize the initial Cartesian position vector and transform it to spherical coordinates:
    $$\bar r = x\hat x + y\hat y + z\hat z$$
    Our transformation equations are the following:
    $$x=r\sin\theta\cos\phi$$
    $$y = r\sin\theta\sin\phi$$
    $$z = r\cos\theta$$
    We recall that the transformed basis vectors (not unit vectors) are obtain via the following equation (where Einstein summation notation is assumed):
    $$\bar e_i = \frac{\partial q_k}{\partial q^i}\frac{\partial \bar r}{\partial q_k}$$
    Hence our basis vectors are given by the following equations:
    $$\bar e_r = \frac{\partial q_k}{\partial r}\frac{\partial \bar r}{\partial q_k} = \sin\theta\cos\phi \hat x + \sin\theta\sin\phi \hat y + \cos\theta \hat z$$
    $$\bar e_\theta = \frac{\partial q_k}{\partial \theta}\frac{\partial \bar r}{\partial q_k} = r\cos\theta\cos\phi \hat x + r\cos\theta\sin\phi \hat y - r\sin\theta\hat z$$
    $$\bar e_\phi = \frac{\partial q_k}{\partial \phi} \frac{\partial \bar r}{\partial q_k} = -r\sin\theta\sin\phi \hat x + r\sin\theta\cos\phi \hat y $$
    Now we aim to get the normalized unit vectors $\hat r$, $\hat \theta$, and $\hat \phi$ in terms of our basis vectors:
    $$|\bar e_r| = \sqrt{\sin^2\theta\cos^2\phi + \sin^2\theta\sin^2\phi + \cos^2\theta} = \sqrt{1} = 1$$
    $$\hat r = \bar e_r$$
    $$|\bar e_\theta| = \sqrt{r^2\cos^2\theta\cos^2\phi + r^2\cos^2\theta\sin^2\phi + r^2\sin^2\theta} = r\left(\cos^2\theta + \sin^2\theta\right)^{\frac{1}{2}} = r$$
    $$r\hat \theta = \bar e_\theta$$
    $$|\bar e_\phi| = \sqrt{r^2\sin^2\theta\sin^2\phi + r^2\sin^2\theta\cos^2\phi} = r\sin\theta\sqrt{\sin^2\phi + \cos^2\phi} = r\sin\theta$$
    $$r\sin\theta \hat \phi = \bar e_\phi$$
    We again recall our vectors can be written as the following in an orthogonal coordinate system such as spherical coordinates:
    $$\bar A = A_i \bar e_i$$
    Therefore our position vector can be written as the following:
    $$\bar r = r\bar e_r + \theta \bar e_\theta + \phi \bar e_\phi$$
    Now we may use the following equation to obtain our generalized force components:
    $$Q_i = \bar F \cdot \frac{\partial \bar r}{\partial q^i}$$
    $$Q_r = \bar F \cdot \frac{\partial \bar r}{\partial r} = \bar F \cdot \bar e_r = F_r |\bar e_r| = F_r$$
    $$Q_\theta = \bar F \cdot \frac{\partial \bar r}{\partial \theta} = \bar F\cdot \bar e_\theta = F_\theta|\bar e_\theta| = rF_\theta$$
    $$Q_\phi = \bar F \cdot \frac{\partial \bar r}{\partial \phi} = \bar F \cdot \bar e_\phi = F_\phi |\bar e_\phi| = r\sin\theta F_\phi$$
    This problem can also be done starting in Cartesian coordinates.
    We can begin the problem by writing our generalized force equation:
    $$Q_j = \bar F \cdot \frac{\partial \bar r}{\partial q^j}$$
    Next we will write our position vector in terms of our spherical coordinate transformation:
    $$x = r\sin\theta\cos\phi$$
    $$y = r\sin\theta\sin\phi$$
    $$z = r\cos\theta$$
    $$\bar r = r\sin\theta\cos\phi \hat x + r\sin\theta\sin\phi \hat y + r\cos\theta \hat z$$\
    Now we can write out our generalized force components:
    $$Q_r = \bar F \cdot \frac{\partial \bar r}{\partial r} = F_x \sin\theta\cos\phi + F_y \sin\theta\sin\phi + F_z\cos\theta$$
    $$Q_\theta = \bar F \cdot \frac{\partial \bar r}{\partial \theta} = rF_x\cos\theta\cos\phi + rF_y\cos\theta\sin\phi - rF_z\sin\theta$$
    $$Q_\phi = \bar F \cdot \frac{\partial \bar r}{\partial \phi} = -rF_x\sin\theta\sin\phi + rF_y\sin\theta\cos\phi$$
    We can further write these purely in Cartesian coordinates:
    $$Q_r = \frac{xF_x}{\sqrt{x^2 + y^2 + z^2}} + \frac{yF_y}{\sqrt{x^2+y^2+z^2}} + \frac{zF_z}{\sqrt{x^2+y^2+z^2}}$$
    $$Q_\theta = \frac{zxF_x}{\sqrt{x^2+y^2}} + \frac{zyF_y}{\sqrt{x^2 + y^2}} - \sqrt{x^2 + y^2}F_z$$
    $$Q_\phi = xF_y - yF_x$$
\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
We can begin this problem using Newton's second law applied to a system in equilibrium. We will consider the system in cylindrical coordinates with forces applied on the small block being in the radial direction, and forces applied to the large block being in the $z$ direction. We can begin by noting that the small block is in uniform circular motion caused by the tension in the string and obeys the following relationship:
$$F_{r_{\text{block}_1}} = \frac{mv^2}{r_0} = m\omega_0^2 r_0 = T$$
We notice that the forces on the large block come from gravity, and the tension in the string pulling the large block upward.
$$F_{z_{\text{block}_2}} = T-Mg$$
We know the net force on the small block cannot be zero since it is experiencing uniform circular motion, which requires centripetal acceleration. Therefore the equilibrium and net zero force only applies to the large block being suspended in place by the tension in the string:
$$T - Mg = 0$$
$$T = Mg$$
Connecting this equation to the small block's force equation we obtain the following:
$$m\omega_0^2r_0 = Mg$$
If we consider the situation where the large block is lifted, we reconsider the force equation for the large block:
$$T - Mg = f \text{ where } f>0 \text{ N}$$
$$T - Mg > 0$$
$$T > Mg$$
We then transfer this condition to the force equation relating our centripetal force to our tension and obtain the following:
$$m\omega_0^2r_0 > Mg$$
We simplify the condition as the following:
$$1 > \frac{Mg}{m\omega_0^2 r_0}$$
Or in other words, the ratio of the force of gravity on the large block to the centripetal force required to keep the small block spinning, must be less than unity if the large block is to be lifted.


\end{solution}
\end{document}