{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {"vscode": {"languageId": "python"}}, "outputs": [{"data": {"text/plain": ["252.000f"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["#include <iostream>\n", "float x=12;\n", "float y=21;\n", "x*y"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "python"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "C++17", "language": "C++17", "name": "xcpp17"}, "language_info": {"codemirror_mode": "text/x-c++src", "file_extension": ".cpp", "mimetype": "text/x-c++src", "name": "C++17", "version": "17"}}, "nbformat": 4, "nbformat_minor": 2}