\section{Introduction}
\subsection{Dirac Semi-metals}
 
\indent PtTe$_2$ is a Dirac semi-metal and our material of interest. Dirac semi-metals are notably defined by their three-dimensional linear dispersions in the electronic band structure, which are the result of crystal symmetries.
PtTe$_2$ has three notable crystal symmetries, mirror symmetry, inversion symmetry, and time-reversal symmetry, although only the later two are relevant to the formation of the eponymous Dirac cones. These bands are 2-fold degenerate and therefore the Dirac point, where the conducting and valence Dirac cone dispersions converge, is 4-fold degenerate.
We can discuss the notable properties of Dirac semi-metals in the context of relativistic quantum mechanics.
\subsubsection{Relativity and Quantum Mechanics}
\indent We may begin with two crucial equations in physics. First we recall the the Schr<PERSON>inger equation, and its quantum mechanical treatment of observables as Hermitian operators on Hilbert space vectors known as wavefunctions:
\begin{equation}
    \hat H|\psi\rangle = E|\psi\rangle \quad \text{ where } \hat H = \frac{\hat p^2}{2m} = \frac{(-i\hslash\nabla)^2}{2m}
\end{equation}
Second, we recall the relativistic energy-momentum relation for a free particle:
\begin{equation}
    E^2 = p^2c^2 + m^2c^4
\end{equation}
where $E$ is the energy, $p$ is the momentum, $m$ is the mass, and $c$ is the speed of light.
A quantum-relativistic wave equation first formulated by Paul Dirac in 1928 \cite{Dirac1928}, can be derived by combining the two equations above. The Dirac equation takes the form:
\begin{equation}
    (i\hslash\gamma^\mu\partial_\mu - mc)\psi(x^\mu) = 0
\end{equation}
where $\mu \in \set{0,1,2,3}$ and $\gamma^\mu$ are the Dirac matrices. The Dirac matrices have the following property:
\begin{equation}
    \{\gamma^\mu, \gamma^\nu\} = 2g^{\mu\nu} \text{ where } g^{\mu\nu} = \text{diag}(1,-1,-1,-1)
\end{equation}
In other words, the Dirac matrices anticommute with each other and square to their respective metric signature.
We can rearrange the Dirac equation to find the Dirac Hamiltonian:
\begin{equation}
    \hat{H} \psi = i\hslash \partial_t \psi = \left(-i\hslash c\vec{\alpha} \cdot \nabla  + mc^2 \beta \right)\psi \quad \text{with } \vec{\alpha} = \gamma^0\gamma^i \hat{e}_i \text{ and } \beta = \gamma^0
    \label{eq:DiracHamiltonian}
\end{equation}
\subsubsection{Massless Dirac Fermions}
\indent It can be demonstrated that low-energy excitations in inversion and time-reversal symmetric systems are described by the massless Dirac Hamiltonian. For the purpose of demonstration we will first consider a free massless particle moving in flat Minkowski spacetime and adapt the results to a crystal lattice later.
For a massless particle ($m=0$), the Dirac Hamiltonian shown in Eq. \ref{eq:DiracHamiltonian} breaks down to:
\begin{equation}
    \hat{H} \psi = -i\hslash c\vec{\alpha} \cdot \nabla \psi = \left(c\vec{\alpha}\cdot \hat{\overline{p}}\right)\psi
\end{equation}
where the removal of the mass term allows us to treat our Dirac spinor wavefunction as a Weyl spinor, which is a two-component spinor.
Since our momentum operator is a vector of scalar operators while $\vec{\alpha}$ is a vector of matrix operators, the two operators share an eigenbasis, and thus have simulatenous eigenstates. We may commute the two to find:
\begin{equation}
    \hat{H} \psi = \left(\hat{\overline{p}}\cdot c\vec{\alpha}\right)\psi
\end{equation}
Recall that the velocity operator is given by: \begin{equation} 
    \hat{\overline{v}} = c\vec{\alpha}
\end{equation}
\indent The $\alpha$ matrices have eigenvalues of $\pm 1$ and the velocity operator has eigenvalues of $\pm c$. This indicates a free, massless particle only moves at the speed of light.
Therefore, the Hamiltonian can be written in terms of the velocity operator and the momentum operator in the following form:
\begin{equation}
    \hat{H} \psi = \left(\hat{\overline{p}}\cdot \hat{\overline{v}}\right)\psi
\end{equation}
If we apply the Hamiltonian twice this gives us an energy squared eigenvalue. Additionally, it can be shown that $\left(\vec{\alpha}\cdot \hat{\overline{p}}\right)^2 = \hat{\overline{p}}^2$.
Therefore, the square energy eigenvalue can be written as:
\begin{equation}
    E^2 = |\vec{p}|^2c^2 = \hslash^2 |\vec{k}|^2 c^2 \Rightarrow E = \pm \hslash |\vec{k}| c
\end{equation}
which is the expected relativistic energy-momentum relation for a free particle. It is notably linear in the wavevector $\vec{k}$.
\subsection{Dirac Fermions on a Crystal Lattice}
Now we may adapt our dynamics to a crystal lattice. This can be done most simply by introducing a tight binding model Hamiltonian that accounts for spin-orbit coupling (from \cite{Armitage2018}):
\begin{equation}
    \hat{H} = t\sum_{\left\langle i,j \right\rangle} \hat{a}^\dagger_i \hat{a}_j + \frac{i\lambda}{a^2}\sum_{\langle\langle i,j \rangle\rangle} \hat{a}^\dagger_i \left(\hat{\bar{\sigma}} \cdot \hat{\overline{r}}_{ij}^{\left(1\right)} \times \hat{\overline{r}}_{ij}^{\left(2\right)}\right) \hat{a}_j
\end{equation}
where $\hat{a}^\dagger_i$ and $\hat{a}_j$ are the creation and annihilation operators for electrons on sites $i$ and $j$, $t$ is the hopping parameter, $\lambda$ is the spin-orbit coupling constant, $a$ is the lattice constant, and $\hat{\bar{\sigma}}$ are the Pauli matrices. The first sum is over nearest-neighbor hopping while the second sum is over next-nearest-neighbor hopping. The vectors $\hat{\overline{r}}_{ij}^{\left(1\right)}$ and $\hat{\overline{r}}_{ij}^{\left(2\right)}$ are the vectors connecting sites $i$ and $j$ for the nearest and next-nearest neighbors, respectively.
We can then rewrite our Hamiltonian by fourier transforming the lattice operators:
\begin{equation}
    \hat{a}_i = \sum_{\vec{k}} e^{i\vec{k}\cdot\vec{R}_i} \hat{a}_{\vec{k}}
\end{equation}
This means our nearest neighbours sum over $i$ and $j$ becomes a sum over nearest neighbour vectors $\vec{G}$:
\begin{equation}
    \hat{H}_{\text{NN}} = t\sum_{\vec{k}} a^\dagger_{\vec{k}} \left[\sum_{\vec{G}} e^{i\vec{k} \cdot \vec{G}} \right]\hat{a}_{\vec{k}}
\end{equation}
We define the sum over $\vec{G}$ as $f(\vec{k})$. For low energy excitations near the Dirac point $\vec{k}_D$, we have $f(\vec{k}_D) = 0$. Hence we can expand around $\vec{k}_D$ in small $\delta \vec{k}$:
\begin{equation}
    f(\vec{k}_D + \delta \vec{k}) = f(\vec{k}_D) + \delta\vec{k} \cdot \nabla f(\vec{k}_D) + \mathcal{O}(\delta \vec{k}^2) = i\sum_{\vec{G}} \vec{G} \cdot \delta\vec{k} e^{i\vec{k}_D \cdot \vec{G}} + \mathcal{O}(\delta \vec{k}^2)
\end{equation}
Now if we remove terms of order $\delta \vec{k}^2$ and higher, we are left with:
\begin{equation}
    f(\vec{k}_D + \delta \vec{k}) = i\sum_{\vec{G}} \vec{G} \cdot \delta\vec{k} e^{i\vec{k}_D \cdot \vec{G}}
\end{equation}
On a honeycomb lattice (like PtTe$_2$) we can break this function down further, although we will have to adjust our Hamiltonian to account for this. We know our nearest neighbor vectors $\vec{G}$ are given by:
\begin{equation}
    \vec{G} \in \set{\left(a,0\right), a\left(-\frac{1}{2}, \frac{\sqrt{3}}{2}\right), a\left(-\frac{1}{2}, -\frac{\sqrt{3}}{2}\right)}
\end{equation}
Our Dirac point vector in reciprocal space is given by:
\begin{equation}
    \vec{k}_D \in \set{\frac{4\pi}{3a}\left(-\frac{1}{2}, \frac{1}{2\sqrt{3}}\right), \frac{4\pi}{3a}\left(-\frac{1}{2}, -\frac{1}{2\sqrt{3}}\right)}
\end{equation}
For our purposes here we only need a single Dirac point, so we will choose:
\begin{equation}
    \vec{k}_D = \frac{2\pi}{3a}\left(-1, \frac{1}{\sqrt{3}}\right)
\end{equation}
Now we can compute the dot product of our individual $\vec{G}$ vectors with our Dirac point vector:
\begin{equation}
    \vec{k}_D \cdot \vec{G}_1  = \frac{2\pi}{3a}\left(-1, \frac{1}{\sqrt{3}}\right) \cdot \left(a,0\right) =-\frac{2\pi}{3}
\end{equation}
\begin{equation}
   \vec{k}_D \cdot \vec{G}_2= \frac{2\pi}{3a}\left(-1, \frac{1}{\sqrt{3}}\right) \cdot a\left(-\frac{1}{2}, \frac{\sqrt{3}}{2}\right) = \frac{2\pi}{3}
\end{equation}
\begin{equation}
    \vec{k}_D \cdot \vec{G}_3 = \frac{2\pi}{3a}\left(-1, \frac{1}{\sqrt{3}}\right) \cdot a\left(-\frac{1}{2}, -\frac{\sqrt{3}}{2}\right) = 0
\end{equation}
Now lets calculate the dot product of our $\vec{G}$ vectors with our $\delta \vec{k}$ vector:
\begin{equation}
    \vec{G}_1 \cdot \delta\vec{k} = \delta k_x a
\end{equation}
\begin{equation}
    \vec{G}_2 \cdot \delta\vec{k} = -\frac{a}{2}\delta k_x + \frac{\sqrt{3}a}{2}\delta k_y
\end{equation}
\begin{equation}
    \vec{G}_3 \cdot \delta\vec{k} = -\frac{a}{2}\delta k_x - \frac{\sqrt{3}a}{2}\delta k_y
\end{equation}
Now if we compute the entire sum in our $f(\vec{k}_D + \delta \vec{k})$ function, with the Dirac point dot products placed in the exponential and the $\delta \vec{k}$ dot products in the coefficient, we are left with:
\begin{equation}
    f(\vec{k}_D + \delta \vec{k}) = ia\left[\delta k_x e^{-i\frac{2\pi}{3}} + \left(-\frac{1}{2}\delta k_x + \frac{\sqrt{3}}{2}\delta k_y\right)e^{i\frac{2\pi}{3}} + \left(-\frac{1}{2}\delta k_x - \frac{\sqrt{3}}{2}\delta k_y\right)\right]
\end{equation}
\begin{equation*}
    = \frac{ia}{2}\left[\delta k_x\left(-1 - i\sqrt{3} + \frac{1}{2} - i\frac{\sqrt{3}}{2} - 1\right) + \sqrt{3}\delta k_y\left(-\frac{1}{2} + i\frac{\sqrt{3}}{2} - 1\right)\right]
\end{equation*}
\begin{equation*}
    = \frac{ia}{2}\left[\delta k_x \left(-\frac{3}{2} - i\frac{3\sqrt{3}}{2}\right) + \sqrt{3}\delta k_y\left(-\frac{3}{2} + i\frac{\sqrt{3}}{2}\right)\right] \end{equation*} \begin{equation*}= \frac{ia}{2}\left[\delta k_x \left(-\frac{3}{2} - i\frac{3\sqrt{3}}{2}\right) + i\delta k_y\left(i\frac{3\sqrt{3}}{2} + \frac{3}{2}\right)\right]
\end{equation*}
We find the final expression to be :
\begin{equation}
    f(\vec{k}_D + \delta \vec{k}) = -\frac{3ia}{4}\left(1 + i\sqrt{3}\right)\left(\delta k_x - i\delta k_y\right) = -\frac{3ia}{2}e^{i\frac{\pi}{3}}\left(\delta k_x - i\delta k_y\right)
\end{equation}
We denote the magnitude of the prefactor (with an extra hopping parameter $t$) as the Fermi velocity (in natural units):
\begin{equation}
    v_F = \frac{3at}{2}
\end{equation}
In SI units the Fermi velocity is given by:
\begin{equation}
    v_F = \frac{3at}{2\hslash}
\end{equation}
Now that we have derived the Fermi velocity, we need to adjust our Hamiltonian for our honeycomb lattice.
We can begin with the nearest neighbor term after a Fourier transform:
\begin{equation}
    \hat{H}_{\text{NN}} = t \sum_{\vec{k}}
    \begin{pmatrix}
    \hat{a}^\dagger_{\vec{k}} && \hat{b}_{\vec{k}}
    \end{pmatrix}
    \begin{pmatrix}
    0 && f(\vec{k}) \\ 
    f^*(\vec{k}) && 0
    \end{pmatrix}
    \begin{pmatrix}
        \hat{a}_{\vec{k}} \\ 
        \hat{b}^\dagger_{\vec{k}}
    \end{pmatrix}
\end{equation}
If we full write out the matrix between our creation and annihilation operators, we find:
\begin{equation}
    \bar{\bar{f}}(\vec{k}) = \frac{1}{t}\begin{pmatrix}
    0 && -i v_F \left(\delta k_x - i\delta k_y\right) \\ 
    iv_F \left(\delta k_x + i\delta k_y\right) && 0
    \end{pmatrix} = -\frac{iv_F}{t}\left(\delta k_x\bar{\bar{\sigma}}_x + \delta k_y\bar{\bar{\sigma}}_y\right)
\end{equation}
where $\bar{\bar{\sigma}}_x$ and $\bar{\bar{\sigma}}_y$ are the Pauli matrices.
We can diagonalize our Hamiltonian into the spinor basis (neglecting phase factors) and write it in the form:
\begin{equation}
    \hat{H}_{\text{NN}} = v_F \left(\delta k_x\bar{\bar{\sigma}}_x + \delta k_y\bar{\bar{\sigma}}_y\right)
    \end{equation}
In SI units, the Hamiltonian is given by:
\begin{equation}
    \hat{H}_{\text{NN}} = \hslash v_F\left(\delta k_x\bar{\bar{\sigma}}_x + \delta k_y\bar{\bar{\sigma}}_y\right)
\end{equation}
Quickly we must address the second part of our total Hamiltonian. The next-nearest neighbor term (after our Fourier transform) is given by:
\begin{equation}
    \hat{H}_{\text{NNN}} = \frac{i\lambda}{a^2} \sum_{\langle\langle i,j \rangle\rangle}\sum_{\vec{k}} \sum_{\vec{k}'} a^\dagger_{\vec{k}} e^{-i\vec{k} \cdot \vec{R}_i}e^{i\vec{k}' \cdot \vec{R}_j} \hat{\bar{\sigma}}\cdot \vec{\nu}_{ij} \hat{a}_{\vec{k}'}
\end{equation}
where $\nu_{ij}$ encodes the cross product of the nearest and next-nearest neighbor vectors. 

We can notice that in our next-nearest neighbor sum, $\vec{R}_j - \vec{R}_i = \vec{G}$ is a nearest neighbor vector, and therefore we can use our previous result for the nearest neighbor term to simplify our expression. We find:
\begin{equation}
    \hat{H}_{\text{NNN}} = \frac{i\lambda}{a^2} \sum_{\vec{G}}\sum_{\vec{k}} \sum_{\vec{k}'} a^\dagger_{\vec{k}} e^{i\vec{k}\cdot \vec{G}} \hat{\bar{\sigma}}\cdot \vec{\nu}_{\vec{G}} \hat{a}_{\vec{k}'}
\end{equation}
We can isolate the $\vec{G}$ sum first and expand it around the Dirac point $\vec{k} \to \vec{k}_D + \delta \vec{k}$:
\begin{equation}
    \sum_{\vec{G}} e^{i\left(\vec{k}_D + \delta \vec{k}\right)\cdot \vec{G}} \nu_{\vec{G}} \approx \sum_{\vec{G}} e^{i\vec{k}_D\cdot \vec{G}} \nu_{\vec{G}} \left(1 + i\delta \vec{k}\cdot \vec{G}\right)
\end{equation}
The linear term vanishes due to the $C_3$ symmetry of the honeycomb lattice, and we are left with:
\begin{equation}
        \sum_{\vec{G}} e^{i\left(\vec{k}_D + \delta \vec{k}\right)\cdot \vec{G}} \nu_{\vec{G}} \approx \sum_{\vec{G}} e^{i\vec{k}_D\cdot \vec{G}} \nu_{\vec{G}}
\end{equation}
This is a constant offset in the Hamiltonian since there are no factors containing $\delta \vec{k}$. Therefore, we can remove it from our total Hamiltonian since it does not affect the physics of the system. Only the nearest neighbor term contributes the dynamics. We can therefore write our total Hamiltonian (in the spinor basis) as:
\begin{equation}
    \hat{H} = \hslash v_F \left(\delta k_x\bar{\bar{\sigma}}_x + \delta k_y\bar{\bar{\sigma}}_y\right) = \hslash v_F \delta \vec{k} \cdot \bar{\sigma}
\end{equation}
The derivation of this Hamiltonian is based on the derivation of the tight-binding model for graphene (see \cite{utermohlen2018graphene}).
The spinor basis that we use here is nearly identical to the Weyl spinor basis that we used in our derivation of the massless Dirac Hamiltonian. We can even transform our $\bar{\alpha}$ matrices in terms of the $\bar{\sigma}$ matrices:
\begin{equation}
    \bar{\alpha}_i = \begin{pmatrix}
        0 && \bar{\sigma}_i \\ 
        \bar{\sigma}_i && 0
    \end{pmatrix}
\end{equation}
This means in the two-component spinor basis, the massless Dirac hamiltonian corresponds to our honeycomb lattice Hamiltonian, notably with $c$ being replaced by the Fermi velocity $v_F$.
\begin{equation}
    \hat H = \hat{\bar{p}}\cdot c \bar{\alpha} \Rightarrow \hat H = \hslash v_F \delta \vec{k} \cdot \bar{\sigma}
\end{equation}
We then arrive at a linear dispersion relation for the energy:
\begin{equation}
    E\left(\vec{k}\right) = \pm\hslash v_F |\vec{k}|
\end{equation}
Where $\vec{k}$ is a small wavevector around the Dirac point. Such a dispersion is characteristic of Dirac semi-metals and the existence of which implies the existence of massless Dirac fermions in the material.

\begin{comment}
\subsection{Dirac Fermions in a Crystal Lattice}
\indent Now we must encode information of a crystal lattice into our Hamiltonian. This can be most simply done by the use of Ehrenfest's theorem. We will assume that the expectation value of the velocity operator is the average velocity of the particle in our crystal, which we usually call the Fermi velocity $v_F$. Therefore we can assume that the time derivative of the expectation value of the position operator is the Fermi velocity.
\begin{equation}
    \frac{d}{dt}\langle \hat{\overline{x}} \rangle = v_F
\end{equation}

In order to encode this into our Hamiltonian we utilize the formal definition of a time derivative in quantum mechanics, which is given by:
\begin{equation}
    \frac{d}{dt}\langle \hat{A} \rangle = \frac{i}{\hslash}\left< [\hat{H}, \hat{A}] \right> + \left< \frac{\partial \hat{A}}{\partial t} \right>
\end{equation}
Our position operator is not explicitly time-dependent, so the second term vanishes. Therefore, we can write:
\begin{equation}
    \frac{d}{dt}\langle \hat{\overline{x}} \rangle = \frac{i}{\hslash}\left< [\hat{H}, \hat{\overline{x}}] \right>
\end{equation}
We can expand this commutator and compute it by hand to find the relation between our Hamiltonian and the Fermi velocity:
\begin{equation} 
    \left|\vec{v}_F\right|^2 = \left|\frac{i}{\hslash}\left< [\hat{H}, \hat{\overline{x}}] \right> \right|^2= \left|\frac{i}{\hslash}\left< \left[\hat{\overline{p}} \cdot \hat{\overline{v}}, \hat{\overline{x}}\right] \right>\right|^2 = \left|\frac{i}{\hslash}\left< p_iv_i x_j\hat{e}_j - x_j\hat{e}_j p_iv_i \right>\right|^2
\end{equation}
Again, the position operator is a vector of scalar operators while the velocity operator is a vector of matrix operators, so they share an eigenbasis. Therefore, we can commute the two and pull the velocity operator out of both terms and write our expression in terms of the commutator of the momentum operator and the position operator:
\begin{equation}
    \left|\vec{v}_F\right|^2 = \left|\frac{i}{\hslash}\left<\left[p_i, x_j\right] v_i \hat{e}_j \right> \right|^2= \left|\frac{i}{\hslash}\left< i\hslash \delta_{ij} v_i \hat{e}_j \right> \right|^2= \left|\left<v_i \hat{e}_i\right>\right|^2 = c^2
\end{equation}
We arrive at a very troubling, yet enlightening relation between the Fermi velocity $v_F$ and the apparent speed of light $c$:
\begin{equation}
     v_F = c
\end{equation}
\indent It is imperative to be cautious with our intepretation of this result, and as such we must recall what path we took to get here. We first derived the Hamiltonian for a free, massless particle in flat Minkowski spacetime. Then, in order to adapt this to a crystal lattice, we assumed that the expectation value of the velocity operator is the average velocity of the particle in our crystal, which is the Fermi velocity $v_F$. We then used Ehrenfest's theorem to find this expectation value using our Hamiltonian. 
The result of the Fermi velocity being equal to the speed of light is either taken at face value, or it is taken as a sign that the $c$ in our Hamiltonian no longer represents the speed of light, but rather the Fermi velocity. If we choose the Fermi velocity to be the speed of light, we censor any information about a specific material and its properties. This is clearly not the valid path forward. If we interpret the $c$ in our Hamiltonian to now represent the Fermi velocity, then we encode information about the material into our Hamiltonian since the Fermi velocity is a property of the material. Our intepretation of this result should therefore not be that the average particle velocity in our system is the speed of light, but rather, in a crystal lattice, the $c$ in our Hamiltonian no longer represents the speed of light, but rather the Fermi velocity. Therefore we may write out the defining statement:
\begin{equation}
    c_{\text{lattice}} \equiv v_F
\end{equation}
Going back to our free particle solution, we adapt our energy eigenvalue equation to include the Fermi velocity and find our resulting dispersion relation:
\begin{equation}
    E^2 = \hslash^2 |\vec{k}|^2 v_F^2 \quad \Rightarrow \quad  E\left(\vec{k}\right) = \pm\hslash v_F |\vec{k}|
\end{equation}
\end{comment}
\begin{figure}
    \begin{center}
        \includegraphics[scale=0.4]{Chapters/DiracCone.png}
        \caption[Example plot of a Dirac cone dispersion]{Representation of a 3D energy-momentum space Dirac cone dispersion. The conducting and valence Dirac cone dispersions are shown in red and blue, respectively and they converge at the Dirac point.}
    \end{center}
    \label{fig:DiracDispersion}
\end{figure}
%This energy eigenvalue and the Hamiltonian that gives rise to it are in exact agreement with a tight-binding model expanded for low-energy excitations. Additionally, we have recovered a \underline{linear} dispersion relation, which is a defining characteristic of Dirac semi-metals. 
\subsection{Platinum Ditelluride (PtTe$_\mathbf{2}$) and Its Chromium (Cr) alloys}
\subsubsection{PtTe$_\mathbf{2}$}

\indent The previous section outlined the presence of three-dimensional Dirac cone dispersions in the context of relativistic quantum mechanics. We now turn our attention to the material of interest, PtTe$_2$. PtTe$_2$ is a transition metal dichalcogenide (TMD) whose crystal symmetries allow for the formation of unique electronic band dispersions. PtTe$_2$ is part of the space group $P\overline{3} m1$ (No. 164). Therefore the crystal lattice of PtTe$_2$ is symmetric under inversion, time-reversal, and C$_3$ rotation and mirroring. These symmetries, are partially responsible for the formation of the three-dimensional Dirac dispersions characteristic of Dirac semi-metals. Another important mechanism for this formation is band inversion, which occurs when the energy hierarchy between the valence and conduction bands is reversed. This effect is responsible for the generation of topologically projected surface states. The most notable of these Dirac dispersions is the Dirac cone dispersion, which is linear in $\left|\vec{k}\right|$ and indicative of the massless Dirac fermions outlined in the previous section. The material is most notably of interest for broader scientific applications, due to the Dirac cone dispersions' proximity to the Fermi level. This means the exotic states are electronically accessible. In the case of PtTe$_2$, the Dirac point, where conducting and valence Dirac cone dispersions converge, is found at binding energy $E_B = E_F - E \approx 1.0 \text{ eV}$.  While relatively close to the Fermi level, this is still energetically disconnected enough to make applications of the material's electronic properties difficult. Our group undertook a detailed ARPES study of PtTe$_2$ and a similar material PdTe$_2$, where various Dirac dispersions were fully characterized. This study provided a new interpretation to one of these dispersions \cite{Pelayo2024}.
\begin{figure}
    \begin{center}
        \includegraphics[scale=0.25]{Chapters/PtTe2.png}
    \end{center}
    \label{fig:PtTe2}
    \caption[Real space crystal structure and first Brillouin zone of PtTe$_2$]{Left: Real space crystal structure of PtTe$_2$ (Pt in green, Te in red). $a$ and $b$ are in-plane lattice vectors, and $c$ is the out-of-plane lattice vector. Right: First Brillouin zone of PtTe$_2$ with high symmetry points labeled. \cite{Yan2017}}
\end{figure}
\subsubsection{Cr$_\mathbf{x}$Pt$_\mathbf{1-x}$Te$_\mathbf{2}$}

\index We are able to investigate alterations to the crystal lattice by alloying PtTe$_2$ with chromium (Cr) to form compounds of the structure Cr$_x$Pt$_{1-x}$Te$_2$. The band structure of this alloy is slightly altered compared to the parent PtTe$_2$ crystal. The addition of magnetic Cr breaks time reversal symmetry and therefore the combination of space and time inversion symmetries. However, the combination of space-time reversal and mirror symmetries, is preserved. This allows for the preservation of the Dirac cone dispersions. The band structure is also notably raised closer to the Fermi level due to hole doping. At $x=0.35$ the Dirac point is only $0.4$ eV below the Fermi level, and at $x=0.45$ the Dirac point is above the Fermi level. While the preservation and raising of the Dirac point is a notable result, in this study we will focus on the effects of Cr substitution on the Fermi sea topology.
\subsection{The Fermi Surface and Fermi Sea}
\index Our study of the Fermi sea can begin with basic definitions. The Fermi surface is a surface in momentum space ($\vec{k}$-space) that lies at the Fermi level, $E = E_F$. In other words, the Fermi surface is the set of all momenta $\vec{k}$ for which the energy of a given particle species is equal to the Fermi Energy. In set notation the three dimensional Fermi surface $S_F^3$ is defined as:
\begin{equation}
    S_F^3 = \set{\vec{k} \in \mathbb{R}^3 \mid E(\vec{k}) = E_F}
\end{equation}
For the purposes of our study of PtTe$_2$ and its Cr alloys, we will be focusing on the two-dimensional electron Fermi surface $S_F^2$ and the set of all points laying on the two-dimensional electron dispersion for energies below the Fermi Energy, $F_S$.
\begin{equation}
    S_F^2 = \set{\vec{k} \in \mathbb{R}^2 \mid E(\vec{k}) = E_F}
\end{equation}
\begin{figure}
    \begin{center}
        \includegraphics[width=0.55\textwidth]{Chapters/PtTe2Fermi2.png}
    \end{center}
    \caption[Fermi surface of PtTe$_2$]{Fermi surface constant energy map constructed from ARPES measurements of PtTe$_2$. Colorbar represents intensity, the $x$ and $y$ momentum axes are in units of $1/\text{Å}$.}
    \label{fig:FermiSurface}
\end{figure}
\indent The set of points lying on the two-dimensional electron dispersion for energies below the Fermi level is defined as:
\begin{equation}
    F_S = \set{\left[\vec{k}, E(\vec{k})\right] \in \mathbb{R}^3 \mid E(\vec{k}) \leq E_F}
\end{equation}
\indent This construction is one representation of the Fermi sea. A notable property of the Fermi sea, by this definition, is that any possible Fermi surface can be obtained by intersecting the Fermi sea with a plane at a given energy $E$. If the Fermi level is modified by external parameters and shifted by an amount $\Delta E_F$, the new Fermi surface will be present in the Fermi sea at the energy level $E = E_F + \Delta E_F$. Such a Fermi surface can then be obtained by intersecting the Fermi sea with a plane at the energy level $E = E_F + \Delta E_F$. This is a trivial yet important property that will be necessary in our study of the Fermi surface topology.
A final consideration we must have in the mathematical construction of these objects is our inclusion of all bands in a given material's band structure. We must consider the ensemble of energy-momentum dispersions for all bands in the material. The set of these points is denoted
\begin{equation}
    F_S^E = F_S^{E_{1}} \cup F_S^{E_{2}} \cup \dots \cup F_S^{E_{N}} 
\end{equation}
where $E_i$ are different energy dispersions in the material and $N$ is the total number of bands that are visible for the energy range of interest. This set represents the Fermi sea composed of all bands.
Notably, this construction is what can be observed experimentally through angle-resolved photoemission spectroscopy (ARPES). 