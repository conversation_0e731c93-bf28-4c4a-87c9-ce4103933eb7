% written by <PERSON>
% April 2025

\documentclass[12pt]{article}
\usepackage[margin=1in]{geometry} % Adjust margins here
\usepackage{xcolor}
\usepackage{tikz}
\usepackage{indentfirst}
\usepackage{ragged2e}
\setlength{\RaggedRightParindent}{\parindent}
% Generic colored dot command
\newcommand{\colordot}[2][2pt]{\tikz[baseline=-0.5ex]\fill[#2] (0,0) circle (#1);}

% Predefined colored dots
\newcommand{\reddot}{\colordot{red}}
\newcommand{\bluedot}{\colordot{blue}}
\newcommand{\greendot}{\colordot{green}}
\newcommand{\orangedot}{\colordot{orange}}
\newcommand{\purpledot}{\colordot{purple}}
\newcommand{\yellowdot}{\colordot{yellow}}
\newcommand{\cyandot}{\colordot{cyan}}
\newcommand{\magentadot}{\colordot{magenta}}

\usepackage{graphicx, amsmath, mathtools, bm, titlesec} 
\usepackage{subcaption, color, hyperref, setspace}
\usepackage[backend=biber, style=ieee]{biblatex} % Change citation style here! % Comment out if you don't add code
\usepackage{fontspec}
\renewcommand{\contentsname}{\centering TABLE OF CONTENTS} 
\renewcommand{\listfigurename}{\centering LIST OF FIGURES} 
\addbibresource{citations.bib}
\numberwithin{equation}{section}



\setlength{\parskip}{1em}
\setlength{\parindent}{0.5in}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{comment}
\def\shrinkage{2.1mu}
\def\vecsign{\mathchar"017E}
\def\dvecsign{\smash{\stackon[-1.95pt]{\mkern-\shrinkage\vecsign}{\rotatebox{180}{$\mkern-\shrinkage\vecsign$}}}}
\def\dvec#1{\def\useanchorwidth{T}\stackon[-4.2pt]{#1}{\,\dvecsign}}
\usepackage{stackengine}
\stackMath
\usepackage{times}
\usepackage{cancel}
\usepackage{amsmath}
\usepackage{tikz}
\usepackage{changepage}
\usetikzlibrary{shapes.misc}
\usepackage{amssymb}
\usepackage{graphicx}%
\setcounter{MaxMatrixCols}{30}
\newtheorem{theorem}{Theorem}
\newtheorem{acknowledgement}[theorem]{Acknowledgement}
\newtheorem{algorithm}[theorem]{Algorithm}
\newtheorem{axiom}{Axiom}
\newtheorem{case}[theorem]{Case}
\newtheorem{claim}[theorem]{Claim}
\newtheorem{conclusion}[theorem]{Conclusion}
\newtheorem{condition}[theorem]{Condition}
\newtheorem{conjecture}[theorem]{Conjecture}
\newtheorem{corollary}[theorem]{Corollary}
\newtheorem{criterion}[theorem]{Criterion}
\newtheorem{definition}[theorem]{Definition}
\newtheorem{example}[theorem]{Example}
\newtheorem{exercise}[theorem]{Exercise}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{notation}[theorem]{Notation}
\newtheorem{problem}[theorem]{Problem}
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{remark}[theorem]{Remark}
\newtheorem{summary}[theorem]{Summary}
\setmainfont{Times New Roman}
\newenvironment{proof}[1][Proof]{\textbf{#1.} }{\ \rule{0.5em}{0.5em}}
\newenvironment{solution}[1][Solution]{\textbf{#1}}{\ \rule{0.5em}{0.5em}}
\newcommand{\Q}{\mathbb{Q}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\Z}{\mathbb{Z}}
\newcommand{\N}{\mathbb{N}}
\newcommand{\Lagr}{\mathcal{L}}
\newcommand{\lagr}{\mathcal{L}_0}
\newcommand{\lb}{\left\{}
\newcommand{\rb}{\right\}}
\newcommand{\set}[1]{\left\{#1\right\}}
\newcommand{\pr}[1]{\left(\{#1\right\})}
\newcommand{\dbar}[1]{\bar{\bar{#1}}}
\newcommand{\tbar}[1]{\bar{\bar{\bar{#1}}}}
\renewcommand{\vec}[1]{\overline{\mathbf{#1}}}

% Thing to keep in mind when using this:
%
%   Whenever you add a figure, instead of using caption as 
%   \caption{Here's my caption}, use it as 
%   \caption[short summary]{Here's my caption}, this way whatever
%   appears between the '[]' will appear in the list of figures 
%   but not on the figure. Usually you write a short summary 
%   there.
%
%   All appendix sections need the asterisk \section*{} to avoid
%   having them in the table of contents.
%   
%   If you don't want to include any code, you can get rid of 
%   the minted bibliography, it slows down the compilation time
%   a decent amount.
%   
%   My Venmo is @dcohencobos


\begin{document}
\pagenumbering{roman} % Roman page numbering

% Insert title, abstract, acknowledgments
\input{title}
\newpage
\input{abstract}
\newpage
\input{acknowledgments}
\newpage

% Insert table of contents and list of figures. 
% List of tables would go here too
\singlespacing  
\tableofcontents
\newpage
\listoffigures
\pagebreak
\doublespacing
\pagenumbering{arabic} % Switch to arabic page numbers

% Insert chapters here!
\input{Chapters/Intro}
\clearpage

\input{Chapters/Walecka}
\clearpage

\input{Chapters/DensityFunctionalApproach}
\clearpage

\input{Chapters/Conclusion.tex}
\clearpage

% Appendix goes here
%\appendix
%\input{Appendix}

% Bibliography goes here
\clearpage
\printbibliography[title={\centering REFERENCES}]
\nocite{*} % This adds unused citations too

% Balls
\end{document}