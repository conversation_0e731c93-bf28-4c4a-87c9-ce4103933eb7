{"latex-workshop.latex.tools": [{"name": "xelatex", "command": "xelatex", "args": ["-shell-escape", "-synctex=1", "-interaction=nonstopmode", "-file-line-error", "%DOCFILE%"], "env": {}}, {"name": "bibtex", "command": "bibtex", "args": ["%DOCFILE%"], "env": {}}, {"name": "latexmk", "command": "latexmk", "args": ["-xelatex", "-shell-escape", "-synctex=1", "-interaction=nonstopmode", "-file-line-error", "%DOCFILE%"], "env": {}}], "latex-workshop.latex.recipes": [{"name": "Full Build (XeLaTeX + BibTeX)", "tools": ["xelatex", "bibtex", "xelatex", "xelatex"]}, {"name": "latexmk (XeLaTeX)", "tools": ["latexmk"]}, {"name": "Quick Build (XeLaTeX only)", "tools": ["xelatex"]}], "latex-workshop.latex.recipe.default": "Full Build (XeLaTeX + BibTeX)", "latex-workshop.latex.autoClean.run": "onBuilt", "latex-workshop.latex.clean.fileTypes": ["*.aux", "*.bbl", "*.blg", "*.idx", "*.ind", "*.lof", "*.lot", "*.out", "*.toc", "*.acn", "*.acr", "*.alg", "*.glg", "*.glo", "*.gls", "*.fls", "*.log", "*.fdb_latexmk", "*.snm", "*.synctex.gz", "*.nav", "*.vrb", "*.run.xml", "*.bcf"], "latex-workshop.view.pdf.viewer": "tab", "latex-workshop.latex.build.forceRecipeUsage": false, "latex-workshop.latex.autoBuild.run": "onSave", "latex-workshop.message.error.show": true, "latex-workshop.message.warning.show": true, "latex-workshop.showContextMenu": true, "latex-workshop.intellisense.package.enabled": true, "latex-workshop.intellisense.unimathsymbols.enabled": true, "latex-workshop.latex.watch.files.ignore": ["**/*.bbx", "**/*.cbx", "**/*.cfg", "**/*.clo", "**/*.cls", "**/*.def", "**/*.fmt", "**/*.lbx", "**/*.map", "**/*.pfb", "**/*.tfm"]}