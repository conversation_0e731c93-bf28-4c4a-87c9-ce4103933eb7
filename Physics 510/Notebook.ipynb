{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["**Problem 1**\n", "\n", "Solve for the motion of a spring "]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from sympy import *\n", "from galgebra import *"]}, {"cell_type": "code", "execution_count": 612, "metadata": {}, "outputs": [], "source": ["t,m,om = symbols('t m omega')\n", "r = Function('r')(t)\n", "th = Function(\"theta\")(t)"]}, {"cell_type": "code", "execution_count": 613, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{m \\omega^{2} r^{2}{\\left(t \\right)}}{2} + 2 m \\omega r^{2}{\\left(t \\right)} \\frac{d}{d t} \\theta{\\left(t \\right)} + \\frac{m \\left(r^{2}{\\left(t \\right)} \\left(\\frac{d}{d t} \\theta{\\left(t \\right)}\\right)^{2} + \\left(\\frac{d}{d t} r{\\left(t \\right)}\\right)^{2}\\right)}{2}$"], "text/plain": ["m*omega**2*r(t)**2/2 + 2*m*omega*r(t)**2*Derivative(theta(t), t) + m*(r(t)**2*Derivative(theta(t), t)**2 + Derivative(r(t), t)**2)/2"]}, "execution_count": 613, "metadata": {}, "output_type": "execute_result"}], "source": ["L = Rational(1,2)*m*(diff(r,t)**2 + r**2*diff(th,t)**2) + 2*m*r**2*om*diff(th,t) + Rational(1,2)*m*om**2*r**2 \n", "L"]}, {"cell_type": "code", "execution_count": 614, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle m \\omega^{2} r{\\left(t \\right)} + 4 m \\omega r{\\left(t \\right)} \\frac{d}{d t} \\theta{\\left(t \\right)} + m r{\\left(t \\right)} \\left(\\frac{d}{d t} \\theta{\\left(t \\right)}\\right)^{2} = m \\frac{d^{2}}{d t^{2}} r{\\left(t \\right)}$"], "text/plain": ["Eq(m*omega**2*r(t) + 4*m*omega*r(t)*Derivative(theta(t), t) + m*r(t)*Derivative(theta(t), t)**2, m*Derivative(r(t), (t, 2)))"]}, "execution_count": 614, "metadata": {}, "output_type": "execute_result"}], "source": ["Eq(diff(L, r), diff(diff(L,diff(r,t)),t))"]}, {"cell_type": "code", "execution_count": 615, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 0 = 4 m \\omega r{\\left(t \\right)} \\frac{d}{d t} r{\\left(t \\right)} + m r^{2}{\\left(t \\right)} \\frac{d^{2}}{d t^{2}} \\theta{\\left(t \\right)} + 2 m r{\\left(t \\right)} \\frac{d}{d t} r{\\left(t \\right)} \\frac{d}{d t} \\theta{\\left(t \\right)}$"], "text/plain": ["Eq(0, 4*m*omega*r(t)*Derivative(r(t), t) + m*r(t)**2*Derivative(theta(t), (t, 2)) + 2*m*r(t)*Derivative(r(t), t)*Derivative(theta(t), t))"]}, "execution_count": 615, "metadata": {}, "output_type": "execute_result"}], "source": ["Eq(diff(L, th), diff(diff(L,diff(th,t)),t))"]}, {"cell_type": "code", "execution_count": 616, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(e_\\rho, e_\\theta, e_z)\n"]}], "source": ["from sympy import symbols\n", "from galgebra.ga import Ga\n", "\n", "# Define the symbols\n", "phi = Function('phi')\n", "\n", "# Define the metric\n", "metric = Matrix(([1,0,0], [0,r**2,0], [0,0,1]))\n", "\n", "# Create a 3D Cartesian geometric algebra\n", "ga = Ga(r'e_\\rho e_\\theta e_z', g=metric, coords=symbols('rho theta z'))\n", "\n", "# Print the basis vectors\n", "print(ga.mv())\n", "\n", "er = ga.mv()[0]\n", "ep = ga.mv()[1]\n", "ez = ga.mv()[2]"]}, {"cell_type": "code", "execution_count": 617, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}1 & 1 & 2\\\\1 & 2 & 2\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[1, 1, 2],\n", "[1, 2, 2]])"]}, "execution_count": 617, "metadata": {}, "output_type": "execute_result"}], "source": ["Matrix(((1,1,2),(1,2,2)))"]}, {"cell_type": "code", "execution_count": 618, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\omega \\mathbf{e}_{z}$"], "text/plain": ["omega*e_z"]}, "execution_count": 618, "metadata": {}, "output_type": "execute_result"}], "source": ["Om = om*ez\n", "Om"]}, {"cell_type": "code", "execution_count": 619, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle r{\\left(t \\right)} \\mathbf{e}_{\\rho}$"], "text/plain": ["r*e_\\rho"]}, "execution_count": 619, "metadata": {}, "output_type": "execute_result"}], "source": ["R = r*er\n", "R"]}, {"cell_type": "code", "execution_count": 620, "metadata": {}, "outputs": [], "source": ["Rdot = diff(r,t)*er + diff(th,t)*ep"]}, {"cell_type": "code", "execution_count": 621, "metadata": {}, "outputs": [], "source": ["I = er*(ep/r)*ez"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 622, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{d}{d t} r{\\left(t \\right)} \\mathbf{e}_{\\rho} + \\frac{d}{d t} \\theta{\\left(t \\right)} \\mathbf{e}_{\\theta}$"], "text/plain": ["D{t}r*e_\\rho + D{t}theta*e_\\theta"]}, "execution_count": 622, "metadata": {}, "output_type": "execute_result"}], "source": ["Rdot"]}, {"cell_type": "code", "execution_count": 623, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{1}{r{\\left(t \\right)}} \\mathbf{e}_{\\rho}\\wedge \\mathbf{e}_{\\theta}\\wedge \\mathbf{e}_{z}$"], "text/plain": ["e_\\rho^e_\\theta^e_z/r"]}, "execution_count": 623, "metadata": {}, "output_type": "execute_result"}], "source": ["I"]}, {"cell_type": "code", "execution_count": 624, "metadata": {}, "outputs": [], "source": ["ephi = ep/r"]}, {"cell_type": "code", "execution_count": 625, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\omega r^{2}{\\left(t \\right)} \\frac{d}{d t} \\theta{\\left(t \\right)}$"], "text/plain": ["omega*r**2*D{t}theta"]}, "execution_count": 625, "metadata": {}, "output_type": "execute_result"}], "source": ["Rdot|(-I*(Om^R))"]}, {"cell_type": "code", "execution_count": 626, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\omega \\mathbf{e}_{\\theta}$"], "text/plain": ["omega*e_\\theta"]}, "execution_count": 626, "metadata": {}, "output_type": "execute_result"}], "source": ["-I*(Om^R)"]}, {"cell_type": "code", "execution_count": 627, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\omega \\mathbf{e}_{\\theta}$"], "text/plain": ["omega*e_\\theta"]}, "execution_count": 627, "metadata": {}, "output_type": "execute_result"}], "source": ["-I*(Om^R)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 628, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle  0 $"], "text/plain": ["0"]}, "execution_count": 628, "metadata": {}, "output_type": "execute_result"}], "source": ["diff(ep,t)"]}, {"cell_type": "code", "execution_count": 629, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\omega r^{2}{\\left(t \\right)} \\frac{d}{d t} \\theta{\\left(t \\right)}$"], "text/plain": ["-omega*r**2*D{t}theta"]}, "execution_count": 629, "metadata": {}, "output_type": "execute_result"}], "source": ["((-I*(Om^Rdot)))|R"]}, {"cell_type": "code", "execution_count": 630, "metadata": {}, "outputs": [], "source": ["P,Q,x,p,k = symbols('P Q x p k')"]}, {"cell_type": "code", "execution_count": 631, "metadata": {}, "outputs": [], "source": ["H = p**2/(2*m) + k/4 *x**4"]}, {"cell_type": "code", "execution_count": 632, "metadata": {}, "outputs": [], "source": ["FF = Rational(1,3) * P*x**3\n", "QQ = diff(FF, P)\n", "pp = diff(FF,x)"]}, {"cell_type": "code", "execution_count": 632, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/IPython/core/async_helpers.py:129\u001b[0m, in \u001b[0;36m_pseudo_sync_runner\u001b[0;34m(coro)\u001b[0m\n\u001b[1;32m    121\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    122\u001b[0m \u001b[38;5;124;03mA runner that does not really allow async execution, and just advance the coroutine.\u001b[39;00m\n\u001b[1;32m    123\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    126\u001b[0m \u001b[38;5;124;03mCredit to <PERSON>\u001b[39;00m\n\u001b[1;32m    127\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    128\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 129\u001b[0m     \u001b[43mcoro\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01mN<PERSON>\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    130\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    131\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m exc\u001b[38;5;241m.\u001b[39mvalue\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/IPython/core/interactiveshell.py:3284\u001b[0m, in \u001b[0;36mInteractiveShell.run_cell_async\u001b[0;34m(self, raw_cell, store_history, silent, shell_futures, transformed_cell, preprocessing_exc_tuple, cell_id)\u001b[0m\n\u001b[1;32m   3282\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m store_history:\n\u001b[1;32m   3283\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhistory_manager \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m-> 3284\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhistory_manager\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstore_inputs\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecution_count\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcell\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mraw_cell\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3285\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m silent:\n\u001b[1;32m   3286\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlogger\u001b[38;5;241m.\u001b[39mlog(cell, raw_cell)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/IPython/core/history.py:792\u001b[0m, in \u001b[0;36mHistoryManager.store_inputs\u001b[0;34m(self, line_num, source, source_raw)\u001b[0m\n\u001b[1;32m    789\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_hist_parsed\u001b[38;5;241m.\u001b[39mappend(source)\n\u001b[1;32m    790\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_hist_raw\u001b[38;5;241m.\u001b[39mappend(source_raw)\n\u001b[0;32m--> 792\u001b[0m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdb_input_cache_lock\u001b[49m\u001b[43m:\u001b[49m\n\u001b[1;32m    793\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdb_input_cache\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mappend\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[43mline_num\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msource\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msource_raw\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    794\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# Trigger to flush cache and write to DB.\u001b[39;49;00m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["xx = solve(QQ - Q, x)[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{3 \\cdot \\sqrt[3]{3} Q^{\\frac{4}{3}} \\cdot \\left(2 P^{2} + k m\\right)}{4 m}$"], "text/plain": ["3*3**(1/3)*Q**(4/3)*(2*P**2 + k*m)/(4*m)"]}, "execution_count": 192, "metadata": {}, "output_type": "execute_result"}], "source": ["(H.subs(x,xx).subs(p,pp).subs(x,xx)).simplify()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 3 \\cdot \\sqrt[3]{3}$"], "text/plain": ["3*3**(1/3)"]}, "execution_count": 198, "metadata": {}, "output_type": "execute_result"}], "source": ["(3**(<PERSON><PERSON>(4,3)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sympy import *"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sigma, mu0, omega, epsilon0 = symbols('sigma mu_0 omega epsilon_0')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l = sqrt(2/(mu0*epsilon0))*(2*pi)/(omega*sqrt(1+sqrt(1+sigma**2/(omega**2*epsilon0**2))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 2.89423149427379 \\cdot 10^{-6}$"], "text/plain": ["2.89423149427379e-6"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["((l.subs(epsilon0,8.854*10**(-12)).subs(mu0, 4*pi*10**(-7)).subs(omega,5*10**9).subs(sigma, 3.8*10**7)).evalf()/(2*pi)).evalf()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = sqrt(2/(mu0*epsilon0))/(omega*sqrt(sqrt(1+sigma**2/(omega**2*epsilon0**2))-1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\sqrt{2} \\sqrt{\\frac{1}{\\epsilon_{0} \\mu_{0}}}}{\\omega \\sqrt{\\sqrt{1 + \\frac{\\sigma^{2}}{\\epsilon_{0}^{2} \\omega^{2}}} - 1}}$"], "text/plain": ["sqrt(2)*sqrt(1/(epsilon_0*mu_0))/(omega*sqrt(sqrt(1 + sigma**2/(epsilon_0**2*omega**2)) - 1))"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["d"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 2.89423149764557 \\cdot 10^{-6}$"], "text/plain": ["2.89423149764557e-6"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["(d.subs(epsilon0,8.854*10**(-12)).subs(mu0, 4*pi*10**(-7)).subs(omega,5*10**9).subs(sigma, 3.8*10**7)).evalf()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q,R,beta, th = symbols('q R beta theta')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EE = q*R*(1-beta**2)/(R**3*(1-beta**2*sin(th)**2))**(Rational(3,2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{R q \\left(1 - \\beta^{2}\\right)}{\\left(R^{3} \\left(- \\beta^{2} \\sin^{2}{\\left(\\theta \\right)} + 1\\right)\\right)^{\\frac{3}{2}}}$"], "text/plain": ["R*q*(1 - beta**2)/(R**3*(-beta**2*sin(theta)**2 + 1))**(3/2)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["EE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sympy as sp\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Define your variables\n", "q, R, beta, th = sp.symbols('q R beta th')\n", "\n", "# Your expression\n", "EE = q*R*(1-beta**2)/(R**3*(1-beta**2*sp.sin(th)**2))**(sp.<PERSON><PERSON>(3,2))\n", "\n", "# Convert sympy expression to lambda function\n", "f = sp.lambdify((q, R, beta, th), EE, \"numpy\")\n", "\n", "# Define your range of inputs\n", "R_values = np.linspace(1, 10, 100)\n", "\n", "# Create a 2D plot\n", "fig, ax = plt.subplots()\n", "\n", "# Input your values for q, beta, and theta here\n", "configs = [(1, 0.5, np.pi/4), (2, 0.3, np.pi/6), (3, 0.7, np.pi/3)]  # example configurations\n", "\n", "# Plot for different inputs\n", "for i, (q, beta, th) in enumerate(configs):\n", "    color = plt.cm.jet(i/len(configs))  # Change color for each configuration\n", "    ax.plot(R_values, f(q, R_values, beta, th), color=color, label=f'q={q}, beta={beta}, th={th}')\n", "\n", "ax.legend()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sympy as sp\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Define your variables\n", "q, R, beta, th = sp.symbols('q R beta th')\n", "\n", "# Your expression\n", "EE = q*R*(1-beta**2)/(R**3*(1-beta**2*sp.sin(th)**2))**(sp.<PERSON><PERSON>(3,2))\n", "\n", "# Convert sympy expression to lambda function\n", "f = sp.lambdify((q, R, beta, th), EE, \"numpy\")\n", "\n", "# Define your range of inputs\n", "th_values = np.linspace(0, 2*np.pi, 1000)\n", "\n", "# Create a 2D plot\n", "fig, ax = plt.subplots()\n", "\n", "# Input your values for q, R, and beta here\n", "configs = [(1, 2, 0), (1, 2, 0.2), (1, 2, 0.4), (1, 2, 0.6), (1, 2, 0.8), (1, 2, 0.9)] # example configurations\n", "\n", "# Plot for different inputs\n", "for i, (q, R, beta) in enumerate(configs):\n", "    color = plt.cm.jet(i/len(configs))  # Change color for each configuration\n", "    ax.plot(th_values, f(q, R, beta, th_values), color=color, label=fr'$q$={q}, $R$={R}, $\\beta$={beta}')\n", "\n", "ax.legend()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sympy as sp\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.ticker as ticker\n", "\n", "# Define your variables\n", "q, R, beta, th = sp.symbols('q R beta th')\n", "\n", "# Your expression\n", "EE = q*R*(1-beta**2)/(R**3*(1-beta**2*sp.sin(th)**2))**(sp.<PERSON><PERSON>(3,2))\n", "\n", "# Convert sympy expression to lambda function\n", "f = sp.lambdify((q, R, beta, th), EE, \"numpy\")\n", "\n", "# Define your range of inputs\n", "th_values = np.linspace(0, 2*np.pi, 1000)\n", "\n", "# Create a 2D plot\n", "fig, ax = plt.subplots()\n", "\n", "# Input your values for q, R, and beta here\n", "configs = [(1, 0.75, 0.5), (1, 0.875, 0.5), (1, 1, 0.5), (1, 1.125, 0.5)] # example configurations\n", "\n", "# Plot for different inputs\n", "for i, (q, R, beta) in enumerate(configs):\n", "    color = plt.cm.jet(i/len(configs))  # Change color for each configuration\n", "    ax.plot(th_values, f(q, R, beta, th_values), color=color, label=fr'$q$={q}, $R$={R}, $\\beta$={beta}')\n", "\n", "# Set x-axis labels as unit circle divisions of 2π\n", "ax.xaxis.set_major_locator(ticker.MultipleLocator(base=np.pi/2))\n", "ax.xaxis.set_major_formatter(ticker.FuncFormatter(lambda val,pos: {np.pi/2: r'$\\frac{\\pi}{2}$', np.pi: r'$\\pi$', 3*np.pi/2: r'$\\frac{3\\pi}{2}$', 2*np.pi: r'$2\\pi$'}.get(val, r'$0$')))\n", "\n", "ax.legend()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t=symbols('t')\n", "x1 = Function('x_1')(t)\n", "x2 = Function('x_2')(t)\n", "y1 = Function('y_1')(t)\n", "y2 = Function('y_2')(t)\n", "m1, m2 ,k, g = symbols('m_1 m_2 k g', positive=True, real=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["L = Rational(1,2)*m1*(diff(x1,t)**2 + diff(y1,t)**2) + Rational(1,2)*m2*(diff(x2,t)**2 + diff(y2,t)**2) - Rational(1,2)*k*(x2-x1)**2 - Rational(1,2)*k*(y2-y1)**2 - m1*g*y1 - m2*g*y2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - g m_{1} y_{1}{\\left(t \\right)} - g m_{2} y_{2}{\\left(t \\right)} - \\frac{k \\left(- x_{1}{\\left(t \\right)} + x_{2}{\\left(t \\right)}\\right)^{2}}{2} - \\frac{k \\left(- y_{1}{\\left(t \\right)} + y_{2}{\\left(t \\right)}\\right)^{2}}{2} + \\frac{m_{1} \\left(\\left(\\frac{d}{d t} x_{1}{\\left(t \\right)}\\right)^{2} + \\left(\\frac{d}{d t} y_{1}{\\left(t \\right)}\\right)^{2}\\right)}{2} + \\frac{m_{2} \\left(\\left(\\frac{d}{d t} x_{2}{\\left(t \\right)}\\right)^{2} + \\left(\\frac{d}{d t} y_{2}{\\left(t \\right)}\\right)^{2}\\right)}{2}$"], "text/plain": ["-g*m_1*y_1(t) - g*m_2*y_2(t) - k*(-x_1(t) + x_2(t))**2/2 - k*(-y_1(t) + y_2(t))**2/2 + m_1*(Derivative(x_1(t), t)**2 + Derivative(y_1(t), t)**2)/2 + m_2*(Derivative(x_2(t), t)**2 + Derivative(y_2(t), t)**2)/2"]}, "execution_count": 237, "metadata": {}, "output_type": "execute_result"}], "source": ["L"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["th = Function('theta')(t)\n", "phi = Function('phi')(t)\n", "L1, L2, d1, d2 = symbols('ell_1 ell_2 d_1 d_2',real=True, positive=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Lp = L.subs(y1, -L1*cos(th)).subs(y2, d1 - L2*cos(phi)).subs(x1, L1*sin(th)).subs(x2, d2+ L2*sin(phi))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{d_{1}^{2} k}{2} - d_{1} \\ell_{1} k \\cos{\\left(\\theta{\\left(t \\right)} \\right)} + d_{1} \\ell_{2} k \\cos{\\left(\\phi{\\left(t \\right)} \\right)} - d_{1} g m_{2} - \\frac{d_{2}^{2} k}{2} + d_{2} \\ell_{1} k \\sin{\\left(\\theta{\\left(t \\right)} \\right)} - d_{2} \\ell_{2} k \\sin{\\left(\\phi{\\left(t \\right)} \\right)} - \\frac{\\ell_{1}^{2} k \\sin^{2}{\\left(\\theta{\\left(t \\right)} \\right)}}{2} - \\frac{\\ell_{1}^{2} k \\cos^{2}{\\left(\\theta{\\left(t \\right)} \\right)}}{2} + \\frac{\\ell_{1}^{2} m_{1} \\sin^{2}{\\left(\\theta{\\left(t \\right)} \\right)} \\left(\\frac{d}{d t} \\theta{\\left(t \\right)}\\right)^{2}}{2} + \\frac{\\ell_{1}^{2} m_{1} \\cos^{2}{\\left(\\theta{\\left(t \\right)} \\right)} \\left(\\frac{d}{d t} \\theta{\\left(t \\right)}\\right)^{2}}{2} + \\ell_{1} \\ell_{2} k \\sin{\\left(\\phi{\\left(t \\right)} \\right)} \\sin{\\left(\\theta{\\left(t \\right)} \\right)} + \\ell_{1} \\ell_{2} k \\cos{\\left(\\phi{\\left(t \\right)} \\right)} \\cos{\\left(\\theta{\\left(t \\right)} \\right)} + \\ell_{1} g m_{1} \\cos{\\left(\\theta{\\left(t \\right)} \\right)} - \\frac{\\ell_{2}^{2} k \\sin^{2}{\\left(\\phi{\\left(t \\right)} \\right)}}{2} - \\frac{\\ell_{2}^{2} k \\cos^{2}{\\left(\\phi{\\left(t \\right)} \\right)}}{2} + \\frac{\\ell_{2}^{2} m_{2} \\sin^{2}{\\left(\\phi{\\left(t \\right)} \\right)} \\left(\\frac{d}{d t} \\phi{\\left(t \\right)}\\right)^{2}}{2} + \\frac{\\ell_{2}^{2} m_{2} \\cos^{2}{\\left(\\phi{\\left(t \\right)} \\right)} \\left(\\frac{d}{d t} \\phi{\\left(t \\right)}\\right)^{2}}{2} + \\ell_{2} g m_{2} \\cos{\\left(\\phi{\\left(t \\right)} \\right)}$"], "text/plain": ["-d_1**2*k/2 - d_1*ell_1*k*cos(theta(t)) + d_1*ell_2*k*cos(phi(t)) - d_1*g*m_2 - d_2**2*k/2 + d_2*ell_1*k*sin(theta(t)) - d_2*ell_2*k*sin(phi(t)) - ell_1**2*k*sin(theta(t))**2/2 - ell_1**2*k*cos(theta(t))**2/2 + ell_1**2*m_1*sin(theta(t))**2*Derivative(theta(t), t)**2/2 + ell_1**2*m_1*cos(theta(t))**2*Derivative(theta(t), t)**2/2 + ell_1*ell_2*k*sin(phi(t))*sin(theta(t)) + ell_1*ell_2*k*cos(phi(t))*cos(theta(t)) + ell_1*g*m_1*cos(theta(t)) - ell_2**2*k*sin(phi(t))**2/2 - ell_2**2*k*cos(phi(t))**2/2 + ell_2**2*m_2*sin(phi(t))**2*Derivative(phi(t), t)**2/2 + ell_2**2*m_2*cos(phi(t))**2*Derivative(phi(t), t)**2/2 + ell_2*g*m_2*cos(phi(t))"]}, "execution_count": 264, "metadata": {}, "output_type": "execute_result"}], "source": ["Lp.expand().doit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Lpp = Lp.doit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EOM1 = Eq(diff(diff(Lpp , diff(th,t)).simplify(),t), diff(Lpp, th))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\ell_{1}^{2} m_{1} \\frac{d^{2}}{d t^{2}} \\theta{\\left(t \\right)} = d_{1} \\ell_{1} k \\theta{\\left(t \\right)} - \\frac{d_{2} \\ell_{1} k \\theta^{2}{\\left(t \\right)}}{2} + d_{2} \\ell_{1} k + \\frac{\\ell_{1} \\ell_{2} k \\phi^{2}{\\left(t \\right)} \\theta{\\left(t \\right)}}{2} - \\frac{\\ell_{1} \\ell_{2} k \\phi{\\left(t \\right)} \\theta^{2}{\\left(t \\right)}}{2} + \\ell_{1} \\ell_{2} k \\phi{\\left(t \\right)} - \\ell_{1} \\ell_{2} k \\theta{\\left(t \\right)} - \\ell_{1} g m_{1} \\theta{\\left(t \\right)}$"], "text/plain": ["Eq(ell_1**2*m_1*Derivative(theta(t), (t, 2)), d_1*ell_1*k*theta(t) - d_2*ell_1*k*theta(t)**2/2 + d_2*ell_1*k + ell_1*ell_2*k*phi(t)**2*theta(t)/2 - ell_1*ell_2*k*phi(t)*theta(t)**2/2 + ell_1*ell_2*k*phi(t) - ell_1*ell_2*k*theta(t) - ell_1*g*m_1*theta(t))"]}, "execution_count": 267, "metadata": {}, "output_type": "execute_result"}], "source": ["(EOM1.subs(sin(th), th).subs(sin(phi), phi).subs(sin(phi - th), phi - th).subs(cos(th), 1-th**2/2).subs(cos(phi),1-phi**2/2)).expand()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\ell_{2}^{2} m_{2} \\frac{d^{2}}{d t^{2}} \\phi{\\left(t \\right)} = - d_{1} \\ell_{2} k \\phi{\\left(t \\right)} + \\frac{d_{2} \\ell_{2} k \\phi^{2}{\\left(t \\right)}}{2} - d_{2} \\ell_{2} k - \\frac{\\ell_{1} \\ell_{2} k \\phi^{2}{\\left(t \\right)} \\theta{\\left(t \\right)}}{2} + \\frac{\\ell_{1} \\ell_{2} k \\phi{\\left(t \\right)} \\theta^{2}{\\left(t \\right)}}{2} - \\ell_{1} \\ell_{2} k \\phi{\\left(t \\right)} + \\ell_{1} \\ell_{2} k \\theta{\\left(t \\right)} - \\ell_{2} g m_{2} \\phi{\\left(t \\right)}$"], "text/plain": ["Eq(ell_2**2*m_2*Derivative(phi(t), (t, 2)), -d_1*ell_2*k*phi(t) + d_2*ell_2*k*phi(t)**2/2 - d_2*ell_2*k - ell_1*ell_2*k*phi(t)**2*theta(t)/2 + ell_1*ell_2*k*phi(t)*theta(t)**2/2 - ell_1*ell_2*k*phi(t) + ell_1*ell_2*k*theta(t) - ell_2*g*m_2*phi(t))"]}, "execution_count": 268, "metadata": {}, "output_type": "execute_result"}], "source": ["EOM2 = Eq(diff(diff(Lpp , diff(phi,t)).simplify(),t), diff(Lpp, phi))\n", "(EOM2.subs(sin(th), th).subs(sin(phi), phi).subs(sin(phi - th), phi - th).subs(cos(th), 1-th**2/2).subs(cos(phi),1-phi**2/2)).expand()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Lpp2 = Lpp.subs(sin(th), th).subs(sin(phi), phi).subs(sin(phi - th), phi - th).subs(cos(th), 1-th**2/2).subs(cos(phi),1-phi**2/2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EOM12 = Eq(diff(diff(Lpp2 , diff(th,t)).simplify(),t), diff(Lpp2, th))\n", "EOM22 = Eq(diff(diff(Lpp2 , diff(phi,t)).simplify(),t), diff(Lpp2, phi))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\ell_{1}^{2} m_{1} \\frac{d^{2}}{d t^{2}} \\theta{\\left(t \\right)} = d_{1} \\ell_{1} k \\theta{\\left(t \\right)} + d_{2} \\ell_{1} k + \\ell_{1} \\ell_{2} k \\phi{\\left(t \\right)} - \\ell_{1} \\ell_{2} k \\theta{\\left(t \\right)} - \\ell_{1} g m_{1} \\theta{\\left(t \\right)}$"], "text/plain": ["Eq(ell_1**2*m_1*Derivative(theta(t), (t, 2)), d_1*ell_1*k*theta(t) + d_2*ell_1*k + ell_1*ell_2*k*phi(t) - ell_1*ell_2*k*theta(t) - ell_1*g*m_1*theta(t))"]}, "execution_count": 271, "metadata": {}, "output_type": "execute_result"}], "source": ["EOM12.simplify().subs(th**3, 0).subs(th**2, 0).subs(phi**3, 0).subs(phi**2,0).expand()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\ell_{1} m_{1} \\frac{d^{2}}{d t^{2}} \\theta{\\left(t \\right)} = d_{1} k \\theta{\\left(t \\right)} + d_{2} k + \\ell_{2} k \\phi{\\left(t \\right)} - \\ell_{2} k \\theta{\\left(t \\right)} - g m_{1} \\theta{\\left(t \\right)}$"], "text/plain": ["Eq(ell_1*m_1*Derivative(theta(t), (t, 2)), d_1*k*theta(t) + d_2*k + ell_2*k*phi(t) - ell_2*k*theta(t) - g*m_1*theta(t))"]}, "execution_count": 272, "metadata": {}, "output_type": "execute_result"}], "source": ["bruh1 = EOM12.simplify().subs(th**3, 0).subs(th**2, 0).subs(phi**3, 0).subs(phi**2,0).expand()\n", "Eq(bruh1.lhs/L1, bruh1.rhs/L1).expand()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\ell_{2} m_{2} \\frac{d^{2}}{d t^{2}} \\phi{\\left(t \\right)} = - d_{1} k \\phi{\\left(t \\right)} - d_{2} k - \\ell_{1} k \\phi{\\left(t \\right)} + \\ell_{1} k \\theta{\\left(t \\right)} - g m_{2} \\phi{\\left(t \\right)}$"], "text/plain": ["Eq(ell_2*m_2*Derivative(phi(t), (t, 2)), -d_1*k*phi(t) - d_2*k - ell_1*k*phi(t) + ell_1*k*theta(t) - g*m_2*phi(t))"]}, "execution_count": 273, "metadata": {}, "output_type": "execute_result"}], "source": ["bruh2 = EOM22.simplify().subs(th**3, 0).subs(th**2, 0).subs(phi**3, 0).subs(phi**2,0).expand()\n", "Eq(bruh2.lhs/L2, bruh2.rhs/L2).simplify()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a,b,c,d,e,f,g1,h, om= symbols('a b c d e f g_1 h omega')\n", "n = symbols('Lambda')\n", "sol1 =solve(Determinant(Matrix([(b/a , c/a), (f/e, g1/e)]) + Matrix([(om**2, 0), (0, om**2)])).doit().subs(om**2, -n).subs(om**4, n**2), n)[0]\n", "Sol1 = sol1.subs(a,L1*m1).subs(b,d1*k - L2*k- g*m1).subs(c, L2*k).subs(e, L2*m2).subs(f, L1*k).subs(g1, -d1*k-L1*k-g*m2).expand()\n", "sol2 =solve(Determinant(Matrix([(b/a , c/a), (f/e, g1/e)]) + Matrix([(om**2, 0), (0, om**2)])).doit().subs(om**2, -n).subs(om**4, n**2), n)[1]\n", "Sol2 = sol2.subs(a,L1*m1).subs(b,d1*k - L2*k- g*m1).subs(c, L2*k).subs(e, L2*m2).subs(f, L1*k).subs(g1, -d1*k-L1*k-g*m2).expand()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["om1 = sqrt(-Sol1.subs(L1,1).subs(L2,1).subs(d1,1).subs(g,9.81).subs(m1,1).subs(m2,1).subs(k,1)).evalf()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["om2 = sqrt(-Sol2.subs(L1,1).subs(L2,1).subs(d1,1).subs(g,9.81).subs(m1,1).subs(m2,1).subs(k,1)).evalf()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{d_{1} k}{2 \\ell_{2} m_{2}} + \\frac{d_{1} k}{2 \\ell_{1} m_{1}} - \\frac{\\ell_{1} k}{2 \\ell_{2} m_{2}} - \\frac{g}{2 \\ell_{2}} - \\frac{\\ell_{2} k}{2 \\ell_{1} m_{1}} - \\frac{g}{2 \\ell_{1}} - \\frac{\\sqrt{d_{1}^{2} \\ell_{1}^{2} k^{2} m_{1}^{2} + 2 d_{1}^{2} \\ell_{1} \\ell_{2} k^{2} m_{1} m_{2} + d_{1}^{2} \\ell_{2}^{2} k^{2} m_{2}^{2} + 2 d_{1} \\ell_{1}^{3} k^{2} m_{1}^{2} + 2 d_{1} \\ell_{1}^{2} \\ell_{2} k^{2} m_{1} m_{2} + 2 d_{1} \\ell_{1}^{2} g k m_{1}^{2} m_{2} - 2 d_{1} \\ell_{1} \\ell_{2}^{2} k^{2} m_{1} m_{2} - 2 d_{1} \\ell_{1} \\ell_{2} g k m_{1}^{2} m_{2} + 2 d_{1} \\ell_{1} \\ell_{2} g k m_{1} m_{2}^{2} - 2 d_{1} \\ell_{2}^{3} k^{2} m_{2}^{2} - 2 d_{1} \\ell_{2}^{2} g k m_{1} m_{2}^{2} + \\ell_{1}^{4} k^{2} m_{1}^{2} + 2 \\ell_{1}^{3} g k m_{1}^{2} m_{2} + 2 \\ell_{1}^{2} \\ell_{2}^{2} k^{2} m_{1} m_{2} - 2 \\ell_{1}^{2} \\ell_{2} g k m_{1}^{2} m_{2} + \\ell_{1}^{2} g^{2} m_{1}^{2} m_{2}^{2} - 2 \\ell_{1} \\ell_{2}^{2} g k m_{1} m_{2}^{2} - 2 \\ell_{1} \\ell_{2} g^{2} m_{1}^{2} m_{2}^{2} + \\ell_{2}^{4} k^{2} m_{2}^{2} + 2 \\ell_{2}^{3} g k m_{1} m_{2}^{2} + \\ell_{2}^{2} g^{2} m_{1}^{2} m_{2}^{2}}}{2 \\ell_{1} \\ell_{2} m_{1} m_{2}}$"], "text/plain": ["-d_1*k/(2*ell_2*m_2) + d_1*k/(2*ell_1*m_1) - ell_1*k/(2*ell_2*m_2) - g/(2*ell_2) - ell_2*k/(2*ell_1*m_1) - g/(2*ell_1) - sqrt(d_1**2*ell_1**2*k**2*m_1**2 + 2*d_1**2*ell_1*ell_2*k**2*m_1*m_2 + d_1**2*ell_2**2*k**2*m_2**2 + 2*d_1*ell_1**3*k**2*m_1**2 + 2*d_1*ell_1**2*ell_2*k**2*m_1*m_2 + 2*d_1*ell_1**2*g*k*m_1**2*m_2 - 2*d_1*ell_1*ell_2**2*k**2*m_1*m_2 - 2*d_1*ell_1*ell_2*g*k*m_1**2*m_2 + 2*d_1*ell_1*ell_2*g*k*m_1*m_2**2 - 2*d_1*ell_2**3*k**2*m_2**2 - 2*d_1*ell_2**2*g*k*m_1*m_2**2 + ell_1**4*k**2*m_1**2 + 2*ell_1**3*g*k*m_1**2*m_2 + 2*ell_1**2*ell_2**2*k**2*m_1*m_2 - 2*ell_1**2*ell_2*g*k*m_1**2*m_2 + ell_1**2*g**2*m_1**2*m_2**2 - 2*ell_1*ell_2**2*g*k*m_1*m_2**2 - 2*ell_1*ell_2*g**2*m_1**2*m_2**2 + ell_2**4*k**2*m_2**2 + 2*ell_2**3*g*k*m_1*m_2**2 + ell_2**2*g**2*m_1**2*m_2**2)/(2*ell_1*ell_2*m_1*m_2)"]}, "execution_count": 277, "metadata": {}, "output_type": "execute_result"}], "source": ["Sol1.expand()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["J = symbols('ell',positive=True, real=True)\n", "M = symbols('M',positive=True, real=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{g}{\\ell} - \\frac{2 k}{M}$"], "text/plain": ["-g/ell - 2*k/M"]}, "execution_count": 279, "metadata": {}, "output_type": "execute_result"}], "source": ["Sol1.simplify().subs(L1,J).subs(L2,J).subs(m1,M).subs(m2,M).expand().subs(d1,0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{g}{\\ell}$"], "text/plain": ["-g/ell"]}, "execution_count": 280, "metadata": {}, "output_type": "execute_result"}], "source": ["Sol2.simplify().subs(L1,J).subs(L2,J).subs(m1,M).subs(m2,M).expand().subs(d1,0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 3.49631428255143$"], "text/plain": ["3.49631428255143"]}, "execution_count": 281, "metadata": {}, "output_type": "execute_result"}], "source": ["om1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gg1 = lambdify((k,m1,m2,d1,L1,L2,g), Sol1)\n", "gg2 = lambdify((k,m1,m2,d1,L1,L2,g), Sol2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["(3.49631428255143, 3.06525471007336)"]}, "execution_count": 373, "metadata": {}, "output_type": "execute_result"}], "source": ["kk = 1\n", "mm1 = 1\n", "mm2 = 1\n", "dd1 = 1\n", "LL1 = 1\n", "LL2 = 1\n", "ggg = 9.81\n", "\n", "sqrt(-gg1(kk,mm1,mm2,dd1,LL1,LL2,ggg)), sqrt(-gg2(kk,mm1,mm2,dd1,LL1,LL2,ggg))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{g}{\\ell_{1}} + \\frac{k \\left(d_{1} - \\ell_{2}\\right)}{\\ell_{1} m_{1}}$"], "text/plain": ["-g/ell_1 + k*(d_1 - ell_2)/(ell_1*m_1)"]}, "execution_count": 362, "metadata": {}, "output_type": "execute_result"}], "source": ["aa = k/(m1*L1)*(d1-L2) - g/L1\n", "aa"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bb = k*L2/(m1*L1)\n", "dd = -(k/(m2*L2)*(d1+L1)+g/L2)\n", "cc = k*L1/(m2*L2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\sqrt{2} \\sqrt{2 M^{2} g + 4 M \\ell k}}{2 M \\sqrt{\\ell}}$"], "text/plain": ["sqrt(2)*sqrt(2*M**2*g + 4*M*ell*k)/(2*M*sqrt(ell))"]}, "execution_count": 380, "metadata": {}, "output_type": "execute_result"}], "source": ["((sqrt(-(Rational(1,2)*((aa+dd)-sqrt((aa+dd)**2 - 4*(aa*dd - bb*cc))))))).subs(d1,0).subs(L1,J).subs(L2,J).simplify().subs(m1,M).subs(m2,M)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 3.49631428255143$"], "text/plain": ["3.49631428255143"]}, "execution_count": 395, "metadata": {}, "output_type": "execute_result"}], "source": ["sqrt((-(Rational(1,2)*((aa+dd)-sqrt((aa+dd)**2 - 4*(aa*dd - bb*cc)))))).subs(g,9.81).subs(L1,1).subs(L2,1).subs(m1,1).subs(m2,1).subs(k,1).subs(d1,1).evalf()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\frac{\\sqrt{2} \\sqrt{\\ell_{1} g m_{1} m_{2} + \\ell_{1} k m_{1} \\left(d_{1} + \\ell_{1}\\right) + \\ell_{2} g m_{1} m_{2} - \\ell_{2} k m_{2} \\left(d_{1} - \\ell_{2}\\right) + \\sqrt{4 \\ell_{1}^{2} \\ell_{2}^{2} k^{2} m_{1} m_{2} - 4 \\ell_{1} \\ell_{2} m_{1} m_{2} \\left(g m_{1} - k \\left(d_{1} - \\ell_{2}\\right)\\right) \\left(g m_{2} + k \\left(d_{1} + \\ell_{1}\\right)\\right) + \\left(\\ell_{1} g m_{1} m_{2} + \\ell_{1} k m_{1} \\left(d_{1} + \\ell_{1}\\right) + \\ell_{2} g m_{1} m_{2} - \\ell_{2} k m_{2} \\left(d_{1} - \\ell_{2}\\right)\\right)^{2}}}}{2 \\sqrt{\\ell_{1}} \\sqrt{\\ell_{2}} \\sqrt{m_{1}} \\sqrt{m_{2}}}\n"]}], "source": ["print_latex(sqrt((-(Rational(1,2)*((aa+dd)-sqrt((aa+dd)**2 - 4*(aa*dd - bb*cc)))))).simplify())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 3.49631428255143$"], "text/plain": ["3.49631428255143"]}, "execution_count": 383, "metadata": {}, "output_type": "execute_result"}], "source": ["sqrt(-gg1(1,1,1,1,1,1,9.81))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 3.49631428255143$"], "text/plain": ["3.49631428255143"]}, "execution_count": 385, "metadata": {}, "output_type": "execute_result"}], "source": ["sqrt(-solve(Eq(Determinant(Matrix([(aa - n,bb),(cc,dd - n)])),0).doit(), n)[0].simplify().subs(d1,1).subs(L1,J).subs(L2,J).subs(m1,M).subs(m2,M).subs(k,1).subs(g,9.81).subs(J,1).subs(M,1)).evalf()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{k^{2}}{M^{2}}$"], "text/plain": ["k**2/M**2"]}, "execution_count": 406, "metadata": {}, "output_type": "execute_result"}], "source": ["((bb*cc).subs(L1,J).subs(L2,J).subs(m1,M).subs(m2,M)).expand()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\sqrt{- \\frac{\\sqrt{\\frac{4 d_{1}^{2} k^{2}}{M^{2} \\ell^{2}} + \\frac{4 k^{2}}{M^{2}}}}{2} + \\frac{g}{\\ell} + \\frac{k}{M}}$"], "text/plain": ["sqrt(-sqrt(4*d_1**2*k**2/(M**2*ell**2) + 4*k**2/M**2)/2 + g/ell + k/M)"]}, "execution_count": 420, "metadata": {}, "output_type": "execute_result"}], "source": ["((sqrt(-Rational(1,2)*((aa+dd)+sqrt((aa+dd)**2 - 4*(aa*dd-bb*cc))))).subs(L1,J).subs(L2,J).subs(m1,M).subs(m2,M)).expand()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q, m, om,p, ga, t, alpha, beta= symbols('q m omega p gamma t alpha beta', positive=True, real=True)\n", "q,p = symbols('q p', real=True)\n", "S = Function('S')(q,t, alpha, beta)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["H = diff(S,q)**2/(2*m) + <PERSON><PERSON>(1,2)*m*om*q**2 + ga/2*q*diff(S,q) - alpha\n", "Sq1 = solve(H,diff(S,q))[0]\n", "Sq2 = solve(H,diff(S,q))[1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\alpha t + \\int \\left(- \\frac{\\gamma m q}{2} + \\frac{\\sqrt{m \\left(8 \\alpha + \\gamma^{2} m q^{2} - 4 m \\omega q^{2}\\right)}}{2}\\right)\\, dq$"], "text/plain": ["-alpha*t + Integral(-gamma*m*q/2 + sqrt(m*(8*alpha + gamma**2*m*q**2 - 4*m*omega*q**2))/2, q)"]}, "execution_count": 461, "metadata": {}, "output_type": "execute_result"}], "source": ["S1 = Integral(Sq1,q) - alpha*t\n", "S2 = Integral(Sq2,q) - alpha*t\n", "S1\n", "S2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - t - 2 \\int \\frac{\\sqrt{8 \\alpha m + \\gamma^{2} m^{2} q^{2} - 4 m^{2} \\omega q^{2}}}{8 \\alpha + \\gamma^{2} m q^{2} - 4 m \\omega q^{2}}\\, dq$"], "text/plain": ["-t - 2*Integral(sqrt(8*alpha*m + gamma**2*m**2*q**2 - 4*m**2*omega*q**2)/(8*alpha + gamma**2*m*q**2 - 4*m*omega*q**2), q)"]}, "execution_count": 463, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{q \\sqrt{b q^{2} + 1}}{2} + \\frac{\\operatorname{asinh}{\\left(\\sqrt{b} q \\right)}}{2 \\sqrt{b}}$"], "text/plain": ["q*sqrt(b*q**2 + 1)/2 + asinh(sqrt(b)*q)/(2*sqrt(b))"]}, "execution_count": 465, "metadata": {}, "output_type": "execute_result"}], "source": ["integrate(sqrt(1+q**2*b), q)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\sqrt{2} \\sqrt{\\alpha m} \\left(\\frac{q \\sqrt{b q^{2} + 1}}{2} + \\frac{\\operatorname{asinh}{\\left(\\sqrt{b} q \\right)}}{2 \\sqrt{b}}\\right)}{2 \\alpha}$"], "text/plain": ["sqrt(2)*sqrt(alpha*m)*(q*sqrt(b*q**2 + 1)/2 + asinh(sqrt(b)*q)/(2*sqrt(b)))/(2*alpha)"]}, "execution_count": 475, "metadata": {}, "output_type": "execute_result"}], "source": ["diff(integrate(-ga*m*q/2 + sqrt(8*alpha*m)/2*sqrt(1+q**2*b), q) - t - beta,alpha)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HH = p**2/(2*m) + k/2*q**2 + ga/2*q*p + diff(S,t)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\partial}{\\partial q} S{\\left(q,t,\\alpha,\\beta \\right)} = - \\frac{\\gamma m q}{2} - \\frac{\\sqrt{m} \\sqrt{8 \\alpha + \\gamma^{2} m q^{2} - 4 k q^{2}}}{2}$"], "text/plain": ["Eq(Derivative(S(q, t, alpha, beta), q), -gamma*m*q/2 - sqrt(m)*sqrt(8*alpha + gamma**2*m*q**2 - 4*k*q**2)/2)"]}, "execution_count": 519, "metadata": {}, "output_type": "execute_result"}], "source": ["Eq(diff(S,q),solve(HH.subs(p,diff(S,q)), diff(S,q))[0].subs(diff(S,t), -alpha))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WW = Integral(solve(HH.subs(p,diff(S,q)), diff(S,q))[0].subs(diff(S,t), -alpha).expand(),q)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{a q^{2}}{2} + b \\left(\\begin{cases} \\frac{q \\sqrt{c q^{2} - 1}}{2} - \\frac{q \\operatorname{acosh}{\\left(\\sqrt{c} \\left|{q}\\right| \\right)}}{2 \\sqrt{c} \\left|{q}\\right|} + \\frac{i \\pi q}{4 \\sqrt{c} \\left|{q}\\right|} & \\text{for}\\: q^{2} \\left|{c}\\right| > 1 \\\\- \\frac{i c q^{3}}{2 \\sqrt{- c q^{2} + 1}} + \\frac{i q}{2 \\sqrt{- c q^{2} + 1}} + \\frac{i q \\operatorname{asin}{\\left(\\sqrt{c} \\left|{q}\\right| \\right)}}{2 \\sqrt{c} \\left|{q}\\right|} & \\text{otherwise} \\end{cases}\\right)$"], "text/plain": ["a*q**2/2 + b*Piecewise((q*sqrt(c*q**2 - 1)/2 - q*acosh(sqrt(c)*Abs(q))/(2*sqrt(c)*Abs(q)) + I*pi*q/(4*sqrt(c)*Abs(q)), q**2*Abs(c) > 1), (-I*c*q**3/(2*sqrt(-c*q**2 + 1)) + I*q/(2*sqrt(-c*q**2 + 1)) + I*q*asin(sqrt(c)*Abs(q))/(2*sqrt(c)*Abs(q)), True))"]}, "execution_count": 516, "metadata": {}, "output_type": "execute_result"}], "source": ["integrate(a*q + b*sqrt(c*q**2-1),q)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ccc = -(4*k-ga**2*m)/(8*alpha)\n", "bbb = -sqrt(m*8*alpha)/2\n", "aaa = -ga*m/2\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{- \\gamma^{2} m + 4 k}{8 \\alpha}$"], "text/plain": ["(-gamma**2*m + 4*k)/(8*alpha)"]}, "execution_count": 515, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["S1 = integrate(a*q + b*sqrt(1-c*q**2), q)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\sqrt{2} \\sqrt{\\alpha} \\sqrt{m} \\left(\\begin{cases} - \\frac{\\sqrt{2} i \\sqrt{\\alpha} q \\operatorname{acosh}{\\left(\\frac{\\sqrt{2} \\sqrt{\\gamma^{2} m - 4 k} \\left|{q}\\right|}{4 \\sqrt{\\alpha}} \\right)}}{\\sqrt{\\gamma^{2} m - 4 k} \\left|{q}\\right|} + \\frac{\\sqrt{2} \\pi \\sqrt{\\alpha} q}{2 \\sqrt{\\gamma^{2} m - 4 k} \\left|{q}\\right|} + \\frac{i q \\sqrt{-1 + \\frac{q^{2} \\left(\\gamma^{2} m - 4 k\\right)}{8 \\alpha}}}{2} & \\text{for}\\: \\frac{q^{2} \\left|{\\gamma^{2} m - 4 k}\\right|}{\\alpha} > 8 \\\\\\frac{\\sqrt{2} \\sqrt{\\alpha} q \\operatorname{asin}{\\left(\\frac{\\sqrt{2} \\sqrt{\\gamma^{2} m - 4 k} \\left|{q}\\right|}{4 \\sqrt{\\alpha}} \\right)}}{\\sqrt{\\gamma^{2} m - 4 k} \\left|{q}\\right|} + \\frac{q}{2 \\sqrt{1 - \\frac{q^{2} \\left(\\gamma^{2} m - 4 k\\right)}{8 \\alpha}}} - \\frac{q^{3} \\left(\\gamma^{2} m - 4 k\\right)}{16 \\alpha \\sqrt{1 - \\frac{q^{2} \\left(\\gamma^{2} m - 4 k\\right)}{8 \\alpha}}} & \\text{otherwise} \\end{cases}\\right) - \\frac{\\gamma m q^{2}}{4}$"], "text/plain": ["-sqrt(2)*sqrt(alpha)*sqrt(m)*Piecewise((-sqrt(2)*I*sqrt(alpha)*q*acosh(sqrt(2)*sqrt(gamma**2*m - 4*k)*Abs(q)/(4*sqrt(alpha)))/(sqrt(gamma**2*m - 4*k)*Abs(q)) + sqrt(2)*pi*sqrt(alpha)*q/(2*sqrt(gamma**2*m - 4*k)*Abs(q)) + I*q*sqrt(-1 + q**2*(gamma**2*m - 4*k)/(8*alpha))/2, q**2*Abs(gamma**2*m - 4*k)/alpha > 8), (sqrt(2)*sqrt(alpha)*q*asin(sqrt(2)*sqrt(gamma**2*m - 4*k)*Abs(q)/(4*sqrt(alpha)))/(sqrt(gamma**2*m - 4*k)*Abs(q)) + q/(2*sqrt(1 - q**2*(gamma**2*m - 4*k)/(8*alpha))) - q**3*(gamma**2*m - 4*k)/(16*alpha*sqrt(1 - q**2*(gamma**2*m - 4*k)/(8*alpha))), True)) - gamma*m*q**2/4"]}, "execution_count": 535, "metadata": {}, "output_type": "execute_result"}], "source": ["S1.subs(a,aaa).subs(b,bbb).subs(c,ccc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - t + \\int \\left(- \\frac{2 \\sqrt{m}}{\\sqrt{8 \\alpha + \\gamma^{2} m q^{2} - 4 k q^{2}}}\\right)\\, dq$"], "text/plain": ["-t + Integral(-2*sqrt(m)/sqrt(8*alpha + gamma**2*m*q**2 - 4*k*q**2), q)"]}, "execution_count": 540, "metadata": {}, "output_type": "execute_result"}], "source": ["diff(WW,alpha) - t"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{\\sqrt{m} \\left(\\begin{cases} - \\frac{i q \\operatorname{acosh}{\\left(\\sqrt{b} \\left|{q}\\right| \\right)}}{\\sqrt{b} \\left|{q}\\right|} + \\frac{\\pi q}{2 \\sqrt{b} \\left|{q}\\right|} & \\text{for}\\: q^{2} \\left|{b}\\right| > 1 \\\\\\frac{q \\operatorname{asin}{\\left(\\sqrt{b} \\left|{q}\\right| \\right)}}{\\sqrt{b} \\left|{q}\\right|} & \\text{otherwise} \\end{cases}\\right)}{2 \\sqrt{\\alpha}}$"], "text/plain": ["-sqrt(m)*Piecewise((-I*q*acosh(sqrt(b)*Abs(q))/(sqrt(b)*Abs(q)) + pi*q/(2*sqrt(b)*Abs(q)), q**2*Abs(b) > 1), (q*asin(sqrt(b)*Abs(q))/(sqrt(b)*Abs(q)), True))/(2*sqrt(alpha))"]}, "execution_count": 545, "metadata": {}, "output_type": "execute_result"}], "source": ["-sqrt(2*m/(8*alpha))*integrate(1/sqrt(1-b*q**2),q)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{2 \\sqrt{2} \\sqrt{\\alpha} \\sin{\\left(\\frac{\\sqrt{2} \\left(\\beta + t\\right) \\sqrt{- \\gamma^{2} m + 4 k}}{2 \\sqrt{m}} \\right)}}{\\sqrt{- \\gamma^{2} m + 4 k}}$"], "text/plain": ["-2*sqrt(2)*sqrt(alpha)*sin(sqrt(2)*(beta + t)*sqrt(-gamma**2*m + 4*k)/(2*sqrt(m)))/sqrt(-gamma**2*m + 4*k)"]}, "execution_count": 551, "metadata": {}, "output_type": "execute_result"}], "source": ["solve(((-sqrt(m/alpha)/2*(q*asin(sqrt(b)*q)/(sqrt(b)*q))).subs(b, (4*k-ga**2*m)/(8*alpha)) -t - beta),q)[0]"]}, {"cell_type": "code", "execution_count": 635, "metadata": {}, "outputs": [], "source": ["ga,m,q,p,om,alpha, t = symbols('gamma m q p omega alpha t', real=True)\n", "q,p = symbols('q p', real=True)\n", "S = Function('S')(q,alpha,t)\n", "W = Function('W')(q,alpha)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\alpha t + W{\\left(q,\\alpha \\right)}$"], "text/plain": ["-alpha*t + W(q, alpha)"]}, "execution_count": 563, "metadata": {}, "output_type": "execute_result"}], "source": ["SS = W - alpha*t\n", "SS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["H = Rational(1,2)*diff(SS,q)**2/m + Rat<PERSON>(1,2)*m*om**2*q**2 + Rational(1,2)*ga*q*diff(SS,q) + diff(SS,t)\n", "pp = diff(Integral((solve(H,diff(W,q))[1]).simplify(),q) - alpha*t, q)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WW = -ga*m*q/2 + sqrt(2*alpha*m)*sqrt(1-q**2*((4*m*om**2 - ga**2*m)/(8*alpha)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\sqrt{2} \\sqrt{\\alpha m} \\sqrt{1 - \\frac{q^{2} \\left(- \\gamma^{2} m + 4 m \\omega^{2}\\right)}{8 \\alpha}}}{2 \\alpha} + \\frac{\\sqrt{2} q^{2} \\sqrt{\\alpha m} \\left(- \\gamma^{2} m + 4 m \\omega^{2}\\right)}{16 \\alpha^{2} \\sqrt{1 - \\frac{q^{2} \\left(- \\gamma^{2} m + 4 m \\omega^{2}\\right)}{8 \\alpha}}}$"], "text/plain": ["sqrt(2)*sqrt(alpha*m)*sqrt(1 - q**2*(-gamma**2*m + 4*m*omega**2)/(8*alpha))/(2*alpha) + sqrt(2)*q**2*sqrt(alpha*m)*(-gamma**2*m + 4*m*omega**2)/(16*alpha**2*sqrt(1 - q**2*(-gamma**2*m + 4*m*omega**2)/(8*alpha)))"]}, "execution_count": 579, "metadata": {}, "output_type": "execute_result"}], "source": ["diff(WW,alpha)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\begin{cases} - \\frac{i b q \\operatorname{acosh}{\\left(\\frac{\\left|{q}\\right|}{b} \\right)}}{\\left|{q}\\right|} + \\frac{\\pi b q}{2 \\left|{q}\\right|} & \\text{for}\\: \\frac{q^{2}}{\\left|{b^{2}}\\right|} > 1 \\\\\\frac{b q \\operatorname{asin}{\\left(\\frac{\\left|{q}\\right|}{b} \\right)}}{\\left|{q}\\right|} & \\text{otherwise} \\end{cases}$"], "text/plain": ["Piecewise((-I*b*q*acosh(Abs(q)/b)/Abs(q) + pi*b*q/(2*Abs(q)), q**2/Abs(b**2) > 1), (b*q*asin(Abs(q)/b)/Abs(q), True))"]}, "execution_count": 586, "metadata": {}, "output_type": "execute_result"}], "source": ["integrate(1/sqrt(1-q**2/b**2),q)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\sqrt{2} \\left(- \\gamma m \\sqrt{- \\frac{\\alpha}{m \\left(\\gamma^{2} - 4 \\omega^{2}\\right)}} \\sin{\\left(\\frac{\\left(\\beta + t\\right) \\sqrt{- \\gamma^{2} + 4 \\omega^{2}}}{2} \\right)} + \\sqrt{\\alpha m \\cos^{2}{\\left(\\frac{\\left(\\beta + t\\right) \\sqrt{- \\gamma^{2} + 4 \\omega^{2}}}{2} \\right)}}\\right)$"], "text/plain": ["sqrt(2)*(-gamma*m*sqrt(-alpha/(m*(gamma**2 - 4*omega**2)))*sin((beta + t)*sqrt(-gamma**2 + 4*omega**2)/2) + sqrt(alpha*m*cos((beta + t)*sqrt(-gamma**2 + 4*omega**2)/2)**2))"]}, "execution_count": 600, "metadata": {}, "output_type": "execute_result"}], "source": ["(pp.subs(q, sqrt(2*alpha/(m*om**2 - ga**2*m/4))*sin(sqrt(om**2 - ga**2/4)*(t+beta)))).simplify()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{\\gamma m q}{2} + \\frac{\\sqrt{m \\left(8 \\alpha + \\gamma^{2} m q^{2} - 4 m \\omega^{2} q^{2}\\right)}}{2}$"], "text/plain": ["-gamma*m*q/2 + sqrt(m*(8*alpha + gamma**2*m*q**2 - 4*m*omega**2*q**2))/2"]}, "execution_count": 598, "metadata": {}, "output_type": "execute_result"}], "source": ["pp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qq = sqrt(2*alpha/(m*om**2 - ga**2*m/4))*sin(sqrt(om**2 - ga**2/4)*(t+beta))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\sqrt{2} \\sqrt{\\frac{\\alpha}{- \\frac{\\gamma^{2} m}{4} + m \\omega^{2}}} \\sin{\\left(\\left(\\beta + t\\right) \\sqrt{- \\frac{\\gamma^{2}}{4} + \\omega^{2}} \\right)}$"], "text/plain": ["sqrt(2)*sqrt(alpha/(-gamma**2*m/4 + m*omega**2))*sin((beta + t)*sqrt(-gamma**2/4 + omega**2))"]}, "execution_count": 603, "metadata": {}, "output_type": "execute_result"}], "source": ["qq"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["q0,p0 = symbols('q_0 p_0',real=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[610], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43msolve\u001b[49m\u001b[43m(\u001b[49m\u001b[43mEq\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msubs\u001b[49m\u001b[43m(\u001b[49m\u001b[43mq\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msqrt\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43malpha\u001b[49m\u001b[38;5;241;43m/\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mm\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mom\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mga\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mm\u001b[49m\u001b[38;5;241;43m/\u001b[39;49m\u001b[38;5;241;43m4\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43msin\u001b[49m\u001b[43m(\u001b[49m\u001b[43msqrt\u001b[49m\u001b[43m(\u001b[49m\u001b[43mom\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mga\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[38;5;241;43m/\u001b[39;49m\u001b[38;5;241;43m4\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mt\u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43mbeta\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msimplify\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msubs\u001b[49m\u001b[43m(\u001b[49m\u001b[43mt\u001b[49m\u001b[43m,\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43mp0\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbeta\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/solvers/solvers.py:1145\u001b[0m, in \u001b[0;36msolve\u001b[0;34m(f, *symbols, **flags)\u001b[0m\n\u001b[1;32m   1143\u001b[0m         solution \u001b[38;5;241m=\u001b[39m _solve_undetermined(f[\u001b[38;5;241m0\u001b[39m], symbols, flags)\n\u001b[1;32m   1144\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m solution:\n\u001b[0;32m-> 1145\u001b[0m         solution \u001b[38;5;241m=\u001b[39m \u001b[43m_solve\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43msymbols\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mflags\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1146\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1147\u001b[0m     linear, solution \u001b[38;5;241m=\u001b[39m _solve_system(f, symbols, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mflags)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/solvers/solvers.py:1670\u001b[0m, in \u001b[0;36m_solve\u001b[0;34m(f, *symbols, **flags)\u001b[0m\n\u001b[1;32m   1668\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1669\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1670\u001b[0m         rv \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mset\u001b[39m(\u001b[43m_vsolve\u001b[49m\u001b[43m(\u001b[49m\u001b[43meq\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msymbol\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mflags\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m   1671\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mNotImplementedError\u001b[39;00m:\n\u001b[1;32m   1672\u001b[0m         rv \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/solvers/solvers.py:2606\u001b[0m, in \u001b[0;36m_vsolve\u001b[0;34m(e, s, **flags)\u001b[0m\n\u001b[1;32m   2604\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_vsolve\u001b[39m(e, s, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mflags):\n\u001b[1;32m   2605\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"return list of scalar values for the solution of e for symbol s\"\"\"\u001b[39;00m\n\u001b[0;32m-> 2606\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m [i[s] \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[43m_solve\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43ms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mflags\u001b[49m\u001b[43m)\u001b[49m]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/solvers/solvers.py:1715\u001b[0m, in \u001b[0;36m_solve\u001b[0;34m(f, *symbols, **flags)\u001b[0m\n\u001b[1;32m   1709\u001b[0m     result \u001b[38;5;241m=\u001b[39m [r \u001b[38;5;28;01mfor\u001b[39;00m r \u001b[38;5;129;01min\u001b[39;00m result \u001b[38;5;28;01mif\u001b[39;00m\n\u001b[1;32m   1710\u001b[0m               \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28many\u001b[39m(checksol(d, r, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mflags)\n\u001b[1;32m   1711\u001b[0m                       \u001b[38;5;28;01mfor\u001b[39;00m d \u001b[38;5;129;01min\u001b[39;00m dens)]\n\u001b[1;32m   1712\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m check:\n\u001b[1;32m   1713\u001b[0m     \u001b[38;5;66;03m# keep only results if the check is not False\u001b[39;00m\n\u001b[1;32m   1714\u001b[0m     result \u001b[38;5;241m=\u001b[39m [r \u001b[38;5;28;01mfor\u001b[39;00m r \u001b[38;5;129;01min\u001b[39;00m result \u001b[38;5;28;01mif\u001b[39;00m\n\u001b[0;32m-> 1715\u001b[0m               \u001b[43mchecksol\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf_num\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mflags\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m]\n\u001b[1;32m   1716\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/solvers/solvers.py:310\u001b[0m, in \u001b[0;36mchecksol\u001b[0;34m(f, symbol, sol, **flags)\u001b[0m\n\u001b[1;32m    307\u001b[0m         sol[k] \u001b[38;5;241m=\u001b[39m simplify(sol[k])\n\u001b[1;32m    308\u001b[0m \u001b[38;5;66;03m# start over without the failed expanded form, possibly\u001b[39;00m\n\u001b[1;32m    309\u001b[0m \u001b[38;5;66;03m# with a simplified solution\u001b[39;00m\n\u001b[0;32m--> 310\u001b[0m val \u001b[38;5;241m=\u001b[39m \u001b[43msimplify\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msubs\u001b[49m\u001b[43m(\u001b[49m\u001b[43msol\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    311\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m flags\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mforce\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;28;01mTrue\u001b[39;00m):\n\u001b[1;32m    312\u001b[0m     val, reps \u001b[38;5;241m=\u001b[39m posify(val)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/simplify.py:699\u001b[0m, in \u001b[0;36msimplify\u001b[0;34m(expr, ratio, measure, rational, inverse, doit, **kwargs)\u001b[0m\n\u001b[1;32m    696\u001b[0m     expr \u001b[38;5;241m=\u001b[39m besselsimp(expr)\n\u001b[1;32m    698\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mhas(TrigonometricFunction, HyperbolicFunction):\n\u001b[0;32m--> 699\u001b[0m     expr \u001b[38;5;241m=\u001b[39m \u001b[43mtrigsimp\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdeep\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    701\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mhas(log):\n\u001b[1;32m    702\u001b[0m     expr \u001b[38;5;241m=\u001b[39m shorter(expand_log(expr, deep\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m), logcombine(expr))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/trigsimp.py:564\u001b[0m, in \u001b[0;36mtrigsimp\u001b[0;34m(expr, inverse, **opts)\u001b[0m\n\u001b[1;32m    553\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m trigsimp_gro<PERSON><PERSON>(new, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts)\n\u001b[1;32m    555\u001b[0m trigsimpfunc \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    556\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfu\u001b[39m\u001b[38;5;124m'\u001b[39m: (\u001b[38;5;28;01mlambda\u001b[39;00m x: fu(x, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts)),\n\u001b[1;32m    557\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmatching\u001b[39m\u001b[38;5;124m'\u001b[39m: (\u001b[38;5;28;01mlambda\u001b[39;00m x: futrig(x)),\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    561\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mold\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;28;01mlambda\u001b[39;00m x: trigsimp_old(x, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts),\n\u001b[1;32m    562\u001b[0m                }[method]\n\u001b[0;32m--> 564\u001b[0m expr_simplified \u001b[38;5;241m=\u001b[39m \u001b[43mtrigsimpfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    565\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m inverse:\n\u001b[1;32m    566\u001b[0m     expr_simplified \u001b[38;5;241m=\u001b[39m _trigsimp_inverse(expr_simplified)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/trigsimp.py:557\u001b[0m, in \u001b[0;36mtrigsimp.<locals>.<lambda>\u001b[0;34m(x)\u001b[0m\n\u001b[1;32m    552\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m new\n\u001b[1;32m    553\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m trigsimp_groebner(new, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts)\n\u001b[1;32m    555\u001b[0m trigsimpfunc \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    556\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfu\u001b[39m\u001b[38;5;124m'\u001b[39m: (\u001b[38;5;28;01mlambda\u001b[39;00m x: fu(x, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts)),\n\u001b[0;32m--> 557\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmatching\u001b[39m\u001b[38;5;124m'\u001b[39m: (\u001b[38;5;28;01mlambda\u001b[39;00m x: \u001b[43mfutrig\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m),\n\u001b[1;32m    558\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mgroebner\u001b[39m\u001b[38;5;124m'\u001b[39m: (\u001b[38;5;28;01mlambda\u001b[39;00m x: groebnersimp(x, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts)),\n\u001b[1;32m    559\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcombined\u001b[39m\u001b[38;5;124m'\u001b[39m: (\u001b[38;5;28;01mlambda\u001b[39;00m x: futrig(groebnersimp(x,\n\u001b[1;32m    560\u001b[0m                            polynomial\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m, hints\u001b[38;5;241m=\u001b[39m[\u001b[38;5;241m2\u001b[39m, tan]))),\n\u001b[1;32m    561\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mold\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;28;01mlambda\u001b[39;00m x: trigsimp_old(x, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts),\n\u001b[1;32m    562\u001b[0m                }[method]\n\u001b[1;32m    564\u001b[0m expr_simplified \u001b[38;5;241m=\u001b[39m trigsimpfunc(expr)\n\u001b[1;32m    565\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m inverse:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/trigsimp.py:1158\u001b[0m, in \u001b[0;36mfutrig\u001b[0;34m(e, hyper, **kwargs)\u001b[0m\n\u001b[1;32m   1155\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m e\n\u001b[1;32m   1157\u001b[0m old \u001b[38;5;241m=\u001b[39m e\n\u001b[0;32m-> 1158\u001b[0m e \u001b[38;5;241m=\u001b[39m \u001b[43mbottom_up\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_futrig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1160\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m hyper \u001b[38;5;129;01mand\u001b[39;00m e\u001b[38;5;241m.\u001b[39mhas(HyperbolicFunction):\n\u001b[1;32m   1161\u001b[0m     e, f \u001b[38;5;241m=\u001b[39m hyper_as_trig(e)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/traversal.py:236\u001b[0m, in \u001b[0;36mbottom_up\u001b[0;34m(rv, F, atoms, nonbasic)\u001b[0m\n\u001b[1;32m    234\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    235\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m args:\n\u001b[0;32m--> 236\u001b[0m         args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtuple\u001b[39m([\u001b[43mbottom_up\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mF\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43matoms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnonbasic\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m args])\n\u001b[1;32m    237\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;241m!=\u001b[39m rv\u001b[38;5;241m.\u001b[39margs:\n\u001b[1;32m    238\u001b[0m             rv \u001b[38;5;241m=\u001b[39m rv\u001b[38;5;241m.\u001b[39mfunc(\u001b[38;5;241m*\u001b[39margs)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/traversal.py:236\u001b[0m, in \u001b[0;36mbottom_up\u001b[0;34m(rv, F, atoms, nonbasic)\u001b[0m\n\u001b[1;32m    234\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    235\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m args:\n\u001b[0;32m--> 236\u001b[0m         args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtuple\u001b[39m([\u001b[43mbottom_up\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mF\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43matoms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnonbasic\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m args])\n\u001b[1;32m    237\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;241m!=\u001b[39m rv\u001b[38;5;241m.\u001b[39margs:\n\u001b[1;32m    238\u001b[0m             rv \u001b[38;5;241m=\u001b[39m rv\u001b[38;5;241m.\u001b[39mfunc(\u001b[38;5;241m*\u001b[39margs)\n", "    \u001b[0;31m[... skipping similar frames: bottom_up at line 236 (2 times)]\u001b[0m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/traversal.py:236\u001b[0m, in \u001b[0;36mbottom_up\u001b[0;34m(rv, F, atoms, nonbasic)\u001b[0m\n\u001b[1;32m    234\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    235\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m args:\n\u001b[0;32m--> 236\u001b[0m         args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtuple\u001b[39m([\u001b[43mbottom_up\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mF\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43matoms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnonbasic\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m args])\n\u001b[1;32m    237\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;241m!=\u001b[39m rv\u001b[38;5;241m.\u001b[39margs:\n\u001b[1;32m    238\u001b[0m             rv \u001b[38;5;241m=\u001b[39m rv\u001b[38;5;241m.\u001b[39mfunc(\u001b[38;5;241m*\u001b[39margs)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/traversal.py:239\u001b[0m, in \u001b[0;36mbottom_up\u001b[0;34m(rv, F, atoms, nonbasic)\u001b[0m\n\u001b[1;32m    237\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;241m!=\u001b[39m rv\u001b[38;5;241m.\u001b[39margs:\n\u001b[1;32m    238\u001b[0m         rv \u001b[38;5;241m=\u001b[39m rv\u001b[38;5;241m.\u001b[39mfunc(\u001b[38;5;241m*\u001b[39margs)\n\u001b[0;32m--> 239\u001b[0m     rv \u001b[38;5;241m=\u001b[39m \u001b[43mF\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrv\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    240\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m atoms:\n\u001b[1;32m    241\u001b[0m     rv \u001b[38;5;241m=\u001b[39m F(rv)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/trigsimp.py:1225\u001b[0m, in \u001b[0;36m_futrig\u001b[0;34m(e)\u001b[0m\n\u001b[1;32m   1186\u001b[0m trigs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mlambda\u001b[39;00m x: x\u001b[38;5;241m.\u001b[39mhas(TrigonometricFunction)\n\u001b[1;32m   1188\u001b[0m tree \u001b[38;5;241m=\u001b[39m [identity,\n\u001b[1;32m   1189\u001b[0m     (\n\u001b[1;32m   1190\u001b[0m     TR3,  \u001b[38;5;66;03m# canonical angles\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1223\u001b[0m         factor_terms, TR12(x), trigs)],  \u001b[38;5;66;03m# expand tan of sum\u001b[39;00m\n\u001b[1;32m   1224\u001b[0m     )]\n\u001b[0;32m-> 1225\u001b[0m e \u001b[38;5;241m=\u001b[39m \u001b[43mgreedy\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtree\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mobjective\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mLops\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1227\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m coeff \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   1228\u001b[0m     e \u001b[38;5;241m=\u001b[39m coeff \u001b[38;5;241m*\u001b[39m e\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/strategies/core.py:150\u001b[0m, in \u001b[0;36mminimize.<locals>.minrule\u001b[0;34m(expr)\u001b[0m\n\u001b[1;32m    149\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mm<PERSON><PERSON><PERSON>\u001b[39m(expr: _S) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m _T:\n\u001b[0;32m--> 150\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mmin\u001b[39m([\u001b[43mrule\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m rule \u001b[38;5;129;01min\u001b[39;00m rules], key\u001b[38;5;241m=\u001b[39mobjective)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/strategies/core.py:64\u001b[0m, in \u001b[0;36mchain.<locals>.chain_rl\u001b[0;34m(expr)\u001b[0m\n\u001b[1;32m     62\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mchain_rl\u001b[39m(expr: _T) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m _T:\n\u001b[1;32m     63\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m rule \u001b[38;5;129;01min\u001b[39;00m rules:\n\u001b[0;32m---> 64\u001b[0m         expr \u001b[38;5;241m=\u001b[39m \u001b[43mrule\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     65\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m expr\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/strategies/core.py:150\u001b[0m, in \u001b[0;36mminimize.<locals>.minrule\u001b[0;34m(expr)\u001b[0m\n\u001b[1;32m    149\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mm<PERSON><PERSON><PERSON>\u001b[39m(expr: _S) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m _T:\n\u001b[0;32m--> 150\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mmin\u001b[39m([\u001b[43mrule\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m rule \u001b[38;5;129;01min\u001b[39;00m rules], key\u001b[38;5;241m=\u001b[39mobjective)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/trigsimp.py:1216\u001b[0m, in \u001b[0;36m_futrig.<locals>.<lambda>\u001b[0;34m(x)\u001b[0m\n\u001b[1;32m   1185\u001b[0m Lops \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mlambda\u001b[39;00m x: (L(x), x\u001b[38;5;241m.\u001b[39mcount_ops(), _nodes(x), \u001b[38;5;28mlen\u001b[39m(x\u001b[38;5;241m.\u001b[39margs), x\u001b[38;5;241m.\u001b[39mis_Add)\n\u001b[1;32m   1186\u001b[0m trigs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mlambda\u001b[39;00m x: x\u001b[38;5;241m.\u001b[39mhas(TrigonometricFunction)\n\u001b[1;32m   1188\u001b[0m tree \u001b[38;5;241m=\u001b[39m [identity,\n\u001b[1;32m   1189\u001b[0m     (\n\u001b[1;32m   1190\u001b[0m     TR3,  \u001b[38;5;66;03m# canonical angles\u001b[39;00m\n\u001b[1;32m   1191\u001b[0m     TR1,  \u001b[38;5;66;03m# sec-csc -> cos-sin\u001b[39;00m\n\u001b[1;32m   1192\u001b[0m     TR12,  \u001b[38;5;66;03m# expand tan of sum\u001b[39;00m\n\u001b[1;32m   1193\u001b[0m     \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(factor, x, trigs),\n\u001b[1;32m   1194\u001b[0m     TR2,  \u001b[38;5;66;03m# tan-cot -> sin-cos\u001b[39;00m\n\u001b[1;32m   1195\u001b[0m     [identity, \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(_mexpand, x, trigs)],\n\u001b[1;32m   1196\u001b[0m     TR2i,  \u001b[38;5;66;03m# sin-cos ratio -> tan\u001b[39;00m\n\u001b[1;32m   1197\u001b[0m     \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(\u001b[38;5;28;01mlambda\u001b[39;00m i: factor(i\u001b[38;5;241m.\u001b[39mnormal()), x, trigs),\n\u001b[1;32m   1198\u001b[0m     TR14,  \u001b[38;5;66;03m# factored identities\u001b[39;00m\n\u001b[1;32m   1199\u001b[0m     TR5,  \u001b[38;5;66;03m# sin-pow -> cos_pow\u001b[39;00m\n\u001b[1;32m   1200\u001b[0m     TR10,  \u001b[38;5;66;03m# sin-cos of sums -> sin-cos prod\u001b[39;00m\n\u001b[1;32m   1201\u001b[0m     TR11, _TR11, TR6, \u001b[38;5;66;03m# reduce double angles and rewrite cos pows\u001b[39;00m\n\u001b[1;32m   1202\u001b[0m     \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(factor, x, trigs),\n\u001b[1;32m   1203\u001b[0m     TR14,  \u001b[38;5;66;03m# factored powers of identities\u001b[39;00m\n\u001b[1;32m   1204\u001b[0m     [identity, \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(_mexpand, x, trigs)],\n\u001b[1;32m   1205\u001b[0m     TR10i,  \u001b[38;5;66;03m# sin-cos products > sin-cos of sums\u001b[39;00m\n\u001b[1;32m   1206\u001b[0m     TRmorrie,\n\u001b[1;32m   1207\u001b[0m     [identity, TR8],  \u001b[38;5;66;03m# sin-cos products -> sin-cos of sums\u001b[39;00m\n\u001b[1;32m   1208\u001b[0m     [identity, \u001b[38;5;28;01mlambda\u001b[39;00m x: TR2i(TR2(x))],  \u001b[38;5;66;03m# tan -> sin-cos -> tan\u001b[39;00m\n\u001b[1;32m   1209\u001b[0m     [\n\u001b[1;32m   1210\u001b[0m         \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(expand_mul, TR5(x), trigs),\n\u001b[1;32m   1211\u001b[0m         \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(\n\u001b[1;32m   1212\u001b[0m             expand_mul, TR15(x), trigs)], \u001b[38;5;66;03m# pos/neg powers of sin\u001b[39;00m\n\u001b[1;32m   1213\u001b[0m     [\n\u001b[1;32m   1214\u001b[0m         \u001b[38;5;28;01mlambda\u001b[39;00m x:  _eapply(expand_mul, TR6(x), trigs),\n\u001b[1;32m   1215\u001b[0m         \u001b[38;5;28;01mlambda\u001b[39;00m x:  _eapply(\n\u001b[0;32m-> 1216\u001b[0m             expand_mul, \u001b[43mTR16\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m, trigs)], \u001b[38;5;66;03m# pos/neg powers of cos\u001b[39;00m\n\u001b[1;32m   1217\u001b[0m     TR111,  \u001b[38;5;66;03m# tan, sin, cos to neg power -> cot, csc, sec\u001b[39;00m\n\u001b[1;32m   1218\u001b[0m     [identity, TR2i],  \u001b[38;5;66;03m# sin-cos ratio to tan\u001b[39;00m\n\u001b[1;32m   1219\u001b[0m     [identity, \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(\n\u001b[1;32m   1220\u001b[0m         expand_mul, TR22(x), trigs)],  \u001b[38;5;66;03m# tan-cot to sec-csc\u001b[39;00m\n\u001b[1;32m   1221\u001b[0m     TR1, TR2, TR2i,\n\u001b[1;32m   1222\u001b[0m     [identity, \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(\n\u001b[1;32m   1223\u001b[0m         factor_terms, TR12(x), trigs)],  \u001b[38;5;66;03m# expand tan of sum\u001b[39;00m\n\u001b[1;32m   1224\u001b[0m     )]\n\u001b[1;32m   1225\u001b[0m e \u001b[38;5;241m=\u001b[39m greedy(tree, objective\u001b[38;5;241m=\u001b[39mLops)(e)\n\u001b[1;32m   1227\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m coeff \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/fu.py:1407\u001b[0m, in \u001b[0;36mTR16\u001b[0;34m(rv, max, pow)\u001b[0m\n\u001b[1;32m   1404\u001b[0m         rv \u001b[38;5;241m=\u001b[39m a\n\u001b[1;32m   1405\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m rv\n\u001b[0;32m-> 1407\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mbottom_up\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/traversal.py:236\u001b[0m, in \u001b[0;36mbottom_up\u001b[0;34m(rv, F, atoms, nonbasic)\u001b[0m\n\u001b[1;32m    234\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    235\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m args:\n\u001b[0;32m--> 236\u001b[0m         args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtuple\u001b[39m([\u001b[43mbottom_up\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mF\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43matoms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnonbasic\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m args])\n\u001b[1;32m    237\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;241m!=\u001b[39m rv\u001b[38;5;241m.\u001b[39margs:\n\u001b[1;32m    238\u001b[0m             rv \u001b[38;5;241m=\u001b[39m rv\u001b[38;5;241m.\u001b[39mfunc(\u001b[38;5;241m*\u001b[39margs)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/traversal.py:236\u001b[0m, in \u001b[0;36mbottom_up\u001b[0;34m(rv, F, atoms, nonbasic)\u001b[0m\n\u001b[1;32m    234\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    235\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m args:\n\u001b[0;32m--> 236\u001b[0m         args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtuple\u001b[39m([\u001b[43mbottom_up\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mF\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43matoms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnonbasic\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m args])\n\u001b[1;32m    237\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;241m!=\u001b[39m rv\u001b[38;5;241m.\u001b[39margs:\n\u001b[1;32m    238\u001b[0m             rv \u001b[38;5;241m=\u001b[39m rv\u001b[38;5;241m.\u001b[39mfunc(\u001b[38;5;241m*\u001b[39margs)\n", "    \u001b[0;31m[... skipping similar frames: bottom_up at line 236 (12 times)]\u001b[0m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/traversal.py:236\u001b[0m, in \u001b[0;36mbottom_up\u001b[0;34m(rv, F, atoms, nonbasic)\u001b[0m\n\u001b[1;32m    234\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    235\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m args:\n\u001b[0;32m--> 236\u001b[0m         args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtuple\u001b[39m([\u001b[43mbottom_up\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mF\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43matoms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnonbasic\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m args])\n\u001b[1;32m    237\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;241m!=\u001b[39m rv\u001b[38;5;241m.\u001b[39margs:\n\u001b[1;32m    238\u001b[0m             rv \u001b[38;5;241m=\u001b[39m rv\u001b[38;5;241m.\u001b[39mfunc(\u001b[38;5;241m*\u001b[39margs)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/traversal.py:233\u001b[0m, in \u001b[0;36mbottom_up\u001b[0;34m(rv, F, atoms, nonbasic)\u001b[0m\n\u001b[1;32m    228\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mbottom_up\u001b[39m(rv, F, atoms\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m, nonbasic\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m):\n\u001b[1;32m    229\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Apply ``F`` to all expressions in an expression tree from the\u001b[39;00m\n\u001b[1;32m    230\u001b[0m \u001b[38;5;124;03m    bottom up. If ``atoms`` is True, apply ``F`` even if there are no args;\u001b[39;00m\n\u001b[1;32m    231\u001b[0m \u001b[38;5;124;03m    if ``nonbasic`` is True, try to apply ``F`` to non-Basic objects.\u001b[39;00m\n\u001b[1;32m    232\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 233\u001b[0m     args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mrv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43margs\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    234\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    235\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m args:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/basic.py:786\u001b[0m, in \u001b[0;36mBasic.args\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    762\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    763\u001b[0m \u001b[38;5;124;03m    The top-level function in an expression.\u001b[39;00m\n\u001b[1;32m    764\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    782\u001b[0m \n\u001b[1;32m    783\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m    784\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m\n\u001b[0;32m--> 786\u001b[0m \u001b[38;5;129m@property\u001b[39m\n\u001b[1;32m    787\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21margs\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mtuple\u001b[39m[Basic, \u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m]:\n\u001b[1;32m    788\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Returns a tuple of arguments of 'self'.\u001b[39;00m\n\u001b[1;32m    789\u001b[0m \n\u001b[1;32m    790\u001b[0m \u001b[38;5;124;03m    Examples\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    814\u001b[0m \u001b[38;5;124;03m    change the interface in the future if needed).\u001b[39;00m\n\u001b[1;32m    815\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m    816\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_args\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["solve(Eq((pp.subs(q, sqrt(2*alpha/(m*om**2 - ga**2*m/4))*sin(sqrt(om**2 - ga**2/4)*(t+beta)))).simplify().subs(t,0),p0), beta)"]}, {"cell_type": "code", "execution_count": 642, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{2 \\operatorname{asin}{\\left(\\frac{q_{0} \\sqrt{\\frac{- \\gamma^{2} + 4 \\omega^{2}}{m q_{0} \\left(\\gamma p_{0} + m \\omega^{2} q_{0}\\right) + p_{0}^{2}}} \\left|{m}\\right|}{2} \\right)}}{\\sqrt{- \\gamma^{2} + 4 \\omega^{2}}}$"], "text/plain": ["2*asin(q_0*sqrt((-gamma**2 + 4*omega**2)/(m*q_0*(gamma*p_0 + m*omega**2*q_0) + p_0**2))*Abs(m)/2)/sqrt(-gamma**2 + 4*omega**2)"]}, "execution_count": 642, "metadata": {}, "output_type": "execute_result"}], "source": ["BB = asin(q0*sqrt((m*om**2 - ga**2*m/4)/(2*alpha)))/sqrt(om**2 - ga**2/4)\n", "BB.subs(alpha, p0**2/(2*m) + Rat<PERSON>(1,2)*m*om**2*q0**2 + ga/2*q0*p0).simplify()"]}, {"cell_type": "code", "execution_count": 647, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\int p\\, dq}{2 \\pi}$"], "text/plain": ["Integral(p, q)/(2*pi)"]}, "execution_count": 647, "metadata": {}, "output_type": "execute_result"}], "source": ["H = p**2/(2*m)\n", "J = 1/(2*pi)*Integral(p,q)\n", "J"]}, {"cell_type": "code", "execution_count": 648, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{p^{2}}{2 \\pi m}$"], "text/plain": ["p**2/(2*pi*m)"]}, "execution_count": 648, "metadata": {}, "output_type": "execute_result"}], "source": ["diff(J,q)*diff(H,p) - diff(J,p)*diff(H,q)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["a,b,c,u,k,t,u0,uf,r = symbols('a b c u k t u_0 u_f r')\n", "tt= integrate(1/sqrt(a*u**2 + b*u + c), (u,u0, uf)).args[0][0]"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\left(- a c + \\frac{\\left(b - \\left(2 \\sqrt{a} \\sqrt{a u_{0}^{2} + b u_{0} + c} + 2 a u_{0} + b\\right) e^{\\sqrt{a} t}\\right)^{2}}{4}\\right) e^{- \\sqrt{a} t}}{a \\left(2 \\sqrt{a} \\sqrt{a u_{0}^{2} + b u_{0} + c} + 2 a u_{0} + b\\right)}$"], "text/plain": ["(-a*c + (b - (2*sqrt(a)*sqrt(a*u_0**2 + b*u_0 + c) + 2*a*u_0 + b)*exp(sqrt(a)*t))**2/4)*exp(-sqrt(a)*t)/(a*(2*sqrt(a)*sqrt(a*u_0**2 + b*u_0 + c) + 2*a*u_0 + b))"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["solve(tt - t,uf)[0]"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{1}{r^{2}}$"], "text/plain": ["-1/r**2"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["uu = 1/r\n", "diff(uu,r)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle p_{\\theta}$"], "text/plain": ["p_theta"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["diff(uu,r)\n", "E, m, k, pt = symbols('E m k p_theta')\n", "pt"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[29], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m WW \u001b[38;5;241m=\u001b[39m Integral(sqrt(\u001b[38;5;241m2\u001b[39m\u001b[38;5;241m*\u001b[39mm\u001b[38;5;241m*\u001b[39m(E\u001b[38;5;241m+\u001b[39mk\u001b[38;5;241m/\u001b[39mr\u001b[38;5;241m+\u001b[39mpt\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m2\u001b[39m\u001b[38;5;241m/\u001b[39m(\u001b[38;5;241m2\u001b[39m\u001b[38;5;241m*\u001b[39mm\u001b[38;5;241m*\u001b[39mr\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m2\u001b[39m))), r)\n\u001b[0;32m----> 2\u001b[0m \u001b[43mdiff\u001b[49m\u001b[43m(\u001b[49m\u001b[43mWW\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpt\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdoit\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/integrals/integrals.py:612\u001b[0m, in \u001b[0;36mIntegral.doit\u001b[0;34m(self, **hints)\u001b[0m\n\u001b[1;32m    610\u001b[0m     antideriv \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    611\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 612\u001b[0m     antideriv \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_eval_integral\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    613\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfunction\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mxab\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43meval_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    614\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m antideriv \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m meijerg \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m    615\u001b[0m         ret \u001b[38;5;241m=\u001b[39m try_meijerg(function, xab)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/integrals/integrals.py:1084\u001b[0m, in \u001b[0;36mIntegral._eval_integral\u001b[0;34m(self, f, x, meijerg, risch, manual, heurisch, conds, final)\u001b[0m\n\u001b[1;32m   1082\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1083\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m conds \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpiecewise\u001b[39m\u001b[38;5;124m'\u001b[39m:\n\u001b[0;32m-> 1084\u001b[0m         h \u001b[38;5;241m=\u001b[39m \u001b[43mheurisch_wrapper\u001b[49m\u001b[43m(\u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mhints\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1085\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1086\u001b[0m         h \u001b[38;5;241m=\u001b[39m heurisch_(g, x, hints\u001b[38;5;241m=\u001b[39m[])\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/integrals/heurisch.py:143\u001b[0m, in \u001b[0;36mheurisch_wrapper\u001b[0;34m(f, x, rewrite, hints, mappings, retries, degree_offset, unnecessary_permutations, _try_heurisch)\u001b[0m\n\u001b[1;32m    140\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m f\u001b[38;5;241m.\u001b[39mhas_free(x):\n\u001b[1;32m    141\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m f\u001b[38;5;241m*\u001b[39mx\n\u001b[0;32m--> 143\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[43mheurisch\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrewrite\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mhints\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmappings\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdegree_offset\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    144\u001b[0m \u001b[43m               \u001b[49m\u001b[43munnecessary_permutations\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_try_heurisch\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    145\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(res, Basic):\n\u001b[1;32m    146\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m res\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/integrals/heurisch.py:754\u001b[0m, in \u001b[0;36mheurisch\u001b[0;34m(f, x, rewrite, hints, mappings, retries, degree_offset, unnecessary_permutations, _try_heurisch)\u001b[0m\n\u001b[1;32m    752\u001b[0m         solution \u001b[38;5;241m=\u001b[39m _integrate()\n\u001b[1;32m    753\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 754\u001b[0m     solution \u001b[38;5;241m=\u001b[39m \u001b[43m_integrate\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    756\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m solution \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    757\u001b[0m     antideriv \u001b[38;5;241m=\u001b[39m solution\u001b[38;5;241m.\u001b[39msubs(rev_mapping)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/integrals/heurisch.py:730\u001b[0m, in \u001b[0;36mheurisch.<locals>._integrate\u001b[0;34m(field)\u001b[0m\n\u001b[1;32m    728\u001b[0m ring \u001b[38;5;241m=\u001b[39m PolyRing(V, coeff_ring)\n\u001b[1;32m    729\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 730\u001b[0m     numer \u001b[38;5;241m=\u001b[39m \u001b[43mring\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_expr\u001b[49m\u001b[43m(\u001b[49m\u001b[43mraw_numer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    731\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m:\n\u001b[1;32m    732\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m PolynomialError\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:397\u001b[0m, in \u001b[0;36mPolyRing.from_expr\u001b[0;34m(self, expr)\u001b[0m\n\u001b[1;32m    394\u001b[0m mapping \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mdict\u001b[39m(\u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mzip\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msymbols, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgens)))\n\u001b[1;32m    396\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 397\u001b[0m     poly \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_rebuild_expr\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmapping\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    398\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m CoercionFailed:\n\u001b[1;32m    399\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mexpected an expression convertible to a polynomial in \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m, got \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m (\u001b[38;5;28mself\u001b[39m, expr))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:391\u001b[0m, in \u001b[0;36mPolyRing._rebuild_expr\u001b[0;34m(self, expr, mapping)\u001b[0m\n\u001b[1;32m    388\u001b[0m         \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[1;32m    389\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mground_new(domain\u001b[38;5;241m.\u001b[39mconvert(expr))\n\u001b[0;32m--> 391\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_rebuild\u001b[49m\u001b[43m(\u001b[49m\u001b[43msympify\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:379\u001b[0m, in \u001b[0;36mPolyRing._rebuild_expr.<locals>._rebuild\u001b[0;34m(expr)\u001b[0m\n\u001b[1;32m    377\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m generator\n\u001b[1;32m    378\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mis_Add:\n\u001b[0;32m--> 379\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m reduce(add, \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mmap\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m_rebuild\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexpr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m    380\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mis_Mul:\n\u001b[1;32m    381\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m reduce(mul, \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mmap\u001b[39m(_rebuild, expr\u001b[38;5;241m.\u001b[39margs)))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:381\u001b[0m, in \u001b[0;36mPolyRing._rebuild_expr.<locals>._rebuild\u001b[0;34m(expr)\u001b[0m\n\u001b[1;32m    379\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m reduce(add, \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mmap\u001b[39m(_rebuild, expr\u001b[38;5;241m.\u001b[39margs)))\n\u001b[1;32m    380\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mis_Mul:\n\u001b[0;32m--> 381\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m reduce(mul, \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mmap\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m_rebuild\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexpr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m    382\u001b[0m \u001b[38;5;28;01<PERSON>se\u001b[39;00m:\n\u001b[1;32m    383\u001b[0m     \u001b[38;5;66;03m# XXX: Use as_base_exp() to handle Pow(x, n) and also exp(n)\u001b[39;00m\n\u001b[1;32m    384\u001b[0m     \u001b[38;5;66;03m# XXX: E can be a generator e.g. sring([exp(2)]) -> ZZ[E]\u001b[39;00m\n\u001b[1;32m    385\u001b[0m     base, exp \u001b[38;5;241m=\u001b[39m expr\u001b[38;5;241m.\u001b[39mas_base_exp()\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:379\u001b[0m, in \u001b[0;36mPolyRing._rebuild_expr.<locals>._rebuild\u001b[0;34m(expr)\u001b[0m\n\u001b[1;32m    377\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m generator\n\u001b[1;32m    378\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mis_Add:\n\u001b[0;32m--> 379\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m reduce(add, \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mmap\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m_rebuild\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexpr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m    380\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mis_Mul:\n\u001b[1;32m    381\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m reduce(mul, \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mmap\u001b[39m(_rebuild, expr\u001b[38;5;241m.\u001b[39margs)))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:381\u001b[0m, in \u001b[0;36mPolyRing._rebuild_expr.<locals>._rebuild\u001b[0;34m(expr)\u001b[0m\n\u001b[1;32m    379\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m reduce(add, \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mmap\u001b[39m(_rebuild, expr\u001b[38;5;241m.\u001b[39margs)))\n\u001b[1;32m    380\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mis_Mul:\n\u001b[0;32m--> 381\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m reduce(mul, \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mmap\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m_rebuild\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexpr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m    382\u001b[0m \u001b[38;5;28;01<PERSON>se\u001b[39;00m:\n\u001b[1;32m    383\u001b[0m     \u001b[38;5;66;03m# XXX: Use as_base_exp() to handle Pow(x, n) and also exp(n)\u001b[39;00m\n\u001b[1;32m    384\u001b[0m     \u001b[38;5;66;03m# XXX: E can be a generator e.g. sring([exp(2)]) -> ZZ[E]\u001b[39;00m\n\u001b[1;32m    385\u001b[0m     base, exp \u001b[38;5;241m=\u001b[39m expr\u001b[38;5;241m.\u001b[39mas_base_exp()\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:379\u001b[0m, in \u001b[0;36mPolyRing._rebuild_expr.<locals>._rebuild\u001b[0;34m(expr)\u001b[0m\n\u001b[1;32m    377\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m generator\n\u001b[1;32m    378\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mis_Add:\n\u001b[0;32m--> 379\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m reduce(add, \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mmap\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m_rebuild\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexpr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[1;32m    380\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mis_Mul:\n\u001b[1;32m    381\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m reduce(mul, \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mmap\u001b[39m(_rebuild, expr\u001b[38;5;241m.\u001b[39margs)))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:381\u001b[0m, in \u001b[0;36mPolyRing._rebuild_expr.<locals>._rebuild\u001b[0;34m(expr)\u001b[0m\n\u001b[1;32m    379\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m reduce(add, \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mmap\u001b[39m(_rebuild, expr\u001b[38;5;241m.\u001b[39margs)))\n\u001b[1;32m    380\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mis_Mul:\n\u001b[0;32m--> 381\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mreduce\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmul\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mmap\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m_rebuild\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexpr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    382\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    383\u001b[0m     \u001b[38;5;66;03m# XXX: Use as_base_exp() to handle Pow(x, n) and also exp(n)\u001b[39;00m\n\u001b[1;32m    384\u001b[0m     \u001b[38;5;66;03m# XXX: E can be a generator e.g. sring([exp(2)]) -> ZZ[E]\u001b[39;00m\n\u001b[1;32m    385\u001b[0m     base, exp \u001b[38;5;241m=\u001b[39m expr\u001b[38;5;241m.\u001b[39mas_base_exp()\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:1118\u001b[0m, in \u001b[0;36mPolyElement.__mul__\u001b[0;34m(p1, p2)\u001b[0m\n\u001b[1;32m   1116\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m exp2, v2 \u001b[38;5;129;01min\u001b[39;00m p2it:\n\u001b[1;32m   1117\u001b[0m         exp \u001b[38;5;241m=\u001b[39m monomial_mul(exp1, exp2)\n\u001b[0;32m-> 1118\u001b[0m         p[exp] \u001b[38;5;241m=\u001b[39m get(exp, zero) \u001b[38;5;241m+\u001b[39m \u001b[43mv1\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mv2\u001b[49m\n\u001b[1;32m   1119\u001b[0m p\u001b[38;5;241m.\u001b[39mstrip_zero()\n\u001b[1;32m   1120\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m p\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:1118\u001b[0m, in \u001b[0;36mPolyElement.__mul__\u001b[0;34m(p1, p2)\u001b[0m\n\u001b[1;32m   1116\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m exp2, v2 \u001b[38;5;129;01min\u001b[39;00m p2it:\n\u001b[1;32m   1117\u001b[0m         exp \u001b[38;5;241m=\u001b[39m monomial_mul(exp1, exp2)\n\u001b[0;32m-> 1118\u001b[0m         p[exp] \u001b[38;5;241m=\u001b[39m get(exp, zero) \u001b[38;5;241m+\u001b[39m \u001b[43mv1\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mv2\u001b[49m\n\u001b[1;32m   1119\u001b[0m p\u001b[38;5;241m.\u001b[39mstrip_zero()\n\u001b[1;32m   1120\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m p\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/domains/expressiondomain.py:105\u001b[0m, in \u001b[0;36mExpressionDomain.Expression.__mul__\u001b[0;34m(f, g)\u001b[0m\n\u001b[1;32m    102\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m f\u001b[38;5;241m.\u001b[39mex\u001b[38;5;241m.\u001b[39mis_Number \u001b[38;5;129;01mand\u001b[39;00m g\u001b[38;5;241m.\u001b[39mex\u001b[38;5;241m.\u001b[39mis_Number:\n\u001b[1;32m    103\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m f\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m(f\u001b[38;5;241m.\u001b[39mex\u001b[38;5;241m*\u001b[39mg\u001b[38;5;241m.\u001b[39mex)\n\u001b[0;32m--> 105\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msimplify\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mex\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mg\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mex\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/domains/expressiondomain.py:50\u001b[0m, in \u001b[0;36mExpressionDomain.Expression.simplify\u001b[0;34m(f, ex)\u001b[0m\n\u001b[1;32m     49\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21msimplify\u001b[39m(f, ex):\n\u001b[0;32m---> 50\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m f\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m(\u001b[43mex\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcancel\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mexpand(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39meflags))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/expr.py:3791\u001b[0m, in \u001b[0;36mExpr.cancel\u001b[0;34m(self, *gens, **args)\u001b[0m\n\u001b[1;32m   3789\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"See the cancel function in sympy.polys\"\"\"\u001b[39;00m\n\u001b[1;32m   3790\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msympy\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpolyto<PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m cancel\n\u001b[0;32m-> 3791\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mcancel\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mgens\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/polytools.py:6840\u001b[0m, in \u001b[0;36mcancel\u001b[0;34m(f, _signsimp, *gens, **args)\u001b[0m\n\u001b[1;32m   6838\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m f\u001b[38;5;241m.\u001b[39mis_Number \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(f, Relational) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(f, Expr):\n\u001b[1;32m   6839\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m f\n\u001b[0;32m-> 6840\u001b[0m     f \u001b[38;5;241m=\u001b[39m \u001b[43mfactor_terms\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mradical\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m   6841\u001b[0m     p, q \u001b[38;5;241m=\u001b[39m f\u001b[38;5;241m.\u001b[39mas_numer_denom()\n\u001b[1;32m   6843\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(f) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m2\u001b[39m:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/exprtools.py:1267\u001b[0m, in \u001b[0;36mfactor_terms\u001b[0;34m(expr, radical, clear, fraction, sign)\u001b[0m\n\u001b[1;32m   1265\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m rv\n\u001b[1;32m   1266\u001b[0m expr \u001b[38;5;241m=\u001b[39m sympify(expr)\n\u001b[0;32m-> 1267\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdo\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/exprtools.py:1257\u001b[0m, in \u001b[0;36mfactor_terms.<locals>.do\u001b[0;34m(expr)\u001b[0m\n\u001b[1;32m   1255\u001b[0m     \u001b[38;5;66;03m# rebuild p not worrying about the order which gcd_terms will fix\u001b[39;00m\n\u001b[1;32m   1256\u001b[0m     p \u001b[38;5;241m=\u001b[39m Add\u001b[38;5;241m.\u001b[39m_from_args(list_args)\n\u001b[0;32m-> 1257\u001b[0m     p \u001b[38;5;241m=\u001b[39m \u001b[43mgcd_terms\u001b[49m\u001b[43m(\u001b[49m\u001b[43mp\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1258\u001b[0m \u001b[43m        \u001b[49m\u001b[43misprimitive\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   1259\u001b[0m \u001b[43m        \u001b[49m\u001b[43mclear\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mclear\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1260\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfraction\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfraction\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mxreplace(special)\n\u001b[1;32m   1261\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m p\u001b[38;5;241m.\u001b[39margs:\n\u001b[1;32m   1262\u001b[0m     p \u001b[38;5;241m=\u001b[39m p\u001b[38;5;241m.\u001b[39mfunc(\n\u001b[1;32m   1263\u001b[0m         \u001b[38;5;241m*\u001b[39m[do(a) \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m p\u001b[38;5;241m.\u001b[39margs])\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/exprtools.py:1068\u001b[0m, in \u001b[0;36mgcd_terms\u001b[0;34m(terms, isprimitive, clear, fraction)\u001b[0m\n\u001b[1;32m   1066\u001b[0m     terms \u001b[38;5;241m=\u001b[39m sympify(terms)\n\u001b[1;32m   1067\u001b[0m terms, reps \u001b[38;5;241m=\u001b[39m mask(terms)\n\u001b[0;32m-> 1068\u001b[0m cont, numer, denom \u001b[38;5;241m=\u001b[39m \u001b[43m_gcd_terms\u001b[49m\u001b[43m(\u001b[49m\u001b[43mterms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43misprimitive\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfraction\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1069\u001b[0m numer \u001b[38;5;241m=\u001b[39m numer\u001b[38;5;241m.\u001b[39mxreplace(reps)\n\u001b[1;32m   1070\u001b[0m coeff, factors \u001b[38;5;241m=\u001b[39m cont\u001b[38;5;241m.\u001b[39mas_coeff_Mul()\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/exprtools.py:934\u001b[0m, in \u001b[0;36m_gcd_terms\u001b[0;34m(terms, isprimitive, fraction)\u001b[0m\n\u001b[1;32m    931\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(terms, Basic) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(terms, Tuple):\n\u001b[1;32m    932\u001b[0m     terms \u001b[38;5;241m=\u001b[39m Add\u001b[38;5;241m.\u001b[39mmake_args(terms)\n\u001b[0;32m--> 934\u001b[0m terms \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mmap\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mTerm\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mterms\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mt\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    936\u001b[0m \u001b[38;5;66;03m# there is some simplification that may happen if we leave this\u001b[39;00m\n\u001b[1;32m    937\u001b[0m \u001b[38;5;66;03m# here rather than duplicate it before the mapping of Term onto\u001b[39;00m\n\u001b[1;32m    938\u001b[0m \u001b[38;5;66;03m# the terms\u001b[39;00m\n\u001b[1;32m    939\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(terms) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/exprtools.py:831\u001b[0m, in \u001b[0;36mTerm.__init__\u001b[0;34m(self, term, numer, denom)\u001b[0m\n\u001b[1;32m    828\u001b[0m         \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    829\u001b[0m             denom[base] \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m-\u001b[39mexp\n\u001b[0;32m--> 831\u001b[0m     numer \u001b[38;5;241m=\u001b[39m \u001b[43mFactors\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnumer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    832\u001b[0m     denom \u001b[38;5;241m=\u001b[39m Factors(denom)\n\u001b[1;32m    833\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/exprtools.py:324\u001b[0m, in \u001b[0;36mFactors.__init__\u001b[0;34m(self, factors)\u001b[0m\n\u001b[1;32m    322\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(factors, Factors):\n\u001b[1;32m    323\u001b[0m     factors \u001b[38;5;241m=\u001b[39m factors\u001b[38;5;241m.\u001b[39mfactors\u001b[38;5;241m.\u001b[39mcopy()\n\u001b[0;32m--> 324\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[43mfactors\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mS\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mOne\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[1;32m    325\u001b[0m     factors \u001b[38;5;241m=\u001b[39m {}\n\u001b[1;32m    326\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m factors \u001b[38;5;129;01mis\u001b[39;00m S\u001b[38;5;241m.\u001b[39mZero \u001b[38;5;129;01mor\u001b[39;00m factors \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/numbers.py:2248\u001b[0m, in \u001b[0;36mInteger.__eq__\u001b[0;34m(self, other)\u001b[0m\n\u001b[1;32m   2246\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(other, Integer):\n\u001b[1;32m   2247\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mp \u001b[38;5;241m==\u001b[39m other\u001b[38;5;241m.\u001b[39mp)\n\u001b[0;32m-> 2248\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mRational\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__eq__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mother\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/numbers.py:1874\u001b[0m, in \u001b[0;36mRational.__eq__\u001b[0;34m(self, other)\u001b[0m\n\u001b[1;32m   1872\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__eq__\u001b[39m(\u001b[38;5;28mself\u001b[39m, other):\n\u001b[1;32m   1873\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1874\u001b[0m         other \u001b[38;5;241m=\u001b[39m \u001b[43m_sympify\u001b[49m\u001b[43m(\u001b[49m\u001b[43mother\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1875\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m SympifyError:\n\u001b[1;32m   1876\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mNotImplemented\u001b[39m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/sympify.py:528\u001b[0m, in \u001b[0;36m_sympify\u001b[0;34m(a)\u001b[0m\n\u001b[1;32m    502\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_sympify\u001b[39m(a):\n\u001b[1;32m    503\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    504\u001b[0m \u001b[38;5;124;03m    Short version of :func:`~.sympify` for internal usage for ``__add__`` and\u001b[39;00m\n\u001b[1;32m    505\u001b[0m \u001b[38;5;124;03m    ``__eq__`` methods where it is ok to allow some things (like Python\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    526\u001b[0m \n\u001b[1;32m    527\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 528\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msympify\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstrict\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/sympify.py:383\u001b[0m, in \u001b[0;36msympify\u001b[0;34m(a, locals, convert_xor, strict, rational, evaluate)\u001b[0m\n\u001b[1;32m    381\u001b[0m         conv \u001b[38;5;241m=\u001b[39m _sympy_converter\u001b[38;5;241m.\u001b[39mget(superclass)\n\u001b[1;32m    382\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m conv \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m--> 383\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mconv\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    385\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28mtype\u001b[39m(\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m):\n\u001b[1;32m    386\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m strict:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/containers.py:332\u001b[0m, in \u001b[0;36m<lambda>\u001b[0;34m(d)\u001b[0m\n\u001b[1;32m    329\u001b[0m     \u001b[38;5;21m__hash__\u001b[39m : Callable[[Basic], Any] \u001b[38;5;241m=\u001b[39m Basic\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__hash__\u001b[39m\n\u001b[1;32m    331\u001b[0m \u001b[38;5;66;03m# this handles dict, defaultdict, OrderedDict\u001b[39;00m\n\u001b[0;32m--> 332\u001b[0m _sympy_converter[\u001b[38;5;28mdict\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mlambda\u001b[39;00m d: \u001b[43mDict\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43md\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mitems\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    334\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01mOrderedSet\u001b[39;00m(MutableSet):\n\u001b[1;32m    335\u001b[0m     \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, iterable\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/containers.py:263\u001b[0m, in \u001b[0;36mDict.__new__\u001b[0;34m(cls, *args)\u001b[0m\n\u001b[1;32m    261\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mPass Dict args as Dict((k1, v1), ...) or Dict(\u001b[39m\u001b[38;5;124m{\u001b[39m\u001b[38;5;124mk1: v1, ...})\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m    262\u001b[0m elements \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mfrozenset\u001b[39m(items)\n\u001b[0;32m--> 263\u001b[0m obj \u001b[38;5;241m=\u001b[39m Basic\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__new__\u001b[39m(\u001b[38;5;28mcls\u001b[39m, \u001b[38;5;241m*\u001b[39mordered(items))\n\u001b[1;32m    264\u001b[0m obj\u001b[38;5;241m.\u001b[39melements \u001b[38;5;241m=\u001b[39m elements\n\u001b[1;32m    265\u001b[0m obj\u001b[38;5;241m.\u001b[39m_dict \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mdict\u001b[39m(items)  \u001b[38;5;66;03m# In case Tuple decides it wants to sympify\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/sorting.py:309\u001b[0m, in \u001b[0;36mordered\u001b[0;34m(seq, keys, default, warn)\u001b[0m\n\u001b[1;32m    306\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(u) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m    307\u001b[0m             \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>r\u001b[39;00m(\n\u001b[1;32m    308\u001b[0m                 \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mnot enough keys to break ties: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m \u001b[38;5;241m%\u001b[39m u)\n\u001b[0;32m--> 309\u001b[0m \u001b[38;5;28;01myield from\u001b[39;00m value\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/sorting.py:309\u001b[0m, in \u001b[0;36mordered\u001b[0;34m(seq, keys, default, warn)\u001b[0m\n\u001b[1;32m    306\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(u) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m    307\u001b[0m             \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>r\u001b[39;00m(\n\u001b[1;32m    308\u001b[0m                 \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mnot enough keys to break ties: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m \u001b[38;5;241m%\u001b[39m u)\n\u001b[0;32m--> 309\u001b[0m \u001b[38;5;28;01myield from\u001b[39;00m value\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/sorting.py:291\u001b[0m, in \u001b[0;36mordered\u001b[0;34m(seq, keys, default, warn)\u001b[0m\n\u001b[1;32m    289\u001b[0m         keys \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m    290\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m seq:\n\u001b[0;32m--> 291\u001b[0m         d[\u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m)\u001b[49m]\u001b[38;5;241m.\u001b[39mappend(a)\n\u001b[1;32m    292\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    293\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m default:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/sorting.py:123\u001b[0m, in \u001b[0;36mdefault_sort_key\u001b[0;34m(item, order)\u001b[0m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdefault_sort_key\u001b[39m(item, order\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m     11\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Return a key that can be used for sorting.\u001b[39;00m\n\u001b[1;32m     12\u001b[0m \n\u001b[1;32m     13\u001b[0m \u001b[38;5;124;03m    The key has the structure:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    121\u001b[0m \n\u001b[1;32m    122\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 123\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbasic\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Basic\n\u001b[1;32m    124\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msingleton\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m S\n\u001b[1;32m    126\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(item, Basic):\n", "File \u001b[0;32m<frozen importlib._bootstrap>:645\u001b[0m, in \u001b[0;36mparent\u001b[0;34m(self)\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["WW = Integral(sqrt(2*m*(E+k/r+pt**2/(2*m*r**2))), r)\n", "diff(WW, pt).doit()"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\int \\frac{\\sqrt{2} p_{\\theta} \\sqrt{m \\left(E + \\frac{k}{r} + \\frac{p_{\\theta}^{2}}{2 m r^{2}}\\right)}}{2 m r^{2} \\left(E + \\frac{k}{r} + \\frac{p_{\\theta}^{2}}{2 m r^{2}}\\right)}\\, dr$"], "text/plain": ["Integral(sqrt(2)*p_theta*sqrt(m*(E + k/r + p_theta**2/(2*m*r**2)))/(2*m*r**2*(E + k/r + p_theta**2/(2*m*r**2))), r)"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["diff(WW,pt)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{\\sqrt{2} p_{\\theta} \\left(\\begin{cases} \\left(- \\frac{E m}{p_{\\theta}^{2}} + \\frac{3 k^{2} m^{2}}{2 p_{\\theta}^{4}}\\right) \\left(\\begin{cases} \\frac{\\sqrt{2} \\log{\\left(k + \\sqrt{2} \\sqrt{\\frac{p_{\\theta}^{2}}{m}} \\sqrt{E + k u + \\frac{p_{\\theta}^{2} u^{2}}{2 m}} + \\frac{p_{\\theta}^{2} u}{m} \\right)}}{\\sqrt{\\frac{p_{\\theta}^{2}}{m}}} & \\text{for}\\: E - \\frac{k^{2} m}{2 p_{\\theta}^{2}} \\neq 0 \\\\\\frac{\\sqrt{2} \\left(\\frac{k m}{p_{\\theta}^{2}} + u\\right) \\log{\\left(\\frac{k m}{p_{\\theta}^{2}} + u \\right)}}{\\sqrt{\\frac{p_{\\theta}^{2} \\left(\\frac{k m}{p_{\\theta}^{2}} + u\\right)^{2}}{m}}} & \\text{otherwise} \\end{cases}\\right) + \\left(- \\frac{3 k m^{2}}{p_{\\theta}^{4}} + \\frac{m u}{p_{\\theta}^{2}}\\right) \\sqrt{E + k u + \\frac{p_{\\theta}^{2} u^{2}}{2 m}} & \\text{for}\\: \\frac{p_{\\theta}^{2}}{m} \\neq 0 \\\\\\frac{2 \\left(E^{2} \\sqrt{E + k u} - \\frac{2 E \\left(E + k u\\right)^{\\frac{3}{2}}}{3} + \\frac{\\left(E + k u\\right)^{\\frac{5}{2}}}{5}\\right)}{k^{3}} & \\text{for}\\: k \\neq 0 \\\\\\frac{u^{3}}{3 \\sqrt{E}} & \\text{otherwise} \\end{cases}\\right)}{2 \\sqrt{m}}$"], "text/plain": ["-sqrt(2)*p_theta*Piecewise(((-E*m/p_theta**2 + 3*k**2*m**2/(2*p_theta**4))*Piecewise((sqrt(2)*log(k + sqrt(2)*sqrt(p_theta**2/m)*sqrt(E + k*u + p_theta**2*u**2/(2*m)) + p_theta**2*u/m)/sqrt(p_theta**2/m), Ne(E - k**2*m/(2*p_theta**2), 0)), (sqrt(2)*(k*m/p_theta**2 + u)*log(k*m/p_theta**2 + u)/sqrt(p_theta**2*(k*m/p_theta**2 + u)**2/m), True)) + (-3*k*m**2/p_theta**4 + m*u/p_theta**2)*sqrt(E + k*u + p_theta**2*u**2/(2*m)), Ne(p_theta**2/m, 0)), (2*(E**2*sqrt(E + k*u) - 2*E*(E + k*u)**(3/2)/3 + (E + k*u)**(5/2)/5)/k**3, Ne(k, 0)), (u**3/(3*sqrt(E)), True))/(2*sqrt(m))"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["(sqrt(2)*pt/(2*sqrt(m))*-Integral(1/(r**2*sqrt(E + k/r + pt**2/(2*m*r**2))).subs(r,1/u),u)).doit()"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [], "source": ["g, l0, m, k, t = symbols('g ell_0 m k t', real=True, positive=True)\n", "th = Function('theta')\n", "x = Function('x')"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle x{\\left(t \\right)} = C_{1} e^{- t \\sqrt{- \\frac{k}{m}}} + C_{2} e^{t \\sqrt{- \\frac{k}{m}}} + \\frac{g m}{k}$"], "text/plain": ["Eq(x(t), C1*exp(-t*sqrt(-k/m)) + C2*exp(t*sqrt(-k/m)) + g*m/k)"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["dsolve(Eq(m*diff(x,t,2), m*g - k*x))"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\theta{\\left(t \\right)} = C_{1} e^{- t \\sqrt{- \\frac{g}{\\ell_{0}}}} + C_{2} e^{t \\sqrt{- \\frac{g}{\\ell_{0}}}}$"], "text/plain": ["Eq(theta(t), C1*exp(-t*sqrt(-g/ell_0)) + C2*exp(t*sqrt(-g/ell_0)))"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["dsolve(Eq(l0*diff(th,t,2), -g*th))"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [], "source": ["x0, px0, A, B = symbols('x_0 p_{x_0} A B')\n", "th0, pth0, C, D = symbols(r'theta_0 p_{\\theta_0} C D')"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["BB = solve(Eq((A-B)*sqrt(k/m), px0/m).subs(A,solve(Eq(A+B+m*g/k, x0), A)[0]), B)[0]"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- \\frac{g m}{2 k} + \\frac{x_{0}}{2} + \\frac{p_{x_0}}{2 m \\sqrt{\\frac{k}{m}}}\n"]}], "source": ["print_latex(solve(Eq(A+BB + m*g/k, x0), A)[0])"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - D + \\theta_{0}$"], "text/plain": ["-D + theta_0"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["Eq()solve(Eq(th0, C+D),C)[0]"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["xx = A*exp(I*sqrt(k/m)*t) + B*exp(-I*sqrt(k/m)*t) "]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["BB = solve(Eq(xx.subs(t,0), x0), B)[0]"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"data": {"text/plain": ["{A: x_0/2 - I*p_{x_0}/(2*m*sqrt(k/m))}"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["(solve(Eq(diff(xx,t).subs(t,0).subs(B,BB), px0/m)))[0]"]}, {"cell_type": "code", "execution_count": 100, "metadata": {}, "outputs": [], "source": ["diffeq = Eq(diff(m*x(t),t,2), m*g - k*x(t))"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [], "source": ["res = dsolve(diffeq, ics={x(0):x0, diff(x(t),t).subs(t,0):px0/m})"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle x{\\left(t \\right)} = \\frac{g m}{k} + \\frac{\\left(- g m + k x_{0}\\right) \\cos{\\left(\\frac{\\sqrt{k} t}{\\sqrt{m}} \\right)}}{k} + \\frac{p_{x_0} \\sin{\\left(\\frac{\\sqrt{k} t}{\\sqrt{m}} \\right)}}{\\sqrt{k} \\sqrt{m}}$"], "text/plain": ["Eq(x(t), g*m/k + (-g*m + k*x_0)*cos(sqrt(k)*t/sqrt(m))/k + p_{x_0}*sin(sqrt(k)*t/sqrt(m))/(sqrt(k)*sqrt(m)))"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["res"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\theta{\\left(t \\right)} = C_{1} \\sin{\\left(\\frac{\\sqrt{g} t}{\\sqrt{\\ell_{0}}} \\right)} + C_{2} \\cos{\\left(\\frac{\\sqrt{g} t}{\\sqrt{\\ell_{0}}} \\right)}\n"]}], "source": ["print_latex(dsolve(Eq(l0*diff(th(t),t,2), -g*th(t))))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\beta \\int \\frac{1}{\\sqrt{2 \\alpha m - \\frac{\\beta^{2}}{r^{2}} + \\frac{2 k m}{r}}}\\, dr$"], "text/plain": ["beta*Integral(1/sqrt(2*alpha*m - beta**2/r**2 + 2*k*m/r), r)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from sympy import *\n", "m, al, k ,r, beta, ga, t = symbols('m alpha k r beta gamma t')\n", "\n", "integrate(beta/sqrt(2*m*al + 2*m*k/r - beta**2/r**2), r).doit()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["q1 = Function('q_1')(t)\n", "q2 = Function('q_2')(t)\n", "p1 = Function('p_1')(t)\n", "p2 = Function('p_2')(t)\n", "tau = Function('tau')(t)\n", "l0 = symbols(r'\\ell_0')\n", "l = Function(r'\\ell')(t)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 0$"], "text/plain": ["0"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["integrate(p1*diff(q1,tau)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "mainenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}