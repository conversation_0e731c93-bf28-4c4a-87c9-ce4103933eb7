\documentclass[12pt]{article}%
\usepackage{amsfonts}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{comment}
\usepackage[a4paper, top=2.5cm, bottom=2.5cm, left=2.2cm, right=2.2cm]{geometry}
\def\shrinkage{2.1mu}
\def\vecsign{\mathchar"017E}
\def\dvecsign{\smash{\stackon[-1.95pt]{\mkern-\shrinkage\vecsign}{\rotatebox{180}{$\mkern-\shrinkage\vecsign$}}}}
\def\dvec#1{\def\useanchorwidth{T}\stackon[-4.2pt]{#1}{\,\dvecsign}}
\usepackage{stackengine}
\stackMath
\usepackage{times}
\usepackage{cancel}
\usepackage{amsmath}
\usepackage{tikz}
\usepackage{changepage}
\usetikzlibrary{shapes.misc}
\usepackage{amssymb}
\usepackage{graphicx}%
\setcounter{MaxMatrixCols}{30}
\newtheorem{theorem}{Theorem}
\newtheorem{acknowledgement}[theorem]{Acknowledgement}
\newtheorem{algorithm}[theorem]{Algorithm}
\newtheorem{axiom}{Axiom}
\newtheorem{case}[theorem]{Case}
\newtheorem{claim}[theorem]{Claim}
\newtheorem{conclusion}[theorem]{Conclusion}
\newtheorem{condition}[theorem]{Condition}
\newtheorem{conjecture}[theorem]{Conjecture}
\newtheorem{corollary}[theorem]{Corollary}
\newtheorem{criterion}[theorem]{Criterion}
\newtheorem{definition}[theorem]{Definition}
\newtheorem{example}[theorem]{Example}
\newtheorem{exercise}[theorem]{Exercise}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{notation}[theorem]{Notation}
\newtheorem{problem}[theorem]{Problem}
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{remark}[theorem]{Remark}
\newtheorem{summary}[theorem]{Summary}
\newenvironment{proof}[1][Proof]{\textbf{#1.} }{\ \rule{0.5em}{0.5em}}
\newenvironment{solution}[1][Solution]{\textbf{#1}}{\ \rule{0.5em}{0.5em}}
\newcommand{\Q}{\mathbb{Q}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\Z}{\mathbb{Z}}
\newcommand{\N}{\mathbb{N}}
\newcommand{\Lagr}{\mathcal{L}}
\newcommand{\lagr}{\mathcal{L}_0}
\newcommand{\lb}{\left\{}
\newcommand{\rb}{\right\}}
\newcommand{\set}[1]{\left\{#1\right\}}
\newcommand{\pr}[1]{\left(\{#1\right\})}
\newcommand{\dbar}[1]{\bar{\bar{#1}}}
\newcommand{\tbar}[1]{\bar{\bar{\bar{#1}}}}
\title{Physics 510 Final Exam}
\author{Tyler Hadsell}
\begin{document}
\maketitle
\begin{problem}
\end{problem}
\begin{solution}\\ 
    a. \\
    We can begin this problem by recalling the Hamilton-Jacobi equation:
    $$H\left(q,p,t\right) + \frac{\partial S\left(q,\alpha, t\right)}{\partial t} = 0$$
    We also utilize the following property of type 2 generating functions, which is the form of $S$:
    $$\frac{\partial S}{\partial q} = p$$
    We will then write out our Hamilton-Jacobi equation using our given Hamiltonian and the above relation:
    $$\frac{1}{2m}\left(\frac{\partial S}{\partial q}\right)^2 + \frac{1}{2}m\omega^2 q^2 + \frac{\gamma q}{2}\frac{\partial S}{\partial q} + \frac{\partial S}{\partial t} = 0$$
    We will now insist that $S$ takes the following form:
    $$S\left(q,\alpha,t\right)=W\left(q,\alpha\right) - \alpha t$$
    This gives our Hamilton-Jacobi equation the following form:
    $$\frac{1}{2m}\left(\frac{\partial W}{\partial q}\right)^2 + \frac{1}{2}m\omega^2q^2 + \frac{\gamma q}{2}\frac{\partial W}{\partial q} -\alpha =0$$
    We can solve for our $\frac{\partial W}{\partial q}$ using the quadratic formula. First we will get our equation into a standard form:
    $$\left(\frac{\partial W}{\partial q}\right)^2 + m^2\omega^2q^2 + \gamma m q \frac{\partial W}{\partial q} - 2\alpha m = 0$$
    Now we solve for our derivative:
    $$\frac{\partial W}{\partial q} = \frac{1}{2}\left(-\gamma m q \pm \sqrt{\gamma^2m^2q^2 - 4\left(m^2\omega^2q^2 - 2\alpha m\right)}\right)$$
    $$= - \frac{\gamma m q}{2} \pm \frac{\sqrt{m \left(8 \alpha + \gamma^{2} m q^{2} - 4 m \omega^{2} q^{2}\right)}}{2}$$
    We can now find our generating function:
    $$S\left(q,\alpha,t\right)= \int - \frac{\gamma m q}{2} \pm \frac{\sqrt{m \left(8 \alpha + \gamma^{2} m q^{2} - 4 m \omega^{2} q^{2}\right)}}{2} dq - \alpha t$$
    We now find a new integration constant $\beta$ by evaluating $\frac{\partial S}{\partial \alpha}$:
    $$\beta = \frac{\partial }{\partial \alpha}\int \pm \frac{\sqrt{m \left(8 \alpha + \gamma^{2} m q^{2} - 4 m \omega^{2} q^{2}\right)}}{2} dq - t$$
    $$=\int \frac{2 \sqrt{m \left(8 \alpha + \gamma^{2} m q^{2} - 4 m \omega^{2} q^{2}\right)}}{8 \alpha + \gamma^{2} m q^{2} - 4 m \omega^{2} q^{2}}dq - t= \int 2\sqrt{\frac{m}{8\alpha + \gamma^2 m q^2 - 4m\omega^2 q^2}}dq - t$$
    $$ = \sqrt{\frac{m}{2\alpha}}\int \frac{1}{\sqrt{1 - q^2\left(\frac{4m\omega^2 - \gamma^2 m}{8\alpha}\right)}} dq - t$$
    Let us redefine the constant multiplying $q^2$ in the integral:
    $$\text{Let } h^2 = \frac{8\alpha}{4m\omega^2 - \gamma^2m}$$
    $$\beta + t = \sqrt{\frac{m}{2\alpha}}\int \frac{1}{\sqrt{1 - \frac{q^2}{h^2}}}\, dq$$
    This integral can be solved analytically to yield the following function:
    $$\beta + t = \sqrt{\frac{m}{2\alpha}}h \arcsin{\left(\frac{q}{h}\right)}$$
    Let us invert this function to get $q$ as a function of time:
    $$\sqrt{\frac{2\alpha}{m}}\frac{\left(t + \beta\right)}{h} = \arcsin{\left(\frac{q}{h}\right)}$$
    $$q\left(t\right) = h\sin{\left(\sqrt{\frac{2\alpha}{m}}\frac{\left(t + \beta\right)}{h}\right)}$$
    $$= \boxed{\sqrt{\frac{2\alpha}{m\omega^2 - \frac{\gamma^2 m}{4}}} \sin{\left(\sqrt{\omega^2 - \frac{\gamma^2}{4}}\left(t + \beta\right)\right)}}$$
    We now can calculate the momentum $p$ as a function of time using the fact that $\frac{\partial W}{\partial q} = p$, and that we already have our $q$ as a function of time:
    $$\boxed{p = \sqrt{2 m \alpha}\cos{\left(\left(t+\beta\right)\sqrt{\omega^2 - \frac{\gamma^2}{4}}\right)} - \sqrt{\frac{m\alpha\gamma^2}{2\left(\omega^2 - \frac{\gamma^2}{4}\right)}}\sin{\left(\left(t+\beta\right)\sqrt{\omega^2 - \frac{\gamma^2}{4}}\right)}}$$
    b.\\ 
    Now lets solve for our integration constants $\beta$ and $\alpha$ in terms of our intitial conditions $q\left(t=0\right) = q_0$ and $p\left(t=0\right)=p_0$:
    First let us use our $q\left(t\right)$ function to obtain $\beta$ in terms of $q_0$ and $\alpha$
    $$\frac{\arcsin{\left(q_0\sqrt{\frac{m\omega^2 - \frac{\gamma^2m}{4}}{2\alpha}}\right)}}{\sqrt{\omega^2 - \frac{\gamma^2 }{4}}} = \beta$$
    Next we recall the Hamilton-Jacobi equation implies the following:
    $$H = -\frac{\partial S}{\partial t} = \alpha$$
    This means our initial Hamiltonian is equal to our $\alpha$ constant:
    $$\boxed{\alpha = \frac{p_0^2}{2m} + \frac{1}{2}m\omega^2 q_0^2 + \frac{\gamma}{2}q_0p_0}$$
    Substituting this into the $\beta$ equation we obtain the following ghastly expression:
    $$\boxed{\beta=\frac{\arcsin{\left(q_0\sqrt{\frac{m\omega^2 - \frac{\gamma^2 m}{4}}{2\left(\frac{p_0^2}{2m} + \frac{1}{2}m\omega^2 q_0^2 + \frac{\gamma}{2}q_0p_0\right)}}\right)}}{\sqrt{\omega^2 - \frac{\gamma^2 }{4}}}}$$



\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
    We can begin this problem by defining the following quantities relevant to the system:
    $$k = \text{Spring constant}$$
    $$\ell_1 = \text{Length of pendulum } 1$$
    $$\ell_2 = \text{Length of pendulum } 2$$
    $$d_1 = \text{Horizontal distance between pendulum pivot points}$$
    $$d_2 = \text{Vertical distance between pendulum pivot points}$$
    $$m_1 = \text{Mass of pendulum } 1$$
    $$m_2 = \text{Mass of pendulum } 2$$
    $$g = \text{Acceleration due to gravity}$$
    Next we can parametrize the motion of our pendulums in terms of two angles $\theta$ and $\phi$:
    $$x_1 = \ell_1 \sin \theta$$
    $$y_1 = -\ell_1 \cos\theta$$
    $$x_2 = d_1 + \ell_2 \sin \phi$$
    $$y_2 = d_2 - \ell_2 \cos\phi$$
    We can next find the velocities of our pendulum masses by taking the time derivatives of our coordinates:
    $$\dot x_1 = \ell_1 \dot \theta \cos \theta$$
    $$\dot y_1 = \ell_1 \dot \theta \sin \theta$$
    $$\dot x_2 = \ell_2 \dot \phi \cos \phi$$
    $$\dot y_2 = \ell_2 \dot \phi \sin\phi$$
    Next we can write the Lagrangian of our system with our new parametrized coordinates replacing our original cartesian coordinates:
    $$\Lagr = \frac{1}{2}m_1\ell_1^2 \dot \theta^2+\frac{1}{2}m_2\ell_2^2 \dot \phi^2 - \frac{1}{2}k\left(d_1 + \ell_2 \sin\phi - \ell_1 \sin\theta\right)^2 - \frac{1}{2}k\left(d_2 - \ell_2\cos\phi + \ell_1\cos\theta\right)^2$$$$ + m_1g\ell_1\cos\theta + m_2g\left(\ell_2\cos\phi - d_2\right)$$
    We can now obtain our equations of motion using the Euler-Lagrange equations:
    $$\frac{d }{d t}\frac{\partial \Lagr}{\partial \dot q_i} = \frac{\partial \Lagr}{\partial q_i}$$
    $$m_1 \ell_1^2 \ddot \theta = k\ell_1\cos\theta\left(d_1 + \ell_2\sin\phi - \ell_1 \sin\theta\right) + k\ell_1\sin\theta\left(d_2 - \ell_2\cos\phi + \ell_1 \cos\theta\right) - m_1g \ell_1\sin\theta$$
    $$m_2 \ell_2^2 \ddot \phi = -k\ell_2\cos\phi\left(d_1 + \ell_2\sin\phi - \ell_1\sin\theta\right) -k\ell_2\sin\phi\left(d_2 - \ell_2\cos\phi + \ell_1\cos\theta\right) - m_2g\ell_2\sin\phi$$
    We can simplify our equations of motion and employ small oscillation approximations to obtain the following form:
    $$\ddot \theta = \frac{k}{m_1\ell_1}\left(d_1 + \ell_2\phi - \ell_1 \theta\right) + \frac{k}{m_1\ell_1}\theta\left(d_2 - \ell_2 + \ell_1\right) - \frac{g}{\ell_1}\theta$$
    $$ = \theta\left(\frac{k}{m_1\ell_1}\left(d_2 - \ell_2\right) - \frac{g}{\ell_1}\right) + \frac{k\ell_2}{m_1\ell_1}\phi + \frac{kd_1}{m_1\ell_1} $$
    $$\ddot \phi = -\phi\left(\frac{k}{m_2\ell_2}\left(d_2 + \ell_1\right) + \frac{g}{\ell_2}\right) + \frac{k\ell_1}{m_2\ell_2}\theta - \frac{kd_1}{m_2\ell_2}$$
    We recognize that each equation has a constant term that has no dependence on the coordinates, these are constant forces that offset the equilibrium position of our oscillations. Since we are only interested in our normal frequencies we will ignore this term in the following analysis.
    We now have a system of differential equations of the following form:
    $$\ddot \theta = a\theta + b\phi$$
    $$\ddot \phi = c\theta + d\phi$$
    $$a = \frac{k}{m_1\ell_1}\left(d_2 - \ell_2\right) - \frac{g}{\ell_1}$$
    $$b = \frac{k\ell_2}{m_1\ell_1}$$
    $$c = \frac{k\ell_1}{m_2\ell_2}$$
    $$d = -\left(\frac{k}{m_2\ell_2}\left(d_2 + \ell_1\right) + \frac{g}{\ell_2}\right) $$
    We can now write this system as a matrix equation:
    $$\begin{pmatrix}
        \ddot \theta \\
        \ddot \phi
    \end{pmatrix} = \begin{pmatrix}
        a &b\\ 
        c & d
    \end{pmatrix}\begin{pmatrix}
        \theta \\ 
        \phi
    \end{pmatrix}$$
    Next we will assume an oscillator solution of the following form:
    $$\theta\left(t\right) = Ae^{i\left(\omega t + \delta_\theta\right)}$$
    $$\phi\left(t\right) = Be^{i\left(\omega t + \delta_\phi\right)}$$
    This gives us the following matrix equation:
    $$-\omega^2\begin{pmatrix}
        \theta \\ 
        \phi
    \end{pmatrix} = \begin{pmatrix}
        a & b\\ 
        c & d
    \end{pmatrix}\begin{pmatrix}
        \theta \\ 
        \phi
    \end{pmatrix}$$
    We now see this is a simple eigenvalue problem for the following case:
    $$\lambda \bar{V} = \dbar{M}\bar{V}$$
    $$\lambda = -\omega^2$$
    $$\dbar{M} = \begin{pmatrix}
        a & b\\ 
        c & d
    \end{pmatrix}$$
    Next we solve the eigenvalue equation:
    $$\det \begin{bmatrix}
        a-\lambda & b\\ 
        c & d -\lambda 
    \end{bmatrix} = 0$$
    $$\lambda^2 + ad -\lambda\left(a+d\right) - bc = 0$$
    $$\lambda = \frac{1}{2}\left(\left(a+d\right)\pm \sqrt{\left(a+d\right)^2 - 4\left(ad-bc\right)}\right)$$
    Now we solve for the eigenfrequencies and obtain the following form:
    $$\boxed{\omega = \sqrt{-\frac{1}{2}\left(\left(a+d\right)\pm \sqrt{\left(a+d\right)^2 - 4\left(ad-bc\right)}\right)}}$$
    We have taken the positive root of our outer square root since we require our frequencies to be positive real numbers.
    If we plug in limits where $m_1 = m_2 = M$, $\ell_1 = \ell_2 = \ell$ we obtain the following:
    $$a = \frac{k}{M\ell}\left(d_2 - \ell\right) - \frac{g}{\ell}$$
    $$b = c = \frac{k}{M}$$
    $$d = -\frac{k}{M\ell}\left(d_2 + \ell\right) - \frac{g}{\ell}$$
    $$a+d = -\frac{2k}{M} - \frac{2g}{\ell}$$
    $$ad = \frac{g^{2}}{\ell^{2}} + \frac{2 g k}{M \ell} - \frac{d_{2}^{2} k^{2}}{M^{2} \ell^{2}} + \frac{k^{2}}{M^{2}}$$
    $$bc = \frac{k^{2}}{M^{2}}$$
    Our two frequencies reduce to the following expressions:
    $$\omega_1 = \sqrt{\frac{\sqrt{\frac{4 d_{2}^{2} k^{2}}{M^{2} \ell^{2}} + \frac{4 k^{2}}{M^{2}}}}{2} + \frac{g}{\ell} + \frac{k}{M}}$$
    $$\omega_2 = \sqrt{-\frac{\sqrt{\frac{4 d_{2}^{2} k^{2}}{M^{2} \ell^{2}} + \frac{4 k^{2}}{M^{2}}}}{2} + \frac{g}{\ell} + \frac{k}{M}}$$
    We can see that our frequencies are made up of a combination of our spring frequency, the pendulum frequency and an extra term that accounts for the displacement of the two pendulum pivot points. This is an expected result at this limit.
\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
    We can begin this problem by defining our action integral $J$:
    $$J = \frac{1}{2\pi}\oint p\, dq$$
    We can write the Hamiltonian of our system within the box as the following:
    $$H = \frac{p^2}{2m}$$
    Since $\frac{\partial H}{\partial t} = 0$ we write the following:
    $$E = H = \frac{p^2}{2m}$$
    We can use Hamilton's equations to obtain the following:
    $$\dot q = \frac{p}{m}$$
    $$\dot p = 0$$
    We need to conceptually recognize that if we treat the potential barrier as a moving wall of infinite mass with a position given by the below equation, when the particle collides with the moving wall, it will recoil backwards with a higher velocity dependant on the velocity of the wall.
    $$\ell\left(t\right) = \ell_0 - v_w t$$
    We can determine this change in the particle's speed via the impulse acted on the particle during the collision in the wall's frame of reference. In this frame, the particle is moving a some speed towards the wall, while the wall is stationary. In an elastic collision, the particle will conserve it's energy and recoil backwards with equal velocity:
    $$\Delta p' = p_f' - p_i' = -m\left(v_p + v_w\right) -m\left(v_p + v_w\right) = -2m\left(v_p + v_w\right)$$
    In Classical Mechanics, the impulse is a frame invariant quantity, (in inertial reference frames) therefore we can find the final momentum of the particle in the lab frame by adding the impulse to the initial momentum:
    $$p_i + \Delta p = p_f$$
    $$mv_p - 2m\left(v_p + v_w\right) = -m\left(2v_w + v_p\right) = p_f$$
    We now see that our momentum is constant in time but recieves an impulse at $q\left(t\right) = \ell\left(t\right)$. Therefore we must split our integral into two separate integrals to account for this piecewise momentum:
    $$J = \frac{1}{2\pi} \int^{\ell\left(t\right)}_0 p_1 dq_1 + \frac{1}{2\pi}\int_{\ell\left(t\right)}^0 p_2 dq_2 = \frac{1}{2\pi}\left(\int^{\ell\left(t\right)}_0 mv_p dq_1 + \int_{\ell\left(t\right)}^0 -m\left(2v_w + v_p\right) dq_2\right)$$
    We now parametrize our integral with a new variable $\lambda$:
    $$J = \frac{1}{2\pi}\left(\int^{\lambda \left(\ell\left(t\right)\right)}_{\lambda_0} mv_p \frac{\partial q_1}{\partial \lambda} d\lambda + \int_{\lambda\left(\ell\left(t\right)\right)}^{\lambda_0} -m\left(2v_w + v_p\right) \frac{\partial q_2}{\partial \lambda} d\lambda \right)$$
    We will first define $\lambda$ in terms of a function of time which we will replace in terms of our coordinate $q$ to get our coordinate parametrized by $\lambda\left(q\right)$:
    $$\lambda\left(t\right) = \frac{\omega_0}{\omega\left(t\right)} = \frac{\frac{v_0}{\ell_0}}{\frac{v_0}{\ell\left(t\right)}} = \frac{\ell\left(t\right)}{\ell_0} = 1 - \frac{v_w t}{\ell_0}$$
    We can obtain our differential factor at this step as well:
    $$d\lambda = -\frac{v_w}{\ell_0} dt$$
    We recall our particle has the following equations of motion in its two piecewise modes:
    $$q_1\left(t\right) = v_p t$$
    $$q_2\left(t\right) = 2\ell_0 - \left(2v_w + v_p\right)t$$
    The factors for $q_2$ come from ensuring the functions agree at the collision time.
    Using our differential relation above we can obtain the differential relation between $q_i$ and $\lambda$:
    $$dq_1 = v_p dt = -\frac{\ell_0 v_p}{v_w}d\lambda $$
    $$dq_2 = -\left(2v_w + v_p\right)dt = \frac{\ell_0\left(2v_w + v_p\right)}{v_w} d\lambda$$
    We are now going to invert our position equations and place them into $\lambda\left(t\right)$ to obtain $\lambda\left(q\right)$:
    $$t\left(q_1\right)= \frac{q_1}{v_p}$$
    $$t\left(q_2\right) = \frac{2\ell_0 - q_2}{2v_w + v_p}$$
    $$\Rightarrow \lambda\left(q_1\right) = 1 - \frac{v_w q_1}{v_p \ell_0}$$
    $$\lambda\left(q_2\right) = 1-\frac{v_w}{\ell_0}\left(\frac{2\ell_0 - q_2}{2v_w + v_p}\right)$$
    Now we can perform our integrals with the following bound:
    $$J = \frac{1}{2\pi}\left(\int_1^{1-\frac{v_w}{\ell_0}\left(\frac{\ell_0 - v_w t}{v_p}\right)}mv_p\left(-\frac{\ell_0 v_p}{v_w}\right)d\lambda + \int_{1-\frac{v_w}{\ell_0}\left(\frac{\ell_0 - v_w t}{v_p}\right)}^1 -m\left(2v_w + v_p\right)\frac{\ell_0\left(2v_w + v_p\right)}{v_w} d\lambda\right)$$
    $$=\frac{mv_p}{2\pi}\left(\ell_0 - v_w t\right) -\frac{m}{2\pi v_p}\left(2v_w + v_p\right)^2\left(\ell_0 - v_w t\right)$$
    If we take the time derivative of this function we obtain the following:
    $$\frac{d J}{d t} = \frac{1}{2\pi}\left(-mv_pv_w + \frac{mv_w}{v_p}\left(2v_w + v_p\right)^2\right)$$
    If we take the limit of this where $v_w \ll v_p$ we obtain the following:
    $$\boxed{\lim_{v_w \ll v_p} \frac{d J}{d t} = \lim_{v_w \ll v_p} \frac{1}{2\pi}\left(-mv_pv_w + m\frac{v_w}{v_p}\left(4v_w^2 + v_p^2 + 4v_wv_p\right)\right) = \frac{1}{2\pi}\left(-mv_pv_w + mv_pv_w\right) = 0}$$
\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
    We can begin this problem by writing out the Hamiltonian of our system:
    $$H = \frac{p_r^2}{2m} + \frac{p_\theta^2}{2mr^2} - \frac{k}{r}$$
    We can next write our trivial transformed Hamiltonian and its momentum using the characteristic function $W\left(r,\theta\right)$:
    $$0 = \frac{1}{2m}\left(\frac{\partial W}{\partial r}\right)^2 + \frac{1}{2mr^2}\left(\frac{\partial W}{\partial \theta}\right)^2 -\frac{k}{r} - \alpha$$
    We can now assume that our characteristic function takes the following form due to the lack of $\theta$ dependence in the Hamiltonian:
    $$W\left(r,\theta\right) = W_r\left(r\right) + \beta_\theta \theta$$
    This simplifies our trivial Hamiltonian to the following:
    $$\frac{1}{2m}\left(\frac{\partial W_r}{\partial r}\right)^2 + \frac{\beta_\theta ^2}{2mr^2} - \frac{k}{r} - \alpha = 0$$
    We can then solve for our $W_r$ function:
    $$\frac{\partial W_r}{\partial r} = \sqrt{2m\alpha + \frac{2mk}{r} - \frac{\beta_\theta^2}{r^2}}$$
    $$W_r = \int \sqrt{2m\alpha + \frac{2mk}{r} - \frac{\beta_\theta^2}{r^2}} dr + \beta_\theta \theta$$
    Now we recall that taking the derivatives of our total characteristic $W$ function grant us the coordinate functions. In this case we are aiming to obtain the orbit of an ellipse. Therefore, we want our $\theta$ coordinate equation:
    $$\gamma_\theta = \frac{\partial W}{\partial \beta_\theta} = -\int \frac{\beta_\theta}{\sqrt{2m\alpha + \frac{2mk}{r} - \frac{\beta_\theta^2}{r^2}}}dr + \theta$$ Solving for $\theta$ we obtain the following equation:
    $$\boxed{\theta\left(r\right) = \frac{\partial W}{\partial \beta_\theta} = \int \frac{\beta_\theta}{\sqrt{2m\alpha + \frac{2mk}{r} - \frac{\beta_\theta^2}{r^2}}} dr - \gamma_\theta}$$
    This integral can be evaluated via advanced methods and yields the elliptical equation for an orbit once inverted to obtain $r\left(\theta\right)$. Additionally, all constants $\beta_\theta$, $\gamma_\theta$ and $\alpha$ can be obtained via the initial conditions of the system with $\alpha$ being again the total energy the system governed by our time-indepedent Hamiltonian.
\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\ 
    We can begin this problem by writing down a Lagrangian for our system in polar coordinates:
    $$x=\left(x+\ell_0\right)\sin\theta$$
    $$y=-\left(x+\ell_0\right)\cos\theta$$
    $$\Lagr = \frac{1}{2}m\left(\dot x^2 + \left(x+\ell_0\right)^2\dot \theta^2\right) + mg\left(x+\ell_0\right)\cos\theta - \frac{1}{2}k x^2$$
    Now using Euler-Lagrange equations we obtain the following equations of motion:
    $$m\ddot x = m\left(x+\ell_0\right)\dot \theta^2 + mg\cos\theta - kx$$
    $$m\left(x+\ell_0\right)^2 \ddot \theta + 2\left(x+\ell_0\right)\dot x \dot \theta = -mg\left(x+\ell_0\right)\sin\theta $$
    In their current form these coupled equations are non-integrable, and hence cannot be solved analytically. We can attempt to remove terms that are "small" under small oscillation regime by taking the following transformation:
    $$x \to \varepsilon x$$
    $$\theta \to \varepsilon \theta $$
    These transformations imply:
    $$\dot x \to \varepsilon \dot x$$
    $$\dot \theta \to \varepsilon \dot \theta$$
    $$\ddot x \to \varepsilon \ddot x$$
    $$\ddot \theta \to \varepsilon \ddot \theta$$
    Our equations take the following form under the transformation:
    $$\varepsilon m \ddot x = m\left(\varepsilon x + \ell_0\right)\varepsilon^2 \dot \theta^2 + mg\cos{\left(\varepsilon \theta\right)} - k\varepsilon x$$
    $$m\left(\varepsilon x + \ell_0\right)^2 \varepsilon \ddot \theta + \left(\varepsilon x + \ell_0\right)\varepsilon^2 \dot x \dot \theta = -mg\left(\varepsilon x+\ell_0\right)\sin{\left(\varepsilon \theta\right)}$$
    This $\varepsilon$ acts as a measure of order of each of our variables, with $\varepsilon$ being small and $\varepsilon^2$ being very small etc. This allows us to use small angle approximation and ignore second order or higher terms:
    $$\varepsilon m \ddot x = mg\varepsilon \theta - k\varepsilon x$$
    $$\Rightarrow m\ddot x = mg - kx$$
    $$m\ell_0 \varepsilon \ddot \theta = -mg\varepsilon \theta$$
    $$\Rightarrow \ell_0 \ddot \theta = -g\theta$$
    We can now solve these as individual harmonic oscillators.
    $$x{\left(t \right)} = A\sin{\left(\sqrt{\frac{k}{m}}t \right)} + B\cos{\left(\sqrt{\frac{k}{m}}t \right)} + \frac{g m}{k}$$
    $$\theta{\left(t \right)} = C \sin{\left(\sqrt{\frac{g}{\ell_0}}t \right)} + D\cos{\left(\sqrt{\frac{g}{\ell_0}}t \right)}$$
    We can now solve for our constants using initial conditions:
    $$x_0 = B+\frac{mg}{k}$$
    $$\frac{p_{x_0}}{m} = A\sqrt{\frac{k}{m}}$$
    $$\Rightarrow$$
    $$A = \frac{p_{x_0}}{\sqrt{mk}}$$
    $$B = x_0 - \frac{mg}{k}$$
    $$\theta_0 = D$$
    $$\frac{p_{\theta_0}}{m\left(x_0+\ell_0\right)^2} = C\sqrt{\frac{g}{\ell_0}}$$
    $$\Rightarrow$$
    $$C = \frac{p_{\theta_0}}{m\left(x_0+\ell_0\right)^2} \sqrt{\frac{\ell_0}{g}}$$
    $$\boxed{x\left(t\right) = \frac{p_{x_0}}{\sqrt{mk}}\sin{\left(\sqrt{\frac{k}{m}}t \right)} + \left(x_0 - \frac{mg}{k}\right)\cos{\left(\sqrt{\frac{k}{m}}t \right)}}$$
    $$\boxed{\theta(t) = \frac{p_{\theta_0}}{m\left(x_0+\ell_0\right)^2} \sqrt{\frac{\ell_0}{g}} \sin{\left(\sqrt{\frac{g}{\ell_0}}t\right)} + \theta_0 \cos{\left(\sqrt{\frac{g}{\ell_0}}t\right)}}$$

\end{solution}
\begin{problem}
\end{problem}
\begin{solution}
    $$Q=\frac{1}{p}$$
    $$P=p^2q$$
    We can show that the given transformations are canonical by utilizing the invariance of Poisson Brackets under canonical transformations:
    $$\left[Q,P\right]_{Q,P} = \left[Q,P\right]_{q,p}$$
    $$\left[Q,P\right]_{Q,P} = \frac{\partial Q}{\partial Q}\frac{\partial P}{\partial P} - \frac{\partial Q}{\partial P}\frac{\partial P}{\partial Q} = 1$$
    $$\left[Q,P\right]_{q,p} = \frac{\partial Q}{\partial q}\frac{\partial P}{\partial p} - \frac{\partial Q}{\partial p}\frac{\partial P}{\partial q} = \frac{\partial }{\partial q}\left(\frac{1}{p}\right) \frac{\partial }{\partial p}\left(p^2q\right) - \frac{\partial }{\partial p}\left(\frac{1}{p}\right)\frac{\partial }{\partial q}\left(p^2q\right)$$
    $$=\boxed{\frac{1}{p^2}p^2 = 1 \checkmark}$$
    We conclude the transformation set is in fact canonical.
    Next we can assume a generating function of type 2 $F = F_2\left(q,P,t\right)$. The following relations are valid for type 2 generating functions.
    $$\frac{\partial F_2}{\partial q} = p = \sqrt{\frac{P}{q}}$$
    $$\frac{\partial F_2}{\partial P} = Q = \frac{1}{p} = \sqrt{\frac{q}{P}}$$
    We can then integrate both of these equations to obtain our type 2 generating function.
    $$F_2 = 2\sqrt{Pq} + f_1(P)$$
    $$F_2 = 2\sqrt{Pq} + f_2(q)$$
    $$f_1 = f_2 = 0$$
    $$\boxed{F_2\left(q,P,t\right) = 2\sqrt{Pq}}$$

\end{solution}
\end{document}