\documentclass[10pt]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage[version=4]{mhchem}
\usepackage{stmaryrd}

\title{Fall 2024 Physics 550A: Quantum Mechanics Homework I }


\author{<PERSON><PERSON><PERSON>}
\date{}


\begin{document}
\maketitle
September 1, 2024

\begin{enumerate}
  \item Derive the inequality
\end{enumerate}

$$
|\langle u \mid v\rangle| \leq \sqrt{\langle u \mid u\rangle} \sqrt{\langle v \mid v\rangle}
$$

where $u$ and $v$ are Hilbert space vectors.\\
2. Derive the inequality

$$
\sqrt{\langle u+v \mid u+v\rangle} \leq \sqrt{\langle u \mid u\rangle}+\sqrt{\langle v \mid v\rangle}
$$

\begin{enumerate}
  \setcounter{enumi}{2}
  \item Sow that
\end{enumerate}

$$
\langle\phi \mid \psi\rangle=\sum_{\nu}\left\langle\phi \mid u_{\nu}\right\rangle\left\langle u_{\nu} \mid \psi\right\rangle
$$

is valid for arbitrary $\phi$ and $\psi$ if and only if the orthonormal vectors $u_{\nu}$ from a complete set.\\
4. Hermitian operator $A$ is positive definite if, for any vector $|u\rangle,\langle u|A| u\rangle \geq 0$. Show that $A=|a\rangle\langle a|$ is Hermitian and positive definite.\\
5. Show that the operator $-i \hbar d / d x$ is a linear Hermitian operator in the space of all differentiable functions $\phi(x)$ that vanish at both ends of an interval $(a, b)$.\\
6. Show that if $A$ and $B$ are two operators satisfying the relation $[[A, B], A]=0$, then the relation $\left[A^{m}, B\right]=m A^{m-1}[A, B]$ holds for all positive integers $m$.\\
7. Let $|u\rangle$ and $|v\rangle$ be two vectors with finite norm. Show that $\operatorname{Tr}(|u\rangle\langle v|)=\langle v \mid u\rangle$.\\
8. If $A$ is a linear operator, show that $A^{\dagger} A$ is positive definite Hermitian operator whose trace is the sum of the square moduli of the matrix elements of $A$ in any arbitrary representation. Show that $\operatorname{Tr}\left(A^{\dagger} A\right)=0$ is true if and only if $A=0$.\\
9. The derivative of an operator $A(\lambda)$ that depends on a continuous parameter $\lambda$ is defined to be

$$
\frac{\mathrm{d} A(\lambda)}{\mathrm{d} \lambda}=\lim _{\epsilon \rightarrow 0} \frac{A(\lambda+\epsilon)-A(\lambda)}{\epsilon}
$$

Show that

$$
\begin{aligned}
\frac{\mathrm{d}}{\mathrm{d} \lambda}(A B) & =\frac{\mathrm{d} A}{\mathrm{~d} \lambda} B+A \frac{\mathrm{d} B}{\mathrm{~d} \lambda} \\
\frac{\mathrm{d}}{\mathrm{d} \lambda}\left(A^{-1}\right) & =-A^{-1} \frac{\mathrm{d} A}{\mathrm{~d} \lambda} A^{-1}
\end{aligned}
$$

\begin{enumerate}
  \setcounter{enumi}{9}
  \item Show that the operator
\end{enumerate}

$$
B(t)=\exp (i A t) B_{0} \exp (-i A t)
$$

where $A$ and $B_{0}$ are operators independent of $t$, is a solution of the integral equation

$$
B(t)=B_{0}+i\left[A, \int_{0}^{t} B(\tau) d \tau\right]
$$

\begin{enumerate}
  \setcounter{enumi}{10}
  \item Show that for two operators $A$ and $L$
\end{enumerate}

$$
e^{L} A e^{-L}=A+[L, A]+\frac{1}{2!}[L,[L, A]]+\frac{1}{3!}[L,[L,[L, A]]]+\ldots
$$

\begin{enumerate}
  \setcounter{enumi}{11}
  \item Verify
\end{enumerate}

$$
\left[A, e^{-\beta H}\right]=e^{-\beta H} \int_{0}^{\beta} e^{\lambda H}[H, A] e^{-\lambda H} d \lambda
$$

where $A$ and $H$ are operators.\\
13. Show that a necessary and sufficient condition for a linear operator $U$ to be unitary that in any basis representation

$$
\begin{gathered}
\sum_{i}|\langle i|U| k\rangle|^{2}=1, \\
\sum_{i}\langle i|U| h\rangle\langle i|U| k\rangle^{*}=0, \quad h \neq k
\end{gathered}
$$

\begin{enumerate}
  \setcounter{enumi}{13}
  \item Show that if $[A,[A, B]]=[B,[A, B]]=0$, then
\end{enumerate}

$$
e^{A+B}=e^{-1 / 2[A, B]} e^{A} e^{B}
$$

\begin{enumerate}
  \setcounter{enumi}{14}
  \item Consider
\end{enumerate}

$$
Q=x \quad \text { and } \quad P=\frac{\hbar}{i} \frac{\mathrm{d}}{\mathrm{d} x}
$$

Show that they satisfy the commutation relation

$$
[P, Q]=\frac{\hbar}{i} I
$$

where $I$ is unit operator.\\
Then

$$
\operatorname{Tr}[P, Q]=\operatorname{Tr}(P Q)-\operatorname{Tr}(Q P)=0
$$

but on the other hand

$$
\operatorname{Tr}\left(\frac{\hbar}{i} I\right)=\frac{\hbar}{i} \operatorname{Tr}(I) \neq 0
$$

Explain please!\\
16. Define

$$
\hat{A}=\hat{P} \hat{Q}^{3}+\hat{Q}^{3} \hat{P}
$$

where $\hat{Q}$ and $\hat{P}$ are coordinate and momentum operators, respectively. Since $\hat{Q}$ and $\hat{P}$ are self-adjoint, so is $\hat{A}$

$$
\hat{A}^{\dagger}=\left(\hat{P} \hat{Q}^{3}+\hat{Q}^{3} \hat{P}\right)^{\dagger}=\hat{Q}^{3} \hat{P}+\hat{P} \hat{Q}^{3}=\hat{A}
$$

therefore the eigenvalues are real. Show that

$$
f(x)=\left\{\begin{array}{lll}
1 / \sqrt{2}|x|^{-3 / 2} \exp \left(-1 /(2 x)^{2}\right) & \text { if } \quad x \neq 0 \\
0 & \text { if } \quad x=0
\end{array}\right.
$$

is square integrable and

$$
\hat{A} f=-i \hbar f
$$

We have found that the eigenvalues of a self-adjoint operator is complex. Explain please!


\end{document}