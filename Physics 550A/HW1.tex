\documentclass[12pt]{article}%
\usepackage{amsfonts}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{comment}
\usepackage{physics}
\usepackage[a4paper, top=2.5cm, bottom=2.5cm, left=2.2cm, right=2.2cm]{geometry}
\def\shrinkage{2.1mu}
\def\vecsign{\mathchar"017E}
\def\dvecsign{\smash{\stackon[-1.95pt]{\mkern-\shrinkage\vecsign}{\rotatebox{180}{$\mkern-\shrinkage\vecsign$}}}}
\def\dvec#1{\def\useanchorwidth{T}\stackon[-4.2pt]{#1}{\,\dvecsign}}
\usepackage{stackengine}
\stackMath
\usepackage{times}
\usepackage{cancel}
\usepackage{amsmath}
\usepackage{tikz}
\usepackage{changepage}
\usetikzlibrary{shapes.misc}
\usepackage{amssymb}
\usepackage{graphicx}%
\setcounter{MaxMatrixCols}{30}
\newtheorem{theorem}{Theorem}
\newtheorem{acknowledgement}[theorem]{Acknowledgement}
\newtheorem{algorithm}[theorem]{Algorithm}
\newtheorem{axiom}{Axiom}
\newtheorem{case}[theorem]{Case}
\newtheorem{claim}[theorem]{Claim}
\newtheorem{conclusion}[theorem]{Conclusion}
\newtheorem{condition}[theorem]{Condition}
\newtheorem{conjecture}[theorem]{Conjecture}
\newtheorem{corollary}[theorem]{Corollary}
\newtheorem{criterion}[theorem]{Criterion}
\newtheorem{definition}[theorem]{Definition}
\newtheorem{example}[theorem]{Example}
\newtheorem{exercise}[theorem]{Exercise}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{notation}[theorem]{Notation}
\newtheorem{problem}[theorem]{Problem}
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{remark}[theorem]{Remark}
\newtheorem{summary}[theorem]{Summary}
\newenvironment{proof}[1][Proof]{\textbf{#1.} }{\ \rule{0.5em}{0.5em}}
\newenvironment{solution}[1][Solution]{\textbf{#1}}{\ \rule{0.5em}{0.5em}}
\newcommand{\Q}{\mathbb{Q}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\Z}{\mathbb{Z}}
\newcommand{\N}{\mathbb{N}}
\newcommand{\Lagr}{\mathcal{L}}
\newcommand{\lagr}{\mathcal{L}_0}
\newcommand{\lb}{\left\{}
\newcommand{\rb}{\right\}}
\newcommand{\set}[1]{\left\{#1\right\}}
\newcommand{\pr}[1]{\left(\{#1\right\})}
\newcommand{\dbar}[1]{\bar{\bar{#1}}}
\newcommand{\tbar}[1]{\bar{\bar{\bar{#1}}}}
\title{Physics 550A Homework}
\author{Tyler Hadsell}
\begin{document}
\maketitle

\begin{problem}
Derive the inequality
      $$
      |\langle u \mid v\rangle| \leq \sqrt{\langle u \mid u\rangle} \sqrt{\langle v \mid v\rangle}
      $$
      where $u$ and $v$ are Hilbert space vectors.\\
\end{problem}
\begin{solution}\\
Let us begin by proving the Cauchy-Schwarz inequality:
$$|\langle u|v\rangle|^2 \leq \langle u|u\rangle\langle v|v\rangle$$
We start with an vector defined as the following:
$$\ket{a} = \ket{u-\alpha v}$$
It follows:
$$0\leq \braket{a}{a} = \braket{u-\alpha v}{u-\alpha v} $$$$= \braket{u}{u} - \alpha\braket{u}{v} -\alpha^*\braket{v}{u} + \alpha \alpha^*\braket{v}{v}$$
Now we will optimize this equality to minimize the right side with respect to $\alpha$:
$$0 = \frac{d \braket{a}{a}}{d \alpha} = -\braket{u}{v} + \alpha^*\braket{v}{v}$$
$$\alpha^* = \frac{\braket{u}{v}}{\braket{v}{v}}$$
$$\alpha = \frac{\braket{v}{u}}{\braket{v}{v}}$$
Now we place our optimized $\alpha$ into our expression:
$$\braket{a}{a} = \braket{u}{u} - \frac{\braket{u}{v}\braket{v}{u}}{\braket{v}{v}} -\frac{\braket{v}{u}\braket{u}{v}}{\braket{v}{v}} $$$$+ \frac{\braket{u}{v}\braket{v}{u}}{\braket{v}{v}}$$
Now we begin with the left side of the given inequality and apply the proven Cauchy-Schwarz inequality:
$$\left|\langle u \mid v\rangle\right| \leq \left|u\right|\left|v\right|$$
$$0 \leq \braket{a}{a} = \braket{u}{u} - \frac{\left|\braket{u}{v}\right|^2}{\braket{v}{v}}$$
$$\left|\braket{u}{v}\right|^2\leq\left|u\right|^2\left|v\right|^2$$
Next we utilize the definition of a vector magnitude in Hilbert space to obtain the final right side of the inequality:
$$\left|\langle u \mid v\rangle\right| \leq \sqrt{\left|u\right|^2}\sqrt{\left|v\right|^2}= \sqrt{\langle u \mid u\rangle}\sqrt{\langle v \mid v\rangle}$$
Hence we obtain the desired inequality.
$$\boxed{\left|\langle u \mid v\rangle\right| \leq \sqrt{\langle u \mid u\rangle}\sqrt{\langle v \mid v\rangle}}$$
\end{solution}
\begin{problem}
    Derive the inequality

$$
\sqrt{\langle u+v \mid u+v\rangle} \leq \sqrt{\langle u \mid u\rangle}+\sqrt{\langle v \mid v\rangle}
$$
\end{problem}
\begin{solution}\\
First we begin with the left side of the inequality and exploit the linearity of the inner product to obtain the following:

$$\sqrt{\langle u+v \mid u+v\rangle} $$$$= \sqrt{\langle u \mid u\rangle + \langle u \mid v\rangle + \langle v \mid u\rangle + \langle v \mid v\rangle}$$
We recall the following relation between the real part of a complex number and the complex conjugate operation:
$$\Re(\langle y \mid x\rangle) = \frac{1}{2}(\langle y \mid x\rangle + \langle x \mid y\rangle)$$
Utilizing this relation, we can write:
$$\sqrt{\langle u+v \mid u+v\rangle}$$$$ = \sqrt{\langle u \mid u\rangle + 2\Re(\langle u \mid v\rangle) + \langle v \mid v\rangle}$$
We then utilize the following relation using Problem 1:
$$\Re (\langle u \mid v\rangle) \leq |\langle u \mid v\rangle| \leq \sqrt{\langle u \mid u\rangle}\sqrt{\langle v \mid v\rangle} $$$$= \left|u\right|\left|v\right|$$
Substituting this into the expression above gives:
$$\sqrt{\langle u+v \mid u+v\rangle} \leq \sqrt{\langle u \mid u\rangle + 2|u||v| + \langle v \mid v\rangle}$$$$=\sqrt{\left(\left|u\right| + \left|v\right|\right)^2} = \sqrt{\langle u \mid u\rangle} + \sqrt{\langle v \mid v\rangle}$$
Hence we have derived the desired inequality.
$$\boxed{\sqrt{\langle u+v \mid u+v\rangle} \leq \sqrt{\langle u \mid u\rangle}+\sqrt{\langle v \mid v\rangle}}$$
\end{solution}

\begin{problem}
Show that
$$
\langle\phi \mid \psi\rangle=\sum_{\nu}\left\langle\phi \mid u_{\nu}\right\rangle\left\langle u_{\nu} \mid \psi\right\rangle
$$
is valid for arbitrary $\phi$ and $\psi$ if and only if the orthonormal vectors $u_{\nu}$ form a complete set.
\end{problem}
\begin{solution}\\
To prove this, we will show both directions of the if and only if statement:
$\Rightarrow$ Suppose the orthonormal vectors $u_\nu$ form a complete set. Then any vector $\phi$ and $\psi$ can be written as:
$$\ket{\psi} = \sum_{\nu} c_\nu \ket{u_\nu}$$
$$\ket{\phi} = \sum_{\mu} d_\mu \ket{u_\mu}$$
Where the coefficients $c_\nu$ and $d_\mu$ are given by the projections:
$$c_\nu = \langle u_\nu | \psi \rangle$$
$$d_\mu = \langle \phi | u_\mu \rangle$$
Then we have:
$$\langle\phi \mid \psi\rangle = \left\langle \sum_{\mu} d_\mu \ket{u_\mu} \middle| \sum_{\nu} c_\nu \ket{u_\nu} \right\rangle $$$$= \sum_{\mu,\nu} d_\mu^* c_\nu \langle u_\mu \mid u_\nu \rangle $$$$= \sum_{\nu} d_\nu^* c_\nu = \boxed{\sum_{\nu}\left\langle\phi \mid u_{\nu}\right\rangle\left\langle u_{\nu} \mid \psi\right\rangle}$$
We have hence shown the forward direction.
Next to begin the backward direction, we will assume the given expression is valid for arbitrary $\phi$ and $\psi$.
$$\braket{\phi}{\psi}=\sum_\nu\braket{\phi}{u_\nu}\braket{u_\nu}{\psi}$$
Consider a vector $\chi$ that is orthogonal to all $u_\nu$:
$$\braket{u_\nu}{\chi}=0\ \ \forall\ \nu$$
Then by inserting $\chi$ into the given expression we obtain:
$$\braket{\phi}{\chi}=\sum_\nu\braket{\phi}{u_\nu}\braket{u_\nu}{\chi}=0$$
Since this must hold for arbitrary $\phi$, it implies $\chi=0$. Therefore the set of $u_\nu$ must span the entire Hilbert space and hence form a complete set.

\end{solution}

\begin{problem}
Hermitian operator $A$ is positive definite if, for any vector $|u\rangle,\langle u|A| u\rangle \geq 0$. Show that $A=|a\rangle\langle a|$ is Hermitian and positive definite.
\end{problem}
\begin{solution}\\
We can begin by creating an arbitrary state ket $|u\rangle$ and applying the operator $A=|a\rangle\langle a|$:
$$\langle u|A|u\rangle = \langle u|a\rangle\langle a|u\rangle$$ We can then notice the following about the current form:
$$\boxed{\braket{a}{u}\braket{u}{a} = \left|\braket{a}{u}\right|^2 \geq 0}$$
This gives us our desired result that for any $|u\rangle$, $\braket{u}{A|u} \geq 0$. Therefore, the operator is positive definite. Additionally, an operator $A$ is Hermitian if and only if $A = A^\dagger$. We can show this by starting with the following:
$$A = |a\rangle\langle a|$$
$$\boxed{A^\dagger = (|a\rangle\langle a|)^\dagger = \langle a|^\dagger |a\rangle^\dagger = |a\rangle\langle a| = A}$$
We have hence shown using the properties of Dirac notation that the operator $A=|a\rangle\langle a|$ is Hermitian. Since we have already shown it is positive definite, this completes the proof.
\end{solution}

\begin{problem}
Show that the operator $-i \hbar \frac{d}{dx}$ is a linear Hermitian operator in the space of all differentiable functions $\phi(x)$ that vanish at both ends of an interval $(a, b)$.
\end{problem}
\begin{solution}\\
    We can begin by showing the linearity of the operator $-i\hbar\frac{d}{dx}$. For any two differentiable functions $\phi_1(x)$ and $\phi_2(x)$ that vanish at the endpoints, and any constants $\alpha$ and $\beta$, we have:
    $$\left(-i\hbar\frac{d}{dx}\right)\left(\alpha\phi_1(x)+\beta\phi_2(x)\right) $$$$= -i\hbar\left(\alpha\frac{d\phi_1(x)}{dx}+\beta\frac{d\phi_2(x)}{dx}\right) $$$$= \alpha\left(-i\hbar\frac{d\phi_1(x)}{dx}\right) + \beta\left(-i\hbar\frac{d\phi_2(x)}{dx}\right)$$
    Therefore, the operator is linear. \\
    Now we can show it is Hermitian. For any two functions $\phi_1(x)$ and $\phi_2(x)$ in the space, an operator $A$ being Hermitian, implies the following:
    $$\braket{\phi_1}{A\phi_2} = \braket{A\phi_1}{\phi_2}$$
    We can calculate the left side of this inequality using the following integral form:
    $$\braket{\phi_1}{-i\hbar\frac{d\phi_2}{dx}} = -i\hbar\int_a^b \phi_1^*(x)\frac{d\phi_2(x)}{dx}dx$$
    Integrating by parts, this gives:
    $$\braket{\phi_1}{-i\hbar\frac{d\phi_2}{dx}} =$$$$ -i\hbar\left[\phi_1^*(x)\phi_2(x)\right]_a^b + i\hbar\int_a^b \frac{d\phi_1^*(x)}{dx}\phi_2(x)dx$$
    Since both $\phi_1(x)$ and $\phi_2(x)$ vanish at the endpoints, the boundary term is zero. Additionally, the right side is just $\braket{i\hbar\frac{d\phi_1}{dx}}{\phi_2}$. Therefore, the operator is Hermitian.
    

\end{solution}


\begin{problem}
Show that if $A$ and $B$ are two operators satisfying the relation $[[A, B], A]=0$, then the relation $\left[A^{m}, B\right]=m A^{m-1}[A, B]$ holds for all positive integers $m$.
\end{problem}
\begin{solution}\\
We can begin this problem by writing out the left side of our relation:
$$\left[A^m, B\right] = A^m B - B A^m$$ We can now proceed by induction. For $m=1$ we obtain:
$$\left[A, B\right] = AB - BA = [A,B]$$
Now let us assume it works up to arbitrary $m=k$ and show that it holds for $m=k+1$:
$$\left[A^{k+1},B\right] = A^{k+1}B - BA^{k+1} = A A^k B - B A^k A  $$$$= A\left[A^k,B\right] + \left[A,B\right]A^k$$
Using the inductive hypothesis, this is equal to:
$$kA^{k}[A,B] + \left[A,B\right]A^k$$ We can add and subtract the term $A^k[A,B]$ to obtain:
$$kA^{k}[A,B] + \left[A,B\right]A^k + A^k[A,B] - A^k[A,B]$$
$$= (k+1)A^k[A,B] - \left[A^k, \left[A,B\right]\right]$$
We know that $[[A,B],A]=0$ by the given relation and we will now show that this implies $[A^k,[A,B]]=0$ for all $k$. To see this, let us define:
$$C = [A,B]$$
Then we have:
$$[C,A] =0$$
Let us assume this relation holds for arbitrary $k$:
$$[C,A^k]=0$$
Then for $k+1$:
$$[C,A^{k+1}]=[C,AA^k] = CAA^k - A^kAC $$$$= \left(CA^k - A^kC\right)A + A^kCA - A^k A C$$
$$= [C,A^k]A + A^k[C,A] = 0$$
Both terms on the right hand side are zero by the inductive hypothesis and the given relation respectively. Therefore, $[C,A^{k+1}]=0$ for all positive integers $k$. This implies that the term $-\left[A^k,[A,B]\right]$ in our expression above is zero, leaving us with:
$$\left[A^{k+1},B\right] = (k+1)A^k[A,B]$$ Which when reindexed with $m$ yields:
$$\left[A^{m},B\right] = mA^{m-1}[A,B]\, \forall m\in \Z_+$$
\end{solution}

\begin{problem}
Let $|u\rangle$ and $|v\rangle$ be two vectors with finite norm. Show that $\operatorname{Tr}(|u\rangle\langle v|)=\langle v \mid u\rangle$.
\end{problem}
\begin{solution}\\
    We can calculate the trace of the operator $|u\rangle\langle v|$ as follows:
    $$\operatorname{Tr}(|u\rangle\langle v|) = \sum_n \braket{\psi_n}{u\rangle\langle v|\psi_n}$$
    Where $\{\psi_n\}$ forms an orthonormal basis. Since both brakets are scalars we can commute them rearranging to form the following:
    $$\operatorname{Tr}(|u\rangle\langle v|) = \sum_n \langle v|\psi_n\rangle\braket{\psi_n}{u}$$
    We can also push the sum into the braket to obtain the following:
    $$\operatorname{Tr}(|u\rangle\langle v|) = \langle v|\left(\sum_n|\psi_n\rangle\bra{\psi_n}\right)|u\rangle = \langle v|u\rangle$$
    Where we have used the completeness relation for the orthonormal basis $\sum_n|\psi_n\rangle\bra{\psi_n}=I$. Therefore, the trace is equal to the inner product $\langle v|u\rangle$.
\end{solution}

\begin{problem}
If $A$ is a linear operator, show that $A^{\dagger} A$ is positive definite Hermitian operator whose trace is the sum of the square moduli of the matrix elements of $A$ in any arbitrary representation. Show that $\operatorname{Tr}\left(A^{\dagger} A\right)=0$ is true if and only if $A=0$.
\end{problem}

\begin{solution}\\
    To show that $A^{\dagger}A$ is positive definite and Hermitian, let $|u\rangle$ be any vector in the space. Then we have:
    $$ \langle u|A^{\dagger}A|u\rangle = \langle A u|A u\rangle = ||A u||^2 \geq 0 $$
    Next to show it is Hermitian, we define the following:
    Let $$B=A^{\dagger}A$$
    Then $$B^{\dagger}=(A^{\dagger}A)^{\dagger}=A^{\dagger}(A^{\dagger})^\dagger=A^{\dagger}A=B$$
    
    Therefore $A^{\dagger}A$ is positive definite and Hermitian as required.
    Since the norm squared of any vector is non-negative, $A^{\dagger}A$ is positive definite. Additionally, since $A^{\dagger}A$ is self-adjoint, it is also Hermitian.
    Next, we can calculate the trace of $A^{\dagger}A$ using the definition:
    $$\operatorname{Tr}(A^{\dagger}A) = \sum_n \langle \psi_n | A^{\dagger}A|\psi_n\rangle $$$$= \sum_n \sum_m \bra{\psi_n}A^\dagger\ket{\psi_m}\bra{\psi_m}A \ket{\psi_n}$$
    $$= \sum_n \sum_m \left|\bra{\psi_m}A\ket{\psi_n}\right|^2= \sum_n \sum_m |A_{mn}|^2$$
    Where $A_{mn} = \bra{\psi_m}A\ket{\psi_n}$ are the matrix elements of $A$ in the arbitrary basis $\{\ket{\psi_n}\}$.
    This implies that since $|A_{mn}|^2 \geq 0$ the trace of $A^{\dagger}A$ is the sum of the squares of the matrix elements and is therefore non-negative and can only be equal to $0$ if each individual matrix element is equal to $0$, which occurs if and only if $A=0$.
\end{solution}

\begin{problem}
The derivative of an operator $A(\lambda)$ that depends on a continuous parameter $\lambda$ is defined to be

$
\frac{\mathrm{d} A(\lambda)}{\mathrm{d} \lambda}=\lim _{\epsilon \rightarrow 0} \frac{A(\lambda+\epsilon)-A(\lambda)}{\epsilon}
$

Show that

$
\begin{aligned}
\frac{\mathrm{d}}{\mathrm{d} \lambda}(A B) & =\frac{\mathrm{d} A}{\mathrm{~d} \lambda} B+A \frac{\mathrm{d} B}{\mathrm{~d} \lambda} \\
\frac{\mathrm{d}}{\mathrm{d} \lambda}\left(A^{-1}\right) & =-A^{-1} \frac{\mathrm{d} A}{\mathrm{~d} \lambda} A^{-1}
\end{aligned}
$
\end{problem}
\begin{solution}\\
We can begin by rearranging the definition of the derivative of an operator to find the value of a function after a small step $\epsilon$:
$$A(\lambda+\epsilon)=A(\lambda)+\epsilon\frac{dA}{d\lambda}+O(\epsilon^2)$$
Where $O(\epsilon^2)$ represents terms of order $\epsilon^2$ or higher that vanish in the limit $\epsilon\to0$.
Then, for the product rule:
$$\frac{d \left(AB\right)}{d \lambda} = \lim_{\epsilon \to 0} \frac{A(\lambda+\epsilon)B(\lambda+\epsilon)-A(\lambda)B(\lambda)}{\epsilon}$$
We will now replace the first term in our numerator with our approximation for $A(\lambda+\epsilon)$:
$$\frac{d \left(AB\right)}{d \lambda} = $$$$ \lim_{\epsilon \to 0} \frac{(A(\lambda)+\epsilon\frac{dA}{d\lambda})B(\lambda+\epsilon)-A(\lambda)B(\lambda)}{\epsilon}$$
Expanding the $B(\lambda+\epsilon)$ term similarly, this becomes:
$$\frac{d \left(AB\right)}{d \lambda} = \lim_{\epsilon \to 0} \frac{A(\lambda)\epsilon\frac{dB}{d\lambda}+\epsilon\frac{dA}{d\lambda}B(\lambda)}{\epsilon}$$
Cancelling out the $\epsilon$ and taking the limit gives:
$$\frac{d \left(AB\right)}{d \lambda} = \frac{dA}{d\lambda}B+A\frac{dB}{d\lambda}$$
For the next part we will use our newly proven product rule starting with the following expression:
$$A^{-1}A=I$$
Taking the derivative of both sides with respect to $\lambda$ gives:
$$\frac{d}{d\lambda}(A^{-1}A)=\frac{d}{d\lambda}I=0$$
We then apply the product rule to obtain the following:
$$\frac{d A^-1}{d \lambda}A + A^{-1}\frac{dA}{d\lambda} = 0$$
Since $A^{-1}A=I$, we can multiply both sides by $A^{-1}$ from the left to obtain:
$$\frac{d A^{-1}}{d \lambda} + A^{-1}\frac{dA}{d\lambda}A^{-1} = 0$$
Finally we subtract to the other side and obtain:
$$\frac{dA^{-1}}{d\lambda} = -A^{-1}\frac{dA}{d\lambda}A^{-1}$$
\end{solution}

\begin{problem}
Show that the operator
$
B(t)=\exp (i A t) B_{0} \exp (-i A t)
$
where $A$ and $B_{0}$ are operators independent of $t$, is a solution of the integral equation
$
B(t)=B_{0}+i\left[A, \int_{0}^{t} B(\tau) d \tau\right]
$
\end{problem}
\begin{solution}\\
The easiest way to solve this problem is to show that the derivatives with respect to $t$ of both forms are equal and have the same initial conditions at $t=0$. Taking the derivative of $B(t)=\exp (i A t) B_{0} \exp (-i A t)$, we obtain:
$$\frac{d}{dt}(\exp (i A t) B_{0} \exp (-i A t))$$
$$=iAe^{iAt}B_0e^{-iAt}-ie^{iAt}B_0Ae^{-iAt}$$
We recall that for any operator $A$, $A$ commutes with all functions of $A$, therefore:
$$\frac{d B\left(t\right)}{d t} = iAe^{iAt}B_0e^{-iAt}-ie^{iAt}B_0Ae^{-iAt} $$$$= iAe^{iAt}B_0e^{-iAt}-ie^{iAt}B_0e^{-iAt}A$$
$$= i[A,B\left(t\right)]$$
Taking the derivative of the integral equation we obtain:
$$\frac{d}{dt}\left(B_{0} + i\left[A, \int_{0}^{t} B(\tau) d \tau\right]\right)$$
$$=\frac{d}{dt}\left(i\left[A, \int_{0}^{t} B(\tau) d \tau\right]\right)$$
We can then expand the commutator bracket and utilize the Fundamental Theorem of Calculus to obtain:
$$i[A,B(t)] = iA B(t) - i B(t) A = \frac{d B(t)}{dt}$$
The derivatives are equal and the initial conditions at $t=0$ are also equal since $B(0)=B_0$. Therefore, the given form $B(t)=\exp (i A t) B_{0} \exp (-i A t)$ is a solution to the integral equation.
\end{solution}

\begin{problem}
Show that for two operators $A$ and $L$
$
e^{L} A e^{-L}=A+[L, A]+\frac{1}{2!}[L,[L, A]]+\frac{1}{3!}[L,[L,[L, A]]]+\ldots
$
\end{problem}
\begin{solution}\\
We can begin this problem by rewriting our exponentials as Taylor Series:
$$e^LAe^{-L} = $$$$\left(1 + L + \frac{L^2}{2!} + \frac{L^3}{3!} + ... \right)A\left(1 - L + \frac{L^2}{2!} - \frac{L^3}{3!} + ... \right)$$
Expanding this out using the properties of operators, commutators and factorials, we obtain:
$$A + LA - AL + \frac{L^2}{2!}A - \frac{L^3}{3!}A + \frac{L^2}{2!}AL - \frac{L^3}{6!}A + ...$$
Using the fact that $[L,A] = LA - AL$, we can group the terms to obtain:
$$A + [L,A] + \frac{1}{2!}\left(L\left(LA - LAL\right)\right) + $$$$\frac{1}{3!}\left(L\left(L\left(LA - LAL\right)\right)\right) + \ldots$$
$$= A + [L,A] + \frac{1}{2!}[L,[L,A]] + \frac{1}{3!}[L,[L,[L,A]]] + \ldots$$
This proves the equation requested for operators $A$ and $L$. By repeatedly using the commutator identity $[L,A] = LA - AL$ and properties of operators, factorials, and Taylor series, we have shown that $e^{L} A e^{-L}$ can be expressed as a series involving nested commutators as stated.
\end{solution}

\begin{problem}
Verify
$
\left[A, e^{-\beta H}\right]=e^{-\beta H} \int_{0}^{\beta} e^{\lambda H}[H, A] e^{-\lambda H} d \lambda
$
where $A$ and $H$ are operators.
\end{problem}
\begin{solution}\\ 
We will prove these equations are the same first by showing they have the same initial condition at $\beta=0$:
If we plug in $\beta=0$ into the right hand side, we obtain:\
$$e^{-0H} \int _{0}^{0} e^{\lambda H}[H, A] e^{-\lambda H} d\lambda = 0$$
Now if we plug the same value into the left  hand side we obtain:
$$[A, e^{-0H}] =\left[A,I\right] = 0$$
Therefore, both sides satisfy the same initial condition at $\beta=0$.
To show they are equal for all $\beta$, we can take the derivative of both sides with respect to $\beta$. The derivative of the left hand side is:
$$\frac{d}{d\beta}[A,e^{-\beta H}] = \frac{d }{d \beta}\left(Ae^{-\beta H} - e^{-\beta H}A\right)$$
$$=He^{-\beta H}A - AHe^{-\beta H}$$
$$= -H\left[A,e^{-\beta H}\right] + HAe^{-\beta H} - AHe^{-\beta H}$$
$$= -H\left[A,e^{-\beta H}\right] + [H,A]e^{-\beta H}$$
We can recognize the first argument as the original commutator. We will define this to be:
$$f = \left[A,e^{-\beta H}\right]$$
We hence have a differential equation:
$$\frac{df}{d\beta} = -Hf + [H,A]e^{-\beta H}$$
Taking the derivative of the right hand side with respect to $\beta$ gives:
$$\frac{d}{d\beta}\left(e^{-\beta H} \int_{0}^{\beta} e^{\lambda H}[H, A] e^{-\lambda H} d \lambda\right)=$$
$$-He^{-\beta H}\int_{0}^{\beta} e^{\lambda H}[H, A] e^{-\lambda H} d \lambda + [H,A]e^{-\beta H}$$
Similar to the last equation we can rename the integral as our original function $g$:
$$g = e^{-\beta H} \int_{0}^{\beta} e^{\lambda H}[H, A] e^{-\lambda H} d \lambda$$
Hence we again obtain a differential equation:
$$\frac{dg}{d\beta} = -Hg + [H,A]e^{-\beta H}$$
Since these two differential equations are the same, and the initial conditions of $f(0) = g(0)=0$ are also the same, by the uniqueness theorem of differential equations, we must have $f=g$. Therefore our equation is proven for operators $A$ and $H$.

\end{solution}

\begin{problem}
Show that a necessary and sufficient condition for a linear operator $U$ to be unitary that in any basis representation

$
\begin{gathered}
\sum_{i}|\langle i|U| k\rangle|^{2}=1, \\
\sum_{i}\langle i|U| h\rangle\langle i|U| k\rangle^{*}=0, \quad h \neq k
\end{gathered}
$
\end{problem}
\begin{solution}\\
First we will do the forwards proof and show that if  $U$ is unitary, then the given conditions hold.
If $U$ is unitary, then by definition we have:
$$U^{\dagger}U=I$$
Now we'll operate on an orthogonal basis:
$$\langle \psi_i|U^{\dagger}U|\psi_k\rangle= \braket{\psi_i}{\psi_k}=\delta_{ik}$$
Using the fact that U is unitary, the left hand side is equal to $\delta_{ik}$. Hence,
We can now write out the following brakets:
$$\sum_i \left|\bra \psi_i U \ket \psi_k\right|^2 = \sum_i \braket{\psi_i}{U \psi_k} \braket{\psi_i}{U \psi_k}^* $$
$$= \sum_i \braket{\psi_i}{U\psi_k}\braket{\psi_k}{U^\dagger\psi_i} = \sum_i \braket{U\psi_k}{\psi_i}\braket{\psi_i}{U\psi_k} = \braket{U \psi_k}{U\psi_k} = \braket{\psi_k}{\psi_k} = 1$$
We also notice that in the case where $k\neq h$, we obtain:
$$\sum_i \braket{\psi_i}{U\psi_h}\braket{\psi_i}{U\psi_k}^* = \braket{U\psi_h}{U\psi_k} = 0$$
since the vectors $U\psi_h$ and $U\psi_k$ are orthogonal for $h\neq k$.
Therefore, the given conditions are necessary for $U$ to be unitary.
To show sufficiency we will do the backwards proof:
We will assume the conditions stated are true and work to show unitarity:
$$\sum_i \bra {\psi_i} U \ket{\psi_n} \bra {\psi_i} U \ket{\psi_k} ^* = \delta_{hk}$$
If we take the conjugate we obtain the following:
$$\sum_i \bra{\psi_i} U \ket{\psi_n} \bra{\psi_k} U^\dagger \ket{\psi_i}$$
Now switching the order of the brakets we obtain the following:
$$\sum_i \bra{\psi_k} U^\dagger \ket{\psi_i} \bra{\psi_i} U \ket{\psi_n} = \bra{\psi_k} U^\dagger U \ket{\psi_n} = \delta_{kn}$$
If we set $n=k$ we obtain:
$$\bra{\psi_k}U^\dagger U \ket{\psi_k}=1 = \bra{\psi_k}\ket{\psi_k}$$
Therefore $$U^\dagger U = I$$
 which shows that $U$ is unitary.
And if we set $n\neq k$ we obtain:
$$\bra{\psi_k}U^\dagger U \ket{\psi_n} = 0$$



\end{solution}

\begin{problem}
Show that if $[A,[A, B]]=[B,[A, B]]=0$, then

$
e^{A+B}=e^{-1 / 2[A, B]} e^{A} e^{B}
$
\end{problem}
\begin{solution}\\
    We can start by defining a function of $\lambda$:
    $$f(\lambda) = e^{\lambda A}e^{\lambda B}$$
    Then take the derivative:
    $$\frac{df}{d\lambda} = Ae^{\lambda A}e^{\lambda B} + e^{\lambda A}Be^{\lambda B}$$
    We can now rewrite this expression into a more convenient form:
    $$\frac{d f}{d \lambda} = \left(A f\left(\lambda\right) + e^{\lambda A} B e^{-\lambda A}f(\lambda)\right) $$
    $$= \left(A + B + [A,B]\lambda\right)f(\lambda)$$
    We multiply by the inverse of $f$ on both sides:
    $$\frac{1}{f(\lambda)}\frac{df}{d\lambda} = A + B + [A,B]\lambda$$
    Since by assumption $[A,[A,B]]=[B,[A,B]]=0$, the commutator $[A,B]$ is constant. Therefore, we can integrate both sides:
    $$\ln|f(\lambda)|=\lambda(A+B)+\frac{1}{2}[A,B]\lambda^2$$
    $$f(\lambda)=e^{\lambda(A+B)+\frac{1}{2}[A,B]\lambda^2}$$
    $$e^{\lambda A}e^{\lambda B} = e^{\lambda(A+B)+\frac{1}{2}[A,B]\lambda^2}$$
    We can now set $\lambda=1$ to obtain the following:
    $$e^{A}e^{B} = e^{A+B+\frac{1}{2}[A,B]}$$
    We have that $[B,[A,B]]=[A,[A,B]]=0$, so the commutator $[A,B]$ is constant. Therefore, we can factorize the exponential and obtain:
    $$e^{A}e^{B} = e^{\frac{1}{2}[A,B]}e^{A+B}$$
    We can then take the inverse of the first exponential on the right hand side and obtain our desired answer:
    $$e^{A+B}=e^{-1/2[A,B]}e^{A}e^{B}$$
\end{solution}

\begin{problem}
Consider
$
Q=x \quad \text { and } \quad P=\frac{\hbar}{i} \frac{\mathrm{d}}{\mathrm{d} x}
$

Show that they satisfy the commutation relation

$
[P, Q]=\frac{\hbar}{i} I
$

where $I$ is unit operator.\\
Then

$
\operatorname{Tr}[P, Q]=\operatorname{Tr}(P Q)-\operatorname{Tr}(Q P)=0
$

but on the other hand

$
\operatorname{Tr}\left(\frac{\hbar}{i} I\right)=\frac{\hbar}{i} \operatorname{Tr}(I) \neq 0
$

Explain please!
\end{problem}
\begin{solution}\\
We can define the operators $Q$ and $P$ on the Hilbert space of square-integrable functions $L^2(\mathbb{R})$ as:
$$Q=\hat{x}=x$$
$$P=\hat{p}=-i\hbar\frac{\mathrm{d}}{\mathrm{d}x}$$
We can compute their commutator bracket to obtain the requested relation:
$$[P,Q]=\left[-i\hbar\frac{\mathrm{d}}{\mathrm{d}x},x\right]$$
$$= -i\hbar\frac{\mathrm{d}}{\mathrm{d}x}x-x(-i\hbar\frac{\mathrm{d}}{\mathrm{d}x})$$
$$= -i\hbar I = \frac{\hbar}{i}I$$

The apparent contradiction can be resolved by noting that the trace is taken over a finite dimensional basis, while $P$ and $Q$ are unbounded operators with continuous spectrum.
Specifically, while $[P,Q]=\frac{\hbar}{i}I$ as operators, the trace is defined as $\text{Tr}(A)=\sum_n\langle n|A|n\rangle$ where $\{|n\rangle\}$ is a basis of eigenvectors of a self-adjoint operator with discrete spectrum.
However, $P$ and $Q$ do not have a discrete spectrum, so their eigenvectors do not form a basis. As a result, their trace is not well-defined. On the other hand, the commutator $[P,Q]$ is still a well-defined operator, even though its trace is not.
\end{solution}

\begin{problem}
Define

$
\hat{A}=\hat{P} \hat{Q}^{3}+\hat{Q}^{3} \hat{P}
$

where $\hat{Q}$ and $\hat{P}$ are coordinate and momentum operators, respectively. Since $\hat{Q}$ and $\hat{P}$ are self-adjoint, so is $\hat{A}$

$
\hat{A}^{\dagger}=\left(\hat{P} \hat{Q}^{3}+\hat{Q}^{3} \hat{P}\right)^{\dagger}=\hat{Q}^{3} \hat{P}+\hat{P} \hat{Q}^{3}=\hat{A}
$

therefore the eigenvalues are real. Show that

$
f(x)=\left\{\begin{array}{lll}
1 / \sqrt{2}|x|^{-3 / 2} \exp \left(-1 /(2 x)^{2}\right) & \text { if } \quad x \neq 0 \\
0 & \text { if } \quad x=0
\end{array}\right.
$

is square integrable and

$
\hat{A} f=-i \hbar f
$

We have found that the eigenvalues of a self-adjoint operator is complex. Explain please!
\end{problem}
\begin{solution}\\ 
We can begin by showing that $f\left(x\right)$ is square integrable:
$$\int_{-\infty}^{\infty}|f(x)|^2dx=\int_{-\infty}^{\infty}\frac{1}{2|x|^3}\exp\left(-\frac{1}{2x^2}\right)dx$$
We can first simplify the integral by noting that the integrand is an even function, so we can extend the limits of integration to be symmetric about 0:
$$\int_{-\infty}^{\infty}|f(x)|^2dx = 2\int_{0}^{\infty}\frac{1}{2x^3}\exp\left(-\frac{1}{2x^2}\right)dx$$
We can make the $u$ substitution $u=\frac{1}{2x^2}$, so that $du = -\frac{1}{x^3}dx$.  This gives:
$$2\int_{0}^{\infty}\frac{1}{2x^3}\exp\left(-\frac{1}{2x^2}\right)dx = \int_{0}^{\infty}e^{-u}du = 1$$
Hence the function is square integrable.
To show that $\hat{A}f=-i\hbar f$, we compute:
$$Af = (\hat{P}\hat{Q}^3+\hat{Q}^3\hat{P})f$$
We then plug in for $f$
$$Af = -i\hbar x^3 \frac{df }{d x} -i\hbar \frac{d \left(x^3 f\right)}{d x}$$
$$=-i\hbar\left(x^3 \frac{d f}{d x} + 3x^2f + x^3 \frac{d f}{d x}\right)$$
$$=-i\hbar\left(2 x^3 \frac{d f}{d x} + 3x^2f\right)$$
We can now compute:
$$\frac{d f}{d x} = - \frac{3 \sqrt{2} e^{- \frac{1}{4 x^{2}}}}{4 x^{\frac{5}{2}}} + \frac{\sqrt{2} e^{- \frac{1}{4 x^{2}}}}{4 x^{\frac{9}{2}}}$$
$$=\frac{1}{2x^3}\left(1-3x^2\right)f$$
Plugging this in to the above expression we obtain:
$$Af=-i\hbar\left(2x^3\frac{1}{2x^3}\left(1-3x^2\right)f+3x^2f\right)$$$$=-i\hbar f$$
We will now show that for the eigenvalues of the $A$ operator to be real, the eigenfunction must take a specific form:
$$\hat Af = C f\, \text{ where }C\in \R$$
$$-i\hbar\left(3x^2 + 2x^3 \frac{d }{d x}\right)f = C f$$
$$-i\hbar\left(3x^2f + 2x^3\frac{df}{dx}\right) = Cf$$
We can rearrange this to obtain an ordinary differential equation for the eigenfunction $f$:
$$2i\hbar x^3\frac{df}{dx}+(C+3i\hbar x^2)f=0$$
This has the solution of the following form:
$$f{\left(x \right)} = \frac{C_{1} e^{- \frac{i C}{4 \hbar x^{2}}}}{x^{\frac{3}{2}}}$$
Where $C_{1}$ is a normalization constant.
We see that since $C$ is real, this function is complex, while the given function $f(x)$ is real. Therefore, the eigenvalues of the self-adjoint operator $\hat{A}$ cannot be real.
In other words, the operator is only Hermitian on the space of complex functions, not on real functions alone. 
\end{solution}

\end{document}