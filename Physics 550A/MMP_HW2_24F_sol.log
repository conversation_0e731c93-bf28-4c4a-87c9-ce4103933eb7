This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024/Arch Linux) (preloaded format=pdflatex 2024.8.31)  17 SEP 2024 00:16
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/home/<USER>/WorkSpace/Physics 550A/MMP_HW2_24F_sol.tex"
(/home/<USER>/WorkSpace/Physics 550A/MMP_HW2_24F_sol.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(/usr/share/texmf-dist/tex/latex/revtex/revtex4-2.cls
Document Class: revtex4-2 2022/06/05 4.2f (https://journals.aps.org/revtex/ for documentation)
 Copyright (c) 2019 American Physical Society.
 mailto:<EMAIL>
 Licensed under the LPPL:
http://www.ctan.org/tex-archive/macros/latex/base/lppl.txt
 <PERSON> <arthur_ogawa at sbcglobal dot net>
 Based on work by <PERSON> <david at dcarlisle.demon.co.uk>
 Version (4.2d--4.2f): Modified by <PERSON> and <PERSON><PERSON><PERSON>k
 .
ltxutil[2022/06/05 4.2f utilities package (portions licensed from W. E. Baxter web at superscript.com)]
LaTeX Info: Redefining \@centercr on input line 356.
Class revtex4-2 Info: Repairing broken LaTeX eqnarray on input line 417.
\eqncolsep=\skip48
Class revtex4-2 Info: Repairing hyperref-unfriendly LaTeX definition of \mpfootnote on input line 492.
Class revtex4-2 Info: Repairing broken LateX \@sect on input line 814.
\intertabularlinepenalty=\count188
\@tbpen=\count189
\@arstrutbox@hline=\box51
Class revtex4-2 Info: Repairing broken LaTeX \@xbitor on input line 1845.
Class revtex4-2 Info: Making \typeout \long on input line 2110.
ltxfront[2022/06/05 4.2f frontmatter package (AO,DPC,MD)]
\c@affil=\count190
\c@collab=\count191
\absbox=\box52
ltxgrid[2022/06/05 4.2f page grid package (portions licensed from W. E. Baxter web at superscript.com)]
\c@linecount=\count192
\output@latex=\toks17
\box@size@1=\box53
\box@size@2=\box54
\@protection@box=\box55
\@topmark@saved=\toks18
\footins@saved=\box56
\footins@recovered=\box57
\column@recovered=\box58
\pagesofar=\box59
\footsofar=\box60
\pagegrid@col=\count193
Class revtex4-2 Info: Overriding \@vspace, \@vspacer, \@no@pgbk, \@newline, and \\  on input line 5676.
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count266
\fbox@1=\box61
\c@table=\count267
\fbox@2=\box62
\c@video=\count268
\fbox@4=\box63
\@bibdataout=\write3
\widetext@top=\box64
\widetext@bot=\box65
Class revtex4-2 Info: Selecting society aps on input line 7669.
(/usr/share/texmf-dist/tex/latex/revtex/aps4-2.rtx
File: aps4-2 2022/06/05 4.2f (https://journals.aps.org/revtex/ for documentation)
Class revtex4-2 Info: RevTeX society APS selected on input line 40.
)
Class revtex4-2 Info: Selecting journal prb on input line 7671.
Class revtex4-2 Info: APS journal PRB selected on input line 7672.


Class revtex4-2 Warning: No type size specified, using default 10.

(/usr/share/texmf-dist/tex/latex/revtex/aps10pt4-2.rtx
File: aps10pt4-2 2022/06/05 4.2f (https://journals.aps.org/revtex/ for documentation)
Class revtex4-2 Info: RevTeX pointsize 10pt selected on input line 39.
) (/usr/share/texmf-dist/tex/latex/textcase/textcase.sty
Package: textcase 2023/07/27 v1.04 Text only upper/lower case changing (DPC)
Package textcase Info: Defining Legacy names on input line 20.
) (/usr/share/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/share/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks19
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen140
)
LaTeX Info: Redefining \text on input line 28.
) (/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen141
) (/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count269
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count270
\leftroot@=\count271
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count272
\DOTSCASE@=\count273
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box66
\strutbox@=\box67
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen142
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count274
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count275
\dotsspace@=\muskip16
\c@parentequation=\count276
\dspbrk@lvl=\count277
\tag@help=\toks21
\row@=\count278
\column@=\count279
\maxfields@=\count280
\andhelp@=\toks22
\eqnshift@=\dimen143
\alignsep@=\dimen144
\tagshift@=\dimen145
\tagwidth@=\dimen146
\totwidth@=\dimen147
\lineht@=\dimen148
\@envbody=\toks23
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/share/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (/usr/share/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip54
\bibsep=\skip55
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count281
)
Class revtex4-2 Info: Repairing natbib's \BibitemShut on input line 7676.
 (/usr/share/texmf-dist/tex/latex/revtex/revsymb4-2.sty
Package: revsymb4-2 2022/06/05 4.2f (https://journals.aps.org/revtex/ for documentation)
LaTeX Info: Redefining \frak on input line 104.
LaTeX Info: Redefining \Bbb on input line 120.
LaTeX Info: Redefining \REV@mathfrak on input line 128.
)) (/usr/share/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks25
) (/usr/share/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (/usr/share/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (/usr/share/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen149
\Gin@req@width=\dimen150
) (/usr/share/texmf-dist/tex/latex/graphics/color.sty
Package: color 2022/01/06 v1.3d Standard LaTeX Color (DPC)
 (/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.
 (/usr/share/texmf-dist/tex/latex/graphics/dvipsnam.def
File: dvipsnam.def 2016/06/17 v3.0m Driver-dependent file (DPC,SPQR)
) (/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx)) (/usr/share/texmf-dist/tex/latex/eepic/epic.sty
Enhancements to Picture Environment. Version 1.2 - Released June 1, 1986
\@@multicnt=\count282
\d@lta=\count283
\@delta=\dimen151
\@@delta=\dimen152
\@gridcnt=\count284
\@joinkind=\count285
\@dotgap=\dimen153
\@ddotgap=\dimen154
\@x@diff=\count286
\@y@diff=\count287
\x@diff=\dimen155
\y@diff=\dimen156
\@dotbox=\box68
\num@segments=\count288
\num@segmentsi=\count289
\@datafile=\read2
) (/usr/share/texmf-dist/tex/generic/epsf/epsf.sty
This is `epsf.tex' v2.7.4 <14 February 2011>
\epsffilein=\read3
\epsfframemargin=\dimen157
\epsfframethickness=\dimen158
\epsfrsize=\dimen159
\epsftmp=\dimen160
\epsftsize=\dimen161
\epsfxsize=\dimen162
\epsfysize=\dimen163
\pspoints=\dimen164
) (/usr/share/texmf-dist/tex/latex/tools/dcolumn.sty
Package: dcolumn 2023/07/08 v1.06 decimal alignment package (DPC)
 (/usr/share/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen165
\ar@mcellbox=\box69
\extrarowheight=\dimen166
\NC@list=\toks26
\extratabsurround=\skip56
\backup@length=\skip57
\ar@cellbox=\box70
)) (/usr/share/texmf-dist/tex/generic/ulem/ulem.sty
\UL@box=\box71
\UL@hyphenbox=\box72
\UL@skip=\skip58
\UL@hook=\toks27
\UL@height=\dimen167
\UL@pe=\count290
\UL@pixel=\dimen168
\ULC@box=\box73
Package: ulem 2019/11/18
\ULdepth=\dimen169
) (/usr/share/texmf-dist/tex/latex/physics/physics.sty
Package: physics 
 (/usr/share/texmf-dist/tex/latex/l3packages/xparse/xparse.sty (/usr/share/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (/usr/share/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count291
\l__pdf_internal_box=\box74
))
Package: xparse 2024-02-18 L3 Experimental document command parser
)) (/usr/share/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks28
\pgfutil@tempdima=\dimen170
\pgfutil@tempdimb=\dimen171
) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box75
) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/share/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/share/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks29
\pgfkeys@temptoks=\toks30
 (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks31
))
\pgf@x=\dimen172
\pgf@y=\dimen173
\pgf@xa=\dimen174
\pgf@ya=\dimen175
\pgf@xb=\dimen176
\pgf@yb=\dimen177
\pgf@xc=\dimen178
\pgf@yc=\dimen179
\pgf@xd=\dimen180
\pgf@yd=\dimen181
\w@pgf@writea=\write4
\r@pgf@reada=\read4
\c@pgf@counta=\count292
\c@pgf@countb=\count293
\c@pgf@countc=\count294
\c@pgf@countd=\count295
\t@pgf@toka=\toks32
\t@pgf@tokb=\toks33
\t@pgf@tokc=\toks34
\pgf@sys@id@count=\count296
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count297
\pgfsyssoftpath@bigbuffer@items=\count298
) (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
LaTeX Info: Redefining \color on input line 758.
 (/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen182
\pgfmath@count=\count299
\pgfmath@box=\box76
\pgfmath@toks=\toks35
\pgfmath@stack@operand=\toks36
\pgfmath@stack@operation=\toks37
) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count300
)) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen183
\pgf@picmaxx=\dimen184
\pgf@picminy=\dimen185
\pgf@picmaxy=\dimen186
\pgf@pathminx=\dimen187
\pgf@pathmaxx=\dimen188
\pgf@pathminy=\dimen189
\pgf@pathmaxy=\dimen190
\pgf@xx=\dimen191
\pgf@xy=\dimen192
\pgf@yx=\dimen193
\pgf@yy=\dimen194
\pgf@zx=\dimen195
\pgf@zy=\dimen196
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen197
\pgf@path@lasty=\dimen198
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen199
\pgf@shorten@start@additional=\dimen256
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box77
\pgf@hbox=\box78
\pgf@layerbox@main=\box79
\pgf@picture@serial@count=\count301
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen257
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen258
\pgf@pt@y=\dimen259
\pgf@pt@temp=\dimen260
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen261
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen262
\pgf@sys@shading@range@num=\count302
\pgf@shadingcount=\count303
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box80
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box81
) (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen263
\pgf@nodesepend=\dimen264
) (/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/usr/share/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen265
\pgffor@skip=\dimen266
\pgffor@stack=\toks38
\pgffor@toks=\toks39
)) (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count304
\pgfplotmarksize=\dimen267
)
\tikz@lastx=\dimen268
\tikz@lasty=\dimen269
\tikz@lastxsaved=\dimen270
\tikz@lastysaved=\dimen271
\tikz@lastmovetox=\dimen272
\tikz@lastmovetoy=\dimen273
\tikzleveldistance=\dimen274
\tikzsiblingdistance=\dimen275
\tikz@figbox=\box82
\tikz@figbox@bg=\box83
\tikz@tempbox=\box84
\tikz@tempbox@bg=\box85
\tikztreelevel=\count305
\tikznumberofchildren=\count306
\tikznumberofcurrentchild=\count307
\tikz@fig@count=\count308
 (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count309
\pgfmatrixcurrentcolumn=\count310
\pgf@matrix@numberofcolumns=\count311
)
\tikz@expandcount=\count312
 (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/tools/bm.sty
Package: bm 2023/07/08 v1.2f Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup6
\symboldletters=\mathgroup7
\symboldsymbols=\mathgroup8
Package bm Info: No bold for \OMX/cmex/m/n, using \pmb.
Package bm Info: No bold for \U/msa/m/n, using \pmb.
Package bm Info: No bold for \U/msb/m/n, using \pmb.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
LaTeX Info: Redefining \bm on input line 219.
) (/usr/share/texmf-dist/tex/latex/comment/comment.sty
\CommentStream=\write5
 Excluding comment 'comment') (/usr/share/texmf-dist/tex/latex/cancel/cancel.sty
Package: cancel 2013/04/12 v2.2 Cancel math terms
) (/usr/share/texmf-dist/tex/latex/oberdiek/stackrel.sty
Package: stackrel 2016/05/16 v1.3 Adding subscript option to stackrel (HO)
 (/usr/share/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
 (/usr/share/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)) (/usr/share/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/share/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (/usr/share/texmf-dist/tex/latex/braket/braket.sty) (/usr/share/texmf-dist/tex/latex/jknapltx/mathrsfs.sty
Package: mathrsfs 1996/01/01 Math RSFS package v1.0 (jk)
\symrsfs=\mathgroup9
)
Class revtex4-2 Info: Unrecognized array package. Please update this document class! (Proceeding with fingers crossed.) on input line 76.
Class revtex4-2 Info: cite was not loaded (OK!) on input line 76.
Class revtex4-2 Info: mcite was not loaded (OK!) on input line 76.
Class revtex4-2 Info: multicol was not loaded (OK!) on input line 76.


LaTeX Warning: Unused global option(s):
    [english].

(./MMP_HW2_24F_sol.aux

LaTeX Warning: Label `ham' multiply defined.


LaTeX Warning: Label `sumofsquares' multiply defined.


LaTeX Warning: Label `middleterms' multiply defined.


LaTeX Warning: Label `prior' multiply defined.

)
\openout1 = `MMP_HW2_24F_sol.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 76.
LaTeX Font Info:    ... okay on input line 76.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 76.
LaTeX Font Info:    ... okay on input line 76.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 76.
LaTeX Font Info:    ... okay on input line 76.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 76.
LaTeX Font Info:    ... okay on input line 76.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 76.
LaTeX Font Info:    ... okay on input line 76.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 76.
LaTeX Font Info:    ... okay on input line 76.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 76.
LaTeX Font Info:    ... okay on input line 76.
 (/usr/share/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count313
\scratchdimen=\dimen276
\scratchbox=\box86
\nofMPsegments=\count314
\nofMParguments=\count315
\everyMPshowfont=\toks40
\MPscratchCnt=\count316
\MPscratchDim=\dimen277
\MPnumerator=\count317
\makeMPintoPDFobject=\count318
\everyMPtoPDFconversion=\toks41
) (/usr/share/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/share/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
\openout3 = `MMP_HW2_24F_solNotes.bib'.



Class revtex4-2 Warning: Assuming \noaffiliation for authors.

LaTeX Font Info:    Trying to load font information for U+msa on input line 91.
(/usr/share/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 91.
 (/usr/share/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for U+rsfs on input line 91.
 (/usr/share/texmf-dist/tex/latex/jknapltx/ursfs.fd
File: ursfs.fd 1998/03/24 rsfs font definition file (jk)
) [1{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}] [2]
Underfull \hbox (badness 10000) in paragraph at lines 251--253

 []

[3] [4]
Underfull \hbox (badness 10000) in paragraph at lines 359--361

 []

[5] [6] [7] [8] (./MMP_HW2_24F_sol.bbl) [9] (./MMP_HW2_24F_sol.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********


LaTeX Warning: There were multiply-defined labels.

 ) 
Here is how much of TeX's memory you used:
 16920 strings out of 476076
 314420 string characters out of 5793775
 1983187 words of memory out of 5000000
 38686 multiletter control sequences out of 15000+600000
 572331 words of font info for 91 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 84i,12n,93p,1131b,552s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmex7.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmex9.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi5.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi6.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi9.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr5.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr6.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr9.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy5.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy6.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy9.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmti10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmti9.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/symbols/msbm10.pfb>
Output written on MMP_HW2_24F_sol.pdf (9 pages, 319949 bytes).
PDF statistics:
 162 PDF objects out of 1000 (max. 8388607)
 100 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 13 words of extra memory for PDF output out of 10000 (max. 10000000)

