{"cells": [{"cell_type": "code", "execution_count": 234, "metadata": {}, "outputs": [], "source": ["from sympy import *\n", "th, phi, = symbols('theta phi', real=True)\n", "f,g,h = symbols('f g h', cls=Function)"]}, {"cell_type": "code", "execution_count": 235, "metadata": {}, "outputs": [], "source": ["f = f(th,phi)\n", "g = g(th,phi)\n", "h = h(th,phi)"]}, {"cell_type": "code", "execution_count": 236, "metadata": {}, "outputs": [], "source": ["sx = Matrix([\n", "    [0, 1],\n", "    [1, 0]\n", "])\n", "\n", "sy = Matrix([\n", "    [0, -I],\n", "    [I, 0]\n", "])\n", "\n", "sz = Matrix([\n", "    [1, 0],\n", "    [0, -1]\n", "])"]}, {"cell_type": "code", "execution_count": 261, "metadata": {}, "outputs": [], "source": ["szp = f*sx + g*sy + h*sz\n", "szp\n", "szp = szp.subs(f,sin(th)*cos(phi)).subs(g,sin(th)*sin(phi)).subs(h,cos(th))\n", "szp = Rational(1,2)*szp.subs(-I*sin(phi)*sin(th)+ sin(th)*cos(phi), E**(-I*phi)*sin(th)).subs(I*sin(phi)*sin(th) + sin(th)*cos(phi), E**(I*phi)*sin(th))"]}, {"cell_type": "code", "execution_count": 282, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}- \\frac{\\sin{\\left(\\theta \\right)}}{e^{i \\phi} \\cos{\\left(\\theta \\right)} - e^{i \\phi}}\\\\1\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[-sin(theta)/(exp(I*phi)*cos(theta) - exp(I*phi))],\n", "[                                               1]])"]}, "execution_count": 282, "metadata": {}, "output_type": "execute_result"}], "source": ["list(szp.eigenvects())[1][2][0]"]}, {"cell_type": "code", "execution_count": 266, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}- \\frac{\\sin{\\left(\\theta \\right)}}{e^{i \\phi} \\cos{\\left(\\theta \\right)} - e^{i \\phi}}\\\\1\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[-sin(theta)/(exp(I*phi)*cos(theta) - exp(I*phi))],\n", "[                                               1]])"]}, "execution_count": 266, "metadata": {}, "output_type": "execute_result"}], "source": ["spinup = list(szp.eigenvects())[1][2][0]\n", "spinup"]}, {"cell_type": "code", "execution_count": 240, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}- \\frac{\\sin{\\left(\\theta \\right)}}{e^{i \\phi} \\cos{\\left(\\theta \\right)} + e^{i \\phi}}\\\\1\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[-sin(theta)/(exp(I*phi)*cos(theta) + exp(I*phi))],\n", "[                                               1]])"]}, "execution_count": 240, "metadata": {}, "output_type": "execute_result"}], "source": ["spindown = list(szp.eigenvects())[0][2][0]\n", "spindown"]}, {"cell_type": "code", "execution_count": 296, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\left|{\\sin{\\left(\\theta \\right)}}\\right|}{\\sqrt{\\cos^{2}{\\left(\\theta \\right)} - 2 \\cos{\\left(\\theta \\right)} + 1}}$"], "text/plain": ["Abs(sin(theta))/sqrt(cos(theta)**2 - 2*cos(theta) + 1)"]}, "execution_count": 296, "metadata": {}, "output_type": "execute_result"}], "source": ["abs((DotProduct(spinup.conjugate(),Matrix([1,0]))).doit())"]}, {"cell_type": "code", "execution_count": 241, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}e^{i \\alpha} \\cos{\\left(\\delta \\right)}\\\\e^{i \\beta} \\sin{\\left(\\delta \\right)}\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[exp(I*alpha)*cos(delta)],\n", "[ exp(I*beta)*sin(delta)]])"]}, "execution_count": 241, "metadata": {}, "output_type": "execute_result"}], "source": ["a,b,d = symbols('alpha beta delta', real=True)\n", "gspin = Matrix([E**(I*a)*cos(d), E**(I*b)*sin(d)])\n", "gspin"]}, {"cell_type": "code", "execution_count": 242, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[242], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43m(\u001b[49m\u001b[38;5;28;43mabs\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[43mDotProduct\u001b[49m\u001b[43m(\u001b[49m\u001b[43mspinup\u001b[49m\u001b[43m,\u001b[49m\u001b[43mszp\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mgspin\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdoit\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msimplify\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexpand\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msimplify\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexpand\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msimplify\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43mcos\u001b[49m\u001b[43m(\u001b[49m\u001b[43mth\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexpand\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msimplify\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msimplify\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mexpand()\u001b[38;5;241m.\u001b[39msimplify()\u001b[38;5;241m.\u001b[39mexpand()\u001b[38;5;241m.\u001b[39msimplify()\u001b[38;5;241m.\u001b[39mexpand()\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/basic.py:1853\u001b[0m, in \u001b[0;36mBasic.simplify\u001b[0;34m(self, **kwargs)\u001b[0m\n\u001b[1;32m   1851\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"See the simplify function in sympy.simplify\"\"\"\u001b[39;00m\n\u001b[1;32m   1852\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msympy\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msimplify\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msimplify\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m simplify\n\u001b[0;32m-> 1853\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msimplify\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/simplify.py:699\u001b[0m, in \u001b[0;36msimplify\u001b[0;34m(expr, ratio, measure, rational, inverse, doit, **kwargs)\u001b[0m\n\u001b[1;32m    696\u001b[0m     expr \u001b[38;5;241m=\u001b[39m besselsimp(expr)\n\u001b[1;32m    698\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mhas(TrigonometricFunction, HyperbolicFunction):\n\u001b[0;32m--> 699\u001b[0m     expr \u001b[38;5;241m=\u001b[39m \u001b[43mtrigsimp\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdeep\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    701\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m expr\u001b[38;5;241m.\u001b[39mhas(log):\n\u001b[1;32m    702\u001b[0m     expr \u001b[38;5;241m=\u001b[39m shorter(expand_log(expr, deep\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m), logcombine(expr))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/trigsimp.py:564\u001b[0m, in \u001b[0;36mtrigsimp\u001b[0;34m(expr, inverse, **opts)\u001b[0m\n\u001b[1;32m    553\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m trigsimp_gro<PERSON><PERSON>(new, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts)\n\u001b[1;32m    555\u001b[0m trigsimpfunc \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    556\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfu\u001b[39m\u001b[38;5;124m'\u001b[39m: (\u001b[38;5;28;01mlambda\u001b[39;00m x: fu(x, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts)),\n\u001b[1;32m    557\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmatching\u001b[39m\u001b[38;5;124m'\u001b[39m: (\u001b[38;5;28;01mlambda\u001b[39;00m x: futrig(x)),\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    561\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mold\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;28;01mlambda\u001b[39;00m x: trigsimp_old(x, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts),\n\u001b[1;32m    562\u001b[0m                }[method]\n\u001b[0;32m--> 564\u001b[0m expr_simplified \u001b[38;5;241m=\u001b[39m \u001b[43mtrigsimpfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    565\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m inverse:\n\u001b[1;32m    566\u001b[0m     expr_simplified \u001b[38;5;241m=\u001b[39m _trigsimp_inverse(expr_simplified)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/trigsimp.py:557\u001b[0m, in \u001b[0;36mtrigsimp.<locals>.<lambda>\u001b[0;34m(x)\u001b[0m\n\u001b[1;32m    552\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m new\n\u001b[1;32m    553\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m trigsimp_groebner(new, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts)\n\u001b[1;32m    555\u001b[0m trigsimpfunc \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    556\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfu\u001b[39m\u001b[38;5;124m'\u001b[39m: (\u001b[38;5;28;01mlambda\u001b[39;00m x: fu(x, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts)),\n\u001b[0;32m--> 557\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmatching\u001b[39m\u001b[38;5;124m'\u001b[39m: (\u001b[38;5;28;01mlambda\u001b[39;00m x: \u001b[43mfutrig\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m),\n\u001b[1;32m    558\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mgroebner\u001b[39m\u001b[38;5;124m'\u001b[39m: (\u001b[38;5;28;01mlambda\u001b[39;00m x: groebnersimp(x, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts)),\n\u001b[1;32m    559\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcombined\u001b[39m\u001b[38;5;124m'\u001b[39m: (\u001b[38;5;28;01mlambda\u001b[39;00m x: futrig(groebnersimp(x,\n\u001b[1;32m    560\u001b[0m                            polynomial\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m, hints\u001b[38;5;241m=\u001b[39m[\u001b[38;5;241m2\u001b[39m, tan]))),\n\u001b[1;32m    561\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mold\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;28;01mlambda\u001b[39;00m x: trigsimp_old(x, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mopts),\n\u001b[1;32m    562\u001b[0m                }[method]\n\u001b[1;32m    564\u001b[0m expr_simplified \u001b[38;5;241m=\u001b[39m trigsimpfunc(expr)\n\u001b[1;32m    565\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m inverse:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/trigsimp.py:1158\u001b[0m, in \u001b[0;36mfutrig\u001b[0;34m(e, hyper, **kwargs)\u001b[0m\n\u001b[1;32m   1155\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m e\n\u001b[1;32m   1157\u001b[0m old \u001b[38;5;241m=\u001b[39m e\n\u001b[0;32m-> 1158\u001b[0m e \u001b[38;5;241m=\u001b[39m \u001b[43mbottom_up\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_futrig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1160\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m hyper \u001b[38;5;129;01mand\u001b[39;00m e\u001b[38;5;241m.\u001b[39mhas(HyperbolicFunction):\n\u001b[1;32m   1161\u001b[0m     e, f \u001b[38;5;241m=\u001b[39m hyper_as_trig(e)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/traversal.py:236\u001b[0m, in \u001b[0;36mbottom_up\u001b[0;34m(rv, F, atoms, nonbasic)\u001b[0m\n\u001b[1;32m    234\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    235\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m args:\n\u001b[0;32m--> 236\u001b[0m         args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtuple\u001b[39m([\u001b[43mbottom_up\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mF\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43matoms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnonbasic\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m args])\n\u001b[1;32m    237\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;241m!=\u001b[39m rv\u001b[38;5;241m.\u001b[39margs:\n\u001b[1;32m    238\u001b[0m             rv \u001b[38;5;241m=\u001b[39m rv\u001b[38;5;241m.\u001b[39mfunc(\u001b[38;5;241m*\u001b[39margs)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/traversal.py:239\u001b[0m, in \u001b[0;36mbottom_up\u001b[0;34m(rv, F, atoms, nonbasic)\u001b[0m\n\u001b[1;32m    237\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m args \u001b[38;5;241m!=\u001b[39m rv\u001b[38;5;241m.\u001b[39margs:\n\u001b[1;32m    238\u001b[0m         rv \u001b[38;5;241m=\u001b[39m rv\u001b[38;5;241m.\u001b[39mfunc(\u001b[38;5;241m*\u001b[39margs)\n\u001b[0;32m--> 239\u001b[0m     rv \u001b[38;5;241m=\u001b[39m \u001b[43mF\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrv\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    240\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m atoms:\n\u001b[1;32m    241\u001b[0m     rv \u001b[38;5;241m=\u001b[39m F(rv)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/trigsimp.py:1225\u001b[0m, in \u001b[0;36m_futrig\u001b[0;34m(e)\u001b[0m\n\u001b[1;32m   1186\u001b[0m trigs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mlambda\u001b[39;00m x: x\u001b[38;5;241m.\u001b[39mhas(TrigonometricFunction)\n\u001b[1;32m   1188\u001b[0m tree \u001b[38;5;241m=\u001b[39m [identity,\n\u001b[1;32m   1189\u001b[0m     (\n\u001b[1;32m   1190\u001b[0m     TR3,  \u001b[38;5;66;03m# canonical angles\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1223\u001b[0m         factor_terms, TR12(x), trigs)],  \u001b[38;5;66;03m# expand tan of sum\u001b[39;00m\n\u001b[1;32m   1224\u001b[0m     )]\n\u001b[0;32m-> 1225\u001b[0m e \u001b[38;5;241m=\u001b[39m \u001b[43mgreedy\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtree\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mobjective\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mLops\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1227\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m coeff \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   1228\u001b[0m     e \u001b[38;5;241m=\u001b[39m coeff \u001b[38;5;241m*\u001b[39m e\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/strategies/core.py:150\u001b[0m, in \u001b[0;36mminimize.<locals>.minrule\u001b[0;34m(expr)\u001b[0m\n\u001b[1;32m    149\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mm<PERSON><PERSON><PERSON>\u001b[39m(expr: _S) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m _T:\n\u001b[0;32m--> 150\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mmin\u001b[39m([\u001b[43mrule\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m rule \u001b[38;5;129;01min\u001b[39;00m rules], key\u001b[38;5;241m=\u001b[39mobjective)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/strategies/core.py:64\u001b[0m, in \u001b[0;36mchain.<locals>.chain_rl\u001b[0;34m(expr)\u001b[0m\n\u001b[1;32m     62\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mchain_rl\u001b[39m(expr: _T) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m _T:\n\u001b[1;32m     63\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m rule \u001b[38;5;129;01min\u001b[39;00m rules:\n\u001b[0;32m---> 64\u001b[0m         expr \u001b[38;5;241m=\u001b[39m \u001b[43mrule\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     65\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m expr\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/trigsimp.py:1193\u001b[0m, in \u001b[0;36m_futrig.<locals>.<lambda>\u001b[0;34m(x)\u001b[0m\n\u001b[1;32m   1185\u001b[0m Lops \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mlambda\u001b[39;00m x: (L(x), x\u001b[38;5;241m.\u001b[39mcount_ops(), _nodes(x), \u001b[38;5;28mlen\u001b[39m(x\u001b[38;5;241m.\u001b[39margs), x\u001b[38;5;241m.\u001b[39mis_Add)\n\u001b[1;32m   1186\u001b[0m trigs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mlambda\u001b[39;00m x: x\u001b[38;5;241m.\u001b[39mhas(TrigonometricFunction)\n\u001b[1;32m   1188\u001b[0m tree \u001b[38;5;241m=\u001b[39m [identity,\n\u001b[1;32m   1189\u001b[0m     (\n\u001b[1;32m   1190\u001b[0m     TR3,  \u001b[38;5;66;03m# canonical angles\u001b[39;00m\n\u001b[1;32m   1191\u001b[0m     TR1,  \u001b[38;5;66;03m# sec-csc -> cos-sin\u001b[39;00m\n\u001b[1;32m   1192\u001b[0m     TR12,  \u001b[38;5;66;03m# expand tan of sum\u001b[39;00m\n\u001b[0;32m-> 1193\u001b[0m     \u001b[38;5;28;01mlambda\u001b[39;00m x: \u001b[43m_eapply\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfactor\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtrigs\u001b[49m\u001b[43m)\u001b[49m,\n\u001b[1;32m   1194\u001b[0m     TR2,  \u001b[38;5;66;03m# tan-cot -> sin-cos\u001b[39;00m\n\u001b[1;32m   1195\u001b[0m     [identity, \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(_mexpand, x, trigs)],\n\u001b[1;32m   1196\u001b[0m     TR2i,  \u001b[38;5;66;03m# sin-cos ratio -> tan\u001b[39;00m\n\u001b[1;32m   1197\u001b[0m     \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(\u001b[38;5;28;01mlambda\u001b[39;00m i: factor(i\u001b[38;5;241m.\u001b[39mnormal()), x, trigs),\n\u001b[1;32m   1198\u001b[0m     TR14,  \u001b[38;5;66;03m# factored identities\u001b[39;00m\n\u001b[1;32m   1199\u001b[0m     TR5,  \u001b[38;5;66;03m# sin-pow -> cos_pow\u001b[39;00m\n\u001b[1;32m   1200\u001b[0m     TR10,  \u001b[38;5;66;03m# sin-cos of sums -> sin-cos prod\u001b[39;00m\n\u001b[1;32m   1201\u001b[0m     TR11, _TR11, TR6, \u001b[38;5;66;03m# reduce double angles and rewrite cos pows\u001b[39;00m\n\u001b[1;32m   1202\u001b[0m     \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(factor, x, trigs),\n\u001b[1;32m   1203\u001b[0m     TR14,  \u001b[38;5;66;03m# factored powers of identities\u001b[39;00m\n\u001b[1;32m   1204\u001b[0m     [identity, \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(_mexpand, x, trigs)],\n\u001b[1;32m   1205\u001b[0m     TR10i,  \u001b[38;5;66;03m# sin-cos products > sin-cos of sums\u001b[39;00m\n\u001b[1;32m   1206\u001b[0m     TRmorrie,\n\u001b[1;32m   1207\u001b[0m     [identity, TR8],  \u001b[38;5;66;03m# sin-cos products -> sin-cos of sums\u001b[39;00m\n\u001b[1;32m   1208\u001b[0m     [identity, \u001b[38;5;28;01mlambda\u001b[39;00m x: TR2i(TR2(x))],  \u001b[38;5;66;03m# tan -> sin-cos -> tan\u001b[39;00m\n\u001b[1;32m   1209\u001b[0m     [\n\u001b[1;32m   1210\u001b[0m         \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(expand_mul, TR5(x), trigs),\n\u001b[1;32m   1211\u001b[0m         \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(\n\u001b[1;32m   1212\u001b[0m             expand_mul, TR15(x), trigs)], \u001b[38;5;66;03m# pos/neg powers of sin\u001b[39;00m\n\u001b[1;32m   1213\u001b[0m     [\n\u001b[1;32m   1214\u001b[0m         \u001b[38;5;28;01mlambda\u001b[39;00m x:  _eapply(expand_mul, TR6(x), trigs),\n\u001b[1;32m   1215\u001b[0m         \u001b[38;5;28;01mlambda\u001b[39;00m x:  _eapply(\n\u001b[1;32m   1216\u001b[0m             expand_mul, TR16(x), trigs)], \u001b[38;5;66;03m# pos/neg powers of cos\u001b[39;00m\n\u001b[1;32m   1217\u001b[0m     TR111,  \u001b[38;5;66;03m# tan, sin, cos to neg power -> cot, csc, sec\u001b[39;00m\n\u001b[1;32m   1218\u001b[0m     [identity, TR2i],  \u001b[38;5;66;03m# sin-cos ratio to tan\u001b[39;00m\n\u001b[1;32m   1219\u001b[0m     [identity, \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(\n\u001b[1;32m   1220\u001b[0m         expand_mul, TR22(x), trigs)],  \u001b[38;5;66;03m# tan-cot to sec-csc\u001b[39;00m\n\u001b[1;32m   1221\u001b[0m     TR1, TR2, TR2i,\n\u001b[1;32m   1222\u001b[0m     [identity, \u001b[38;5;28;01mlambda\u001b[39;00m x: _eapply(\n\u001b[1;32m   1223\u001b[0m         factor_terms, TR12(x), trigs)],  \u001b[38;5;66;03m# expand tan of sum\u001b[39;00m\n\u001b[1;32m   1224\u001b[0m     )]\n\u001b[1;32m   1225\u001b[0m e \u001b[38;5;241m=\u001b[39m greedy(tree, objective\u001b[38;5;241m=\u001b[39mLops)(e)\n\u001b[1;32m   1227\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m coeff \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/simplify/trigsimp.py:1249\u001b[0m, in \u001b[0;36m_eapply\u001b[0;34m(func, e, cond)\u001b[0m\n\u001b[1;32m   1247\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m e\n\u001b[1;32m   1248\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m _is_Expr(e) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m e\u001b[38;5;241m.\u001b[39margs:\n\u001b[0;32m-> 1249\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1250\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m e\u001b[38;5;241m.\u001b[39mfunc(\u001b[38;5;241m*\u001b[39m[\n\u001b[1;32m   1251\u001b[0m     _eapply(func, ei) \u001b[38;5;28;01mif\u001b[39;00m (cond \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mor\u001b[39;00m cond(ei)) \u001b[38;5;28;01melse\u001b[39;00m ei\n\u001b[1;32m   1252\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m ei \u001b[38;5;129;01min\u001b[39;00m e\u001b[38;5;241m.\u001b[39margs])\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/polytools.py:6542\u001b[0m, in \u001b[0;36mfactor\u001b[0;34m(f, deep, *gens, **args)\u001b[0m\n\u001b[1;32m   6539\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m f\u001b[38;5;241m.\u001b[39mxreplace(partials)\n\u001b[1;32m   6541\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 6542\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_generic_factor\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mgens\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mfactor\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m   6543\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m PolynomialError \u001b[38;5;28;01mas\u001b[39;00m msg:\n\u001b[1;32m   6544\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m f\u001b[38;5;241m.\u001b[39mis_commutative:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/polytools.py:6223\u001b[0m, in \u001b[0;36m_generic_factor\u001b[0;34m(expr, gens, args, method)\u001b[0m\n\u001b[1;32m   6221\u001b[0m opt \u001b[38;5;241m=\u001b[39m options\u001b[38;5;241m.\u001b[39mbuild_options(gens, args)\n\u001b[1;32m   6222\u001b[0m opt[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfraction\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m fraction\n\u001b[0;32m-> 6223\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_symbolic_factor\u001b[49m\u001b[43m(\u001b[49m\u001b[43msympify\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopt\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/polytools.py:6163\u001b[0m, in \u001b[0;36m_symbolic_factor\u001b[0;34m(expr, opt, method)\u001b[0m\n\u001b[1;32m   6161\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(expr,\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_eval_factor\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[1;32m   6162\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m expr\u001b[38;5;241m.\u001b[39m_eval_factor()\n\u001b[0;32m-> 6163\u001b[0m     coeff, factors \u001b[38;5;241m=\u001b[39m \u001b[43m_symbolic_factor_list\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtogether\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexpr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfraction\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mopt\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mfraction\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopt\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   6164\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m _keep_coeff(coeff, _factors_product(factors))\n\u001b[1;32m   6165\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(expr, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124margs\u001b[39m\u001b[38;5;124m'\u001b[39m):\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/polytools.py:6128\u001b[0m, in \u001b[0;36m_symbolic_factor_list\u001b[0;34m(expr, opt, method)\u001b[0m\n\u001b[1;32m   6125\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[1;32m   6126\u001b[0m     func \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(poly, method \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_list\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m-> 6128\u001b[0m     _coeff, _factors \u001b[38;5;241m=\u001b[39m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   6129\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m _coeff \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m S\u001b[38;5;241m.\u001b[39mOne:\n\u001b[1;32m   6130\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m exp\u001b[38;5;241m.\u001b[39mis_Integer:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/polytools.py:3350\u001b[0m, in \u001b[0;36mPoly.factor_list\u001b[0;34m(f)\u001b[0m\n\u001b[1;32m   3348\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(f\u001b[38;5;241m.\u001b[39mrep, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfactor_list\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[1;32m   3349\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 3350\u001b[0m         coeff, factors \u001b[38;5;241m=\u001b[39m \u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrep\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfactor_list\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3351\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m DomainError:\n\u001b[1;32m   3352\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m S\u001b[38;5;241m.\u001b[39mOne, [(f, \u001b[38;5;241m1\u001b[39m)]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/polyclasses.py:823\u001b[0m, in \u001b[0;36mDMP.factor_list\u001b[0;34m(f)\u001b[0m\n\u001b[1;32m    821\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mfactor_list\u001b[39m(f):\n\u001b[1;32m    822\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Returns a list of irreducible factors of ``f``. \"\"\"\u001b[39;00m\n\u001b[0;32m--> 823\u001b[0m     coeff, factors \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_factor_list\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlev\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdom\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    824\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m coeff, [ (f\u001b[38;5;241m.\u001b[39mper(g), k) \u001b[38;5;28;01mfor\u001b[39;00m g, k \u001b[38;5;129;01min\u001b[39;00m factors ]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/factortools.py:1423\u001b[0m, in \u001b[0;36mdmp_factor_list\u001b[0;34m(f, u, K0)\u001b[0m\n\u001b[1;32m   1421\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m K\u001b[38;5;241m.\u001b[39mis_ZZ:\n\u001b[1;32m   1422\u001b[0m     levels, f, v \u001b[38;5;241m=\u001b[39m dmp_exclude(f, u, K)\n\u001b[0;32m-> 1423\u001b[0m     coeff, factors \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_zz_factor\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1425\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m i, (f, k) \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(factors):\n\u001b[1;32m   1426\u001b[0m         factors[i] \u001b[38;5;241m=\u001b[39m (dmp_include(f, levels, v, K), k)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/factortools.py:1159\u001b[0m, in \u001b[0;36mdmp_zz_factor\u001b[0;34m(f, u, K)\u001b[0m\n\u001b[1;32m   1156\u001b[0m factors \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m   1158\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m dmp_degree(g, u) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m-> 1159\u001b[0m     g \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_sqf_part\u001b[49m\u001b[43m(\u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1160\u001b[0m     H \u001b[38;5;241m=\u001b[39m dmp_zz_wang(g, u, K)\n\u001b[1;32m   1161\u001b[0m     factors \u001b[38;5;241m=\u001b[39m dmp_trial_division(f, H, u, K)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/sqfreetools.py:256\u001b[0m, in \u001b[0;36mdmp_sqf_part\u001b[0;34m(f, u, K)\u001b[0m\n\u001b[1;32m    254\u001b[0m gcd \u001b[38;5;241m=\u001b[39m f\n\u001b[1;32m    255\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(u\u001b[38;5;241m+\u001b[39m\u001b[38;5;241m1\u001b[39m):\n\u001b[0;32m--> 256\u001b[0m     gcd \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mgcd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdmp_diff_in\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mi\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    257\u001b[0m sqf \u001b[38;5;241m=\u001b[39m dmp_quo(f, gcd, u, K)\n\u001b[1;32m    259\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m K\u001b[38;5;241m.\u001b[39mis_Field:\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1625\u001b[0m, in \u001b[0;36mdmp_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1608\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdmp_gcd\u001b[39m(f, g, u, K):\n\u001b[1;32m   1609\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1610\u001b[0m \u001b[38;5;124;03m    Computes polynomial GCD of `f` and `g` in `K[X]`.\u001b[39;00m\n\u001b[1;32m   1611\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1623\u001b[0m \n\u001b[1;32m   1624\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1625\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mdmp_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1584\u001b[0m, in \u001b[0;36mdmp_inner_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1581\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dup_inner_gcd(f, g, K)\n\u001b[1;32m   1583\u001b[0m J, (f, g) \u001b[38;5;241m=\u001b[39m dmp_multi_deflate((f, g), u, K)\n\u001b[0;32m-> 1584\u001b[0m h, cff, cfg \u001b[38;5;241m=\u001b[39m \u001b[43m_dmp_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1586\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m (dmp_inflate(h, J, u, K),\n\u001b[1;32m   1587\u001b[0m         dmp_inflate(cff, J, u, K),\n\u001b[1;32m   1588\u001b[0m         dmp_inflate(cfg, J, u, K))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1553\u001b[0m, in \u001b[0;36m_dmp_inner_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1551\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m K\u001b[38;5;241m.\u001b[39mis_ZZ \u001b[38;5;129;01mand\u001b[39;00m query(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mUSE_HEU_GCD\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[1;32m   1552\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1553\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdmp_zz_heu_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1554\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m HeuristicGCDFailed:\n\u001b[1;32m   1555\u001b[0m         \u001b[38;5;28;01mpass\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1311\u001b[0m, in \u001b[0;36mdmp_zz_heu_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1308\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m u:\n\u001b[1;32m   1309\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dup_zz_heu_gcd(f, g, K)\n\u001b[0;32m-> 1311\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43m_dmp_rr_trivial_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1313\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m result \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m   1314\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:912\u001b[0m, in \u001b[0;36m_dmp_rr_trivial_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    910\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dmp_one(u, K), f, g\n\u001b[1;32m    911\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m query(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mUSE_SIMPLIFY_GCD\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[0;32m--> 912\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_dmp_simplify_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    913\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    914\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:954\u001b[0m, in \u001b[0;36m_dmp_simplify_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    952\u001b[0m         G \u001b[38;5;241m=\u001b[39m dmp_content(g, u, K)\n\u001b[1;32m    953\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 954\u001b[0m         F \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_content\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    955\u001b[0m         G \u001b[38;5;241m=\u001b[39m dmp_LC(g, K)\n\u001b[1;32m    957\u001b[0m v \u001b[38;5;241m=\u001b[39m u \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1793\u001b[0m, in \u001b[0;36mdmp_content\u001b[0;34m(f, u, K)\u001b[0m\n\u001b[1;32m   1790\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m cont\n\u001b[1;32m   1792\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m c \u001b[38;5;129;01min\u001b[39;00m f[\u001b[38;5;241m1\u001b[39m:]:\n\u001b[0;32m-> 1793\u001b[0m     cont \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcont\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1795\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m dmp_one_p(cont, v, K):\n\u001b[1;32m   1796\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1625\u001b[0m, in \u001b[0;36mdmp_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1608\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdmp_gcd\u001b[39m(f, g, u, K):\n\u001b[1;32m   1609\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1610\u001b[0m \u001b[38;5;124;03m    Computes polynomial GCD of `f` and `g` in `K[X]`.\u001b[39;00m\n\u001b[1;32m   1611\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1623\u001b[0m \n\u001b[1;32m   1624\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1625\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mdmp_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1584\u001b[0m, in \u001b[0;36mdmp_inner_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1581\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dup_inner_gcd(f, g, K)\n\u001b[1;32m   1583\u001b[0m J, (f, g) \u001b[38;5;241m=\u001b[39m dmp_multi_deflate((f, g), u, K)\n\u001b[0;32m-> 1584\u001b[0m h, cff, cfg \u001b[38;5;241m=\u001b[39m \u001b[43m_dmp_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1586\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m (dmp_inflate(h, J, u, K),\n\u001b[1;32m   1587\u001b[0m         dmp_inflate(cff, J, u, K),\n\u001b[1;32m   1588\u001b[0m         dmp_inflate(cfg, J, u, K))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1553\u001b[0m, in \u001b[0;36m_dmp_inner_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1551\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m K\u001b[38;5;241m.\u001b[39mis_ZZ \u001b[38;5;129;01mand\u001b[39;00m query(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mUSE_HEU_GCD\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[1;32m   1552\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1553\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdmp_zz_heu_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1554\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m HeuristicGCDFailed:\n\u001b[1;32m   1555\u001b[0m         \u001b[38;5;28;01mpass\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1311\u001b[0m, in \u001b[0;36mdmp_zz_heu_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1308\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m u:\n\u001b[1;32m   1309\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dup_zz_heu_gcd(f, g, K)\n\u001b[0;32m-> 1311\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43m_dmp_rr_trivial_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1313\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m result \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m   1314\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:912\u001b[0m, in \u001b[0;36m_dmp_rr_trivial_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    910\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dmp_one(u, K), f, g\n\u001b[1;32m    911\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m query(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mUSE_SIMPLIFY_GCD\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[0;32m--> 912\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_dmp_simplify_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    913\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    914\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:952\u001b[0m, in \u001b[0;36m_dmp_simplify_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    950\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m df:\n\u001b[1;32m    951\u001b[0m     F \u001b[38;5;241m=\u001b[39m dmp_LC(f, K)\n\u001b[0;32m--> 952\u001b[0m     G \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_content\u001b[49m\u001b[43m(\u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    953\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    954\u001b[0m     F \u001b[38;5;241m=\u001b[39m dmp_content(f, u, K)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1793\u001b[0m, in \u001b[0;36mdmp_content\u001b[0;34m(f, u, K)\u001b[0m\n\u001b[1;32m   1790\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m cont\n\u001b[1;32m   1792\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m c \u001b[38;5;129;01min\u001b[39;00m f[\u001b[38;5;241m1\u001b[39m:]:\n\u001b[0;32m-> 1793\u001b[0m     cont \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcont\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1795\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m dmp_one_p(cont, v, K):\n\u001b[1;32m   1796\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1625\u001b[0m, in \u001b[0;36mdmp_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1608\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdmp_gcd\u001b[39m(f, g, u, K):\n\u001b[1;32m   1609\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1610\u001b[0m \u001b[38;5;124;03m    Computes polynomial GCD of `f` and `g` in `K[X]`.\u001b[39;00m\n\u001b[1;32m   1611\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1623\u001b[0m \n\u001b[1;32m   1624\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1625\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mdmp_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1584\u001b[0m, in \u001b[0;36mdmp_inner_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1581\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dup_inner_gcd(f, g, K)\n\u001b[1;32m   1583\u001b[0m J, (f, g) \u001b[38;5;241m=\u001b[39m dmp_multi_deflate((f, g), u, K)\n\u001b[0;32m-> 1584\u001b[0m h, cff, cfg \u001b[38;5;241m=\u001b[39m \u001b[43m_dmp_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1586\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m (dmp_inflate(h, J, u, K),\n\u001b[1;32m   1587\u001b[0m         dmp_inflate(cff, J, u, K),\n\u001b[1;32m   1588\u001b[0m         dmp_inflate(cfg, J, u, K))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1553\u001b[0m, in \u001b[0;36m_dmp_inner_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1551\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m K\u001b[38;5;241m.\u001b[39mis_ZZ \u001b[38;5;129;01mand\u001b[39;00m query(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mUSE_HEU_GCD\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[1;32m   1552\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1553\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdmp_zz_heu_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1554\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m HeuristicGCDFailed:\n\u001b[1;32m   1555\u001b[0m         \u001b[38;5;28;01mpass\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1311\u001b[0m, in \u001b[0;36mdmp_zz_heu_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1308\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m u:\n\u001b[1;32m   1309\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dup_zz_heu_gcd(f, g, K)\n\u001b[0;32m-> 1311\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43m_dmp_rr_trivial_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1313\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m result \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m   1314\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:912\u001b[0m, in \u001b[0;36m_dmp_rr_trivial_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    910\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dmp_one(u, K), f, g\n\u001b[1;32m    911\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m query(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mUSE_SIMPLIFY_GCD\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[0;32m--> 912\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_dmp_simplify_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    913\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    914\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:952\u001b[0m, in \u001b[0;36m_dmp_simplify_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    950\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m df:\n\u001b[1;32m    951\u001b[0m     F \u001b[38;5;241m=\u001b[39m dmp_LC(f, K)\n\u001b[0;32m--> 952\u001b[0m     G \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_content\u001b[49m\u001b[43m(\u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    953\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    954\u001b[0m     F \u001b[38;5;241m=\u001b[39m dmp_content(f, u, K)\n", "    \u001b[0;31m[... skipping similar frames: _dmp_inner_gcd at line 1553 (2 times), _dmp_rr_trivial_gcd at line 912 (2 times), dmp_content at line 1793 (2 times), dmp_gcd at line 1625 (2 times), dmp_inner_gcd at line 1584 (2 times), dmp_zz_heu_gcd at line 1311 (2 times), _dmp_simplify_gcd at line 952 (1 times)]\u001b[0m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:952\u001b[0m, in \u001b[0;36m_dmp_simplify_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    950\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m df:\n\u001b[1;32m    951\u001b[0m     F \u001b[38;5;241m=\u001b[39m dmp_LC(f, K)\n\u001b[0;32m--> 952\u001b[0m     G \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_content\u001b[49m\u001b[43m(\u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    953\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    954\u001b[0m     F \u001b[38;5;241m=\u001b[39m dmp_content(f, u, K)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1793\u001b[0m, in \u001b[0;36mdmp_content\u001b[0;34m(f, u, K)\u001b[0m\n\u001b[1;32m   1790\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m cont\n\u001b[1;32m   1792\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m c \u001b[38;5;129;01min\u001b[39;00m f[\u001b[38;5;241m1\u001b[39m:]:\n\u001b[0;32m-> 1793\u001b[0m     cont \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcont\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1795\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m dmp_one_p(cont, v, K):\n\u001b[1;32m   1796\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1625\u001b[0m, in \u001b[0;36mdmp_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1608\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdmp_gcd\u001b[39m(f, g, u, K):\n\u001b[1;32m   1609\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1610\u001b[0m \u001b[38;5;124;03m    Computes polynomial GCD of `f` and `g` in `K[X]`.\u001b[39;00m\n\u001b[1;32m   1611\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1623\u001b[0m \n\u001b[1;32m   1624\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1625\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mdmp_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1584\u001b[0m, in \u001b[0;36mdmp_inner_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1581\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dup_inner_gcd(f, g, K)\n\u001b[1;32m   1583\u001b[0m J, (f, g) \u001b[38;5;241m=\u001b[39m dmp_multi_deflate((f, g), u, K)\n\u001b[0;32m-> 1584\u001b[0m h, cff, cfg \u001b[38;5;241m=\u001b[39m \u001b[43m_dmp_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1586\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m (dmp_inflate(h, J, u, K),\n\u001b[1;32m   1587\u001b[0m         dmp_inflate(cff, J, u, K),\n\u001b[1;32m   1588\u001b[0m         dmp_inflate(cfg, J, u, K))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1553\u001b[0m, in \u001b[0;36m_dmp_inner_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1551\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m K\u001b[38;5;241m.\u001b[39mis_ZZ \u001b[38;5;129;01mand\u001b[39;00m query(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mUSE_HEU_GCD\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[1;32m   1552\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1553\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdmp_zz_heu_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1554\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m HeuristicGCDFailed:\n\u001b[1;32m   1555\u001b[0m         \u001b[38;5;28;01mpass\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1311\u001b[0m, in \u001b[0;36mdmp_zz_heu_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1308\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m u:\n\u001b[1;32m   1309\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dup_zz_heu_gcd(f, g, K)\n\u001b[0;32m-> 1311\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43m_dmp_rr_trivial_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1313\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m result \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m   1314\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:912\u001b[0m, in \u001b[0;36m_dmp_rr_trivial_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    910\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dmp_one(u, K), f, g\n\u001b[1;32m    911\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m query(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mUSE_SIMPLIFY_GCD\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[0;32m--> 912\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_dmp_simplify_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    913\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    914\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:961\u001b[0m, in \u001b[0;36m_dmp_simplify_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    958\u001b[0m h \u001b[38;5;241m=\u001b[39m dmp_gcd(F, G, v, K)\n\u001b[1;32m    960\u001b[0m cff \u001b[38;5;241m=\u001b[39m [ dmp_quo(cf, h, v, K) \u001b[38;5;28;01mfor\u001b[39;00m cf \u001b[38;5;129;01min\u001b[39;00m f ]\n\u001b[0;32m--> 961\u001b[0m cfg \u001b[38;5;241m=\u001b[39m [ \u001b[43mdmp_quo\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mh\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m cg \u001b[38;5;129;01min\u001b[39;00m g ]\n\u001b[1;32m    963\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m [h], cff, cfg\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:1671\u001b[0m, in \u001b[0;36mdmp_quo\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1653\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdmp_quo\u001b[39m(f, g, u, K):\n\u001b[1;32m   1654\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1655\u001b[0m \u001b[38;5;124;03m    Returns exact polynomial quotient in ``K[X]``.\u001b[39;00m\n\u001b[1;32m   1656\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1669\u001b[0m \n\u001b[1;32m   1670\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1671\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdmp_div\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:1629\u001b[0m, in \u001b[0;36mdmp_div\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1627\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dmp_ff_div(f, g, u, K)\n\u001b[1;32m   1628\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1629\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdmp_rr_div\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:1389\u001b[0m, in \u001b[0;36mdmp_rr_div\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1387\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m   1388\u001b[0m     lc_r \u001b[38;5;241m=\u001b[39m dmp_LC(r, K)\n\u001b[0;32m-> 1389\u001b[0m     c, R \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_rr_div\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlc_r\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlc_g\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1391\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m dmp_zero_p(R, v):\n\u001b[1;32m   1392\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:1389\u001b[0m, in \u001b[0;36mdmp_rr_div\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1387\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m   1388\u001b[0m     lc_r \u001b[38;5;241m=\u001b[39m dmp_LC(r, K)\n\u001b[0;32m-> 1389\u001b[0m     c, R \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_rr_div\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlc_r\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlc_g\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1391\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m dmp_zero_p(R, v):\n\u001b[1;32m   1392\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n", "    \u001b[0;31m[... skipping similar frames: dmp_rr_div at line 1389 (4 times)]\u001b[0m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:1389\u001b[0m, in \u001b[0;36mdmp_rr_div\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1387\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m   1388\u001b[0m     lc_r \u001b[38;5;241m=\u001b[39m dmp_LC(r, K)\n\u001b[0;32m-> 1389\u001b[0m     c, R \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_rr_div\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlc_r\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlc_g\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1391\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m dmp_zero_p(R, v):\n\u001b[1;32m   1392\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:1397\u001b[0m, in \u001b[0;36mdmp_rr_div\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1394\u001b[0m j \u001b[38;5;241m=\u001b[39m dr \u001b[38;5;241m-\u001b[39m dg\n\u001b[1;32m   1396\u001b[0m q \u001b[38;5;241m=\u001b[39m dmp_add_term(q, c, j, u, K)\n\u001b[0;32m-> 1397\u001b[0m h \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_mul_term\u001b[49m\u001b[43m(\u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mj\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1398\u001b[0m r \u001b[38;5;241m=\u001b[39m dmp_sub(r, h, u, K)\n\u001b[1;32m   1400\u001b[0m _dr, dr \u001b[38;5;241m=\u001b[39m dr, dmp_degree(r, u)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:184\u001b[0m, in \u001b[0;36mdmp_mul_term\u001b[0;34m(f, c, i, u, K)\u001b[0m\n\u001b[1;32m    182\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dmp_zero(u)\n\u001b[1;32m    183\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 184\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m [ \u001b[43mdmp_mul\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m cf \u001b[38;5;129;01min\u001b[39;00m f ] \u001b[38;5;241m+\u001b[39m dmp_zeros(i, v, K)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:828\u001b[0m, in \u001b[0;36mdmp_mul\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    825\u001b[0m     coeff \u001b[38;5;241m=\u001b[39m dmp_zero(v)\n\u001b[1;32m    827\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m j \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mmax\u001b[39m(\u001b[38;5;241m0\u001b[39m, i \u001b[38;5;241m-\u001b[39m dg), \u001b[38;5;28mmin\u001b[39m(df, i) \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m):\n\u001b[0;32m--> 828\u001b[0m         coeff \u001b[38;5;241m=\u001b[39m dmp_add(coeff, \u001b[43mdmp_mul\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m[\u001b[49m\u001b[43mj\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mj\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m, v, K)\n\u001b[1;32m    830\u001b[0m     h\u001b[38;5;241m.\u001b[39mappend(coeff)\n\u001b[1;32m    832\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m dmp_strip(h, u)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:828\u001b[0m, in \u001b[0;36mdmp_mul\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    825\u001b[0m     coeff \u001b[38;5;241m=\u001b[39m dmp_zero(v)\n\u001b[1;32m    827\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m j \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mmax\u001b[39m(\u001b[38;5;241m0\u001b[39m, i \u001b[38;5;241m-\u001b[39m dg), \u001b[38;5;28mmin\u001b[39m(df, i) \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m):\n\u001b[0;32m--> 828\u001b[0m         coeff \u001b[38;5;241m=\u001b[39m dmp_add(coeff, \u001b[43mdmp_mul\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m[\u001b[49m\u001b[43mj\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mj\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m, v, K)\n\u001b[1;32m    830\u001b[0m     h\u001b[38;5;241m.\u001b[39mappend(coeff)\n\u001b[1;32m    832\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m dmp_strip(h, u)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:828\u001b[0m, in \u001b[0;36mdmp_mul\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    825\u001b[0m     coeff \u001b[38;5;241m=\u001b[39m dmp_zero(v)\n\u001b[1;32m    827\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m j \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mmax\u001b[39m(\u001b[38;5;241m0\u001b[39m, i \u001b[38;5;241m-\u001b[39m dg), \u001b[38;5;28mmin\u001b[39m(df, i) \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m):\n\u001b[0;32m--> 828\u001b[0m         coeff \u001b[38;5;241m=\u001b[39m dmp_add(coeff, \u001b[43mdmp_mul\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m[\u001b[49m\u001b[43mj\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mj\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m, v, K)\n\u001b[1;32m    830\u001b[0m     h\u001b[38;5;241m.\u001b[39mappend(coeff)\n\u001b[1;32m    832\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m dmp_strip(h, u)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:819\u001b[0m, in \u001b[0;36mdmp_mul\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    815\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m f\n\u001b[1;32m    817\u001b[0m dg \u001b[38;5;241m=\u001b[39m dmp_degree(g, u)\n\u001b[0;32m--> 819\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mdg\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m<\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m:\n\u001b[1;32m    820\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m g\n\u001b[1;32m    822\u001b[0m h, v \u001b[38;5;241m=\u001b[39m [], u \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/decorators.py:231\u001b[0m, in \u001b[0;36m_SympifyWrapper.make_wrapped.<locals>._func\u001b[0;34m(self, other)\u001b[0m\n\u001b[1;32m    229\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(other, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_op_priority\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[1;32m    230\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 231\u001b[0m         other \u001b[38;5;241m=\u001b[39m \u001b[43msympify\u001b[49m\u001b[43m(\u001b[49m\u001b[43mother\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstrict\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    232\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m SympifyError:\n\u001b[1;32m    233\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m retval\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/sympify.py:373\u001b[0m, in \u001b[0;36msympify\u001b[0;34m(a, locals, convert_xor, strict, rational, evaluate)\u001b[0m\n\u001b[1;32m    370\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(a, CantSympify):\n\u001b[1;32m    371\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m SympifyError(a)\n\u001b[0;32m--> 373\u001b[0m \u001b[38;5;28mcls\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m__class__\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON>\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    375\u001b[0m \u001b[38;5;66;03m#Check if there exists a converter for any of the types in the mro\u001b[39;00m\n\u001b[1;32m    376\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m superclass \u001b[38;5;129;01min\u001b[39;00m getmro(\u001b[38;5;28mcls\u001b[39m):\n\u001b[1;32m    377\u001b[0m     \u001b[38;5;66;03m#First check for user defined converters\u001b[39;00m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["(abs(((DotProduct(spinup,szp*gspin).doit().simplify()).expand().simplify().expand().simplify()*(-1+cos(th)).expand().simplify()))**2).simplify().expand().simplify().expand().simplify().expand()"]}, {"cell_type": "code", "execution_count": 243, "metadata": {}, "outputs": [], "source": ["(abs((spinup.transpose().conjugate()*szp*gspin)[0].subs(phi,0).expand().simplify())**2).simplify()\n", "lambdified_function = lambdify((th, phi, a, b, d), (abs((spinup.transpose().conjugate()*szp*gspin)[0].subs(phi,0).expand().simplify())**2).simplify())"]}, {"cell_type": "code", "execution_count": 248, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sympy import symbols, <PERSON>, I, exp, cos, sin, pi, lambdify\n", "\n", "# Define symbols\n", "\n", "alpha = pi/6\n", "beta = pi\n", "delta = pi/6\n", "\n", "# Define matrices for spin calculations\n", "spinup = Matrix([[cos(th/2)], [sin(th/2)*exp(I*phi)]])\n", "gspin = Matrix([[cos(delta), -sin(delta)*exp(-I*beta)], [sin(delta)*exp(I*beta), cos(delta)]])\n", "\n", "# Calculate Z using sympy\n", "Z_sympy = (abs((spinup.transpose().conjugate()*szp*gspin)[0].expand().simplify())**2).simplify()\n", "\n", "# Lambdify the function for numerical evaluation\n", "Z_func = lambdify((th, phi), Z_sympy, modules='numpy')\n", "\n", "# Create a grid of theta and phi values\n", "theta_vals = np.linspace(0, 2 * np.pi, 100)\n", "phi_vals = np.linspace(0, 2 * np.pi, 100)\n", "Theta, Phi = np.meshgrid(theta_vals, phi_vals)\n", "\n", "# Evaluate Z over the grid using the lambdified function\n", "Z = Z_func(The<PERSON>, Phi)\n", "\n", "# Plotting\n", "plt.figure(figsize=(8, 6))\n", "contour = plt.contourf(Theta, Phi, np.abs(Z), levels=25, cmap='viridis')\n", "plt.colorbar(contour)\n", "plt.title('2D Solution Space of Theta and Phi')\n", "plt.xlabel('<PERSON><PERSON>')\n", "plt.ylabel('Phi')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 249, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}\\cos{\\left(\\theta \\right)} & e^{- i \\phi} \\sin{\\left(\\theta \\right)}\\\\e^{i \\phi} \\sin{\\left(\\theta \\right)} & - \\cos{\\left(\\theta \\right)}\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[           cos(theta), exp(-I*phi)*sin(theta)],\n", "[exp(I*phi)*sin(theta),            -cos(theta)]])"]}, "execution_count": 249, "metadata": {}, "output_type": "execute_result"}], "source": ["szp"]}, {"cell_type": "code", "execution_count": 300, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}0 & 0 & 0\\\\0 & -1 & 0\\\\0 & 0 & 0\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[0,  0, 0],\n", "[0, -1, 0],\n", "[0,  0, 0]])"]}, "execution_count": 300, "metadata": {}, "output_type": "execute_result"}], "source": ["diagonalize_vector(Matrix([[0,-1,0],[1,0,-1],[0,1,0]]))"]}, {"cell_type": "code", "execution_count": 313, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}0 & 0 & 0\\\\0 & - i \\beta & 0\\\\0 & 0 & i \\beta\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[0,       0,      0],\n", "[0, -I*beta,      0],\n", "[0,       0, I*beta]])"]}, "execution_count": 313, "metadata": {}, "output_type": "execute_result"}], "source": ["list((b/sqrt(2)*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize())[1]"]}, {"cell_type": "code", "execution_count": 321, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}1 & -1 & -1\\\\0 & - \\sqrt{2} i & \\sqrt{2} i\\\\1 & 1 & 1\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[1,         -1,        -1],\n", "[0, -sqrt(2)*I, sqrt(2)*I],\n", "[1,          1,         1]])"]}, "execution_count": 321, "metadata": {}, "output_type": "execute_result"}], "source": ["((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0]"]}, {"cell_type": "code", "execution_count": 331, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}0 & 0 & 0\\\\0 & - i \\beta & 0\\\\0 & 0 & i \\beta\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[0,       0,      0],\n", "[0, -I*beta,      0],\n", "[0,       0, I*beta]])"]}, "execution_count": 331, "metadata": {}, "output_type": "execute_result"}], "source": ["Inverse(((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0])*(b/sqrt(2)*Matrix([[0,-1,0],[1,0,-1],[0,1,0]]))*((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0]"]}, {"cell_type": "code", "execution_count": 330, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}0 & - \\frac{\\sqrt{2} \\beta}{2} & 0\\\\\\frac{\\sqrt{2} \\beta}{2} & 0 & - \\frac{\\sqrt{2} \\beta}{2}\\\\0 & \\frac{\\sqrt{2} \\beta}{2} & 0\\end{matrix}\\right]^{-1}$"], "text/plain": ["Matrix([\n", "[             0, -sqrt(2)*beta/2,               0],\n", "[sqrt(2)*beta/2,               0, -sqrt(2)*beta/2],\n", "[             0,  sqrt(2)*beta/2,               0]])**(-1)"]}, "execution_count": 330, "metadata": {}, "output_type": "execute_result"}], "source": ["Inverse(((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])))"]}, {"cell_type": "code", "execution_count": 333, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}\\frac{1}{2} & 0 & \\frac{1}{2}\\\\- \\frac{1}{4} & \\frac{\\sqrt{2} i}{4} & \\frac{1}{4}\\\\- \\frac{1}{4} & - \\frac{\\sqrt{2} i}{4} & \\frac{1}{4}\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[ 1/2,            0, 1/2],\n", "[-1/4,  sqrt(2)*I/4, 1/4],\n", "[-1/4, -sqrt(2)*I/4, 1/4]])"]}, "execution_count": 333, "metadata": {}, "output_type": "execute_result"}], "source": ["Inverse(((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0]).doit()"]}, {"cell_type": "code", "execution_count": 334, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}1 & -1 & -1\\\\0 & - \\sqrt{2} i & \\sqrt{2} i\\\\1 & 1 & 1\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[1,         -1,        -1],\n", "[0, -sqrt(2)*I, sqrt(2)*I],\n", "[1,          1,         1]])"]}, "execution_count": 334, "metadata": {}, "output_type": "execute_result"}], "source": ["((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0]"]}, {"cell_type": "code", "execution_count": 335, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}0 & - \\frac{\\sqrt{2} \\beta}{2} & 0\\\\\\frac{\\sqrt{2} \\beta}{2} & 0 & - \\frac{\\sqrt{2} \\beta}{2}\\\\0 & \\frac{\\sqrt{2} \\beta}{2} & 0\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[             0, -sqrt(2)*beta/2,               0],\n", "[sqrt(2)*beta/2,               0, -sqrt(2)*beta/2],\n", "[             0,  sqrt(2)*beta/2,               0]])"]}, "execution_count": 335, "metadata": {}, "output_type": "execute_result"}], "source": ["(b/sqrt(2)*Matrix([[0,-1,0],[1,0,-1],[0,1,0]]))"]}, {"cell_type": "code", "execution_count": 339, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}1 & 0 & 0\\\\0 & e^{- i \\beta} & 0\\\\0 & 0 & e^{i \\beta}\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[1,            0,           0],\n", "[0, exp(-I*beta),           0],\n", "[0,            0, exp(I*beta)]])"]}, "execution_count": 339, "metadata": {}, "output_type": "execute_result"}], "source": ["exp(Inverse(((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0])*(b/sqrt(2)*Matrix([[0,-1,0],[1,0,-1],[0,1,0]]))*((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0]).simplify()"]}, {"cell_type": "code", "execution_count": 340, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}0 & - \\frac{\\sqrt{2} \\beta}{2} & 0\\\\\\frac{\\sqrt{2} \\beta}{2} & 0 & - \\frac{\\sqrt{2} \\beta}{2}\\\\0 & \\frac{\\sqrt{2} \\beta}{2} & 0\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[             0, -sqrt(2)*beta/2,               0],\n", "[sqrt(2)*beta/2,               0, -sqrt(2)*beta/2],\n", "[             0,  sqrt(2)*beta/2,               0]])"]}, "execution_count": 340, "metadata": {}, "output_type": "execute_result"}], "source": ["(((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0])*((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[1]*Inverse(((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0])"]}, {"cell_type": "code", "execution_count": 355, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}1 & 0 & 0\\\\0 & e^{- i \\beta} & 0\\\\0 & 0 & e^{i \\beta}\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[1,            0,           0],\n", "[0, exp(-I*beta),           0],\n", "[0,            0, exp(I*beta)]])"]}, "execution_count": 355, "metadata": {}, "output_type": "execute_result"}], "source": ["exp((((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[1]).doit()).subs(E**(I*b), cos(b) + I*sin(b)).simplify()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0]).doit()*"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}\\frac{\\cos{\\left(\\beta \\right)}}{2} + \\frac{1}{2} & - \\frac{\\sqrt{2} \\sin{\\left(\\beta \\right)}}{2} & \\frac{1}{2} - \\frac{\\cos{\\left(\\beta \\right)}}{2}\\\\\\frac{\\sqrt{2} \\sin{\\left(\\beta \\right)}}{2} & \\cos{\\left(\\beta \\right)} & - \\frac{\\sqrt{2} \\sin{\\left(\\beta \\right)}}{2}\\\\\\frac{1}{2} - \\frac{\\cos{\\left(\\beta \\right)}}{2} & \\frac{\\sqrt{2} \\sin{\\left(\\beta \\right)}}{2} & \\frac{\\cos{\\left(\\beta \\right)}}{2} + \\frac{1}{2}\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[  cos(beta)/2 + 1/2, -sqrt(2)*sin(beta)/2,    1/2 - cos(beta)/2],\n", "[sqrt(2)*sin(beta)/2,            cos(beta), -sqrt(2)*sin(beta)/2],\n", "[  1/2 - cos(beta)/2,  sqrt(2)*sin(beta)/2,    cos(beta)/2 + 1/2]])"]}, "execution_count": 363, "metadata": {}, "output_type": "execute_result"}], "source": ["((((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0])*Matrix([[1,0,0],[0,cos(b)-I*sin(b),0],[0,0,cos(b)+I*sin(b)]])*Inverse((((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0]))).simplify()"]}, {"cell_type": "code", "execution_count": 371, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle m_{p}$"], "text/plain": ["m_p"]}, "execution_count": 371, "metadata": {}, "output_type": "execute_result"}], "source": ["mp,m,g = symbols(rf'm_p m gamma', real=True)\n", "mp"]}, {"cell_type": "code", "execution_count": 373, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle e^{- i \\gamma m}$"], "text/plain": ["exp(-I*gamma*m)"]}, "execution_count": 373, "metadata": {}, "output_type": "execute_result"}], "source": ["E**(-I*g*m)"]}, {"cell_type": "code", "execution_count": 376, "metadata": {}, "outputs": [], "source": ["D=E**(-I*a*mp)*((((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0])*Matrix([[1,0,0],[0,cos(b)-I*sin(b),0],[0,0,cos(b)+I*sin(b)]])*Inverse((((b/sqrt(2))*Matrix([[0,-1,0],[1,0,-1],[0,1,0]])).diagonalize()[0]))).simplify()*E**(-I*g*m)"]}, {"cell_type": "code", "execution_count": 388, "metadata": {}, "outputs": [], "source": ["Y11, Y10, Y1m1 = symbols(r'Y_{1\\,1} Y_{1\\,0} Y_{1\\,-1}')"]}, {"cell_type": "code", "execution_count": 402, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}Y_{1,1}\\\\Y_{1,0}\\\\Y_{1,-1}\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[ Y_{1,1}],\n", "[ Y_{1,0}],\n", "[Y_{1,-1}]])"]}, "execution_count": 402, "metadata": {}, "output_type": "execute_result"}], "source": ["YMat = Matrix([Y11,Y10,Y1m1])\n", "YMat"]}, {"cell_type": "code", "execution_count": 411, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\left(Y_{1,-1} \\cos{\\left(\\beta \\right)} + Y_{1,-1} - \\sqrt{2} Y_{1,0} \\sin{\\left(\\beta \\right)} - Y_{1,1} \\cos{\\left(\\beta \\right)} + Y_{1,1}\\right) e^{- i \\left(\\alpha m_{p} + \\gamma m\\right)}}{2}$"], "text/plain": ["(Y_{1,-1}*cos(beta) + Y_{1,-1} - sqrt(2)*Y_{1,0}*sin(beta) - Y_{1,1}*cos(beta) + Y_{1,1})*exp(-I*(alpha*m_p + gamma*m))/2"]}, "execution_count": 411, "metadata": {}, "output_type": "execute_result"}], "source": ["(Transpose(D)*YMat).simplify()[2]"]}, {"cell_type": "code", "execution_count": 410, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left[\\begin{matrix}\\left(\\frac{\\cos{\\left(\\beta \\right)}}{2} + \\frac{1}{2}\\right) e^{- i \\alpha m_{p}} e^{- i \\gamma m} & - \\frac{\\sqrt{2} e^{- i \\alpha m_{p}} e^{- i \\gamma m} \\sin{\\left(\\beta \\right)}}{2} & \\left(\\frac{1}{2} - \\frac{\\cos{\\left(\\beta \\right)}}{2}\\right) e^{- i \\alpha m_{p}} e^{- i \\gamma m}\\\\\\frac{\\sqrt{2} e^{- i \\alpha m_{p}} e^{- i \\gamma m} \\sin{\\left(\\beta \\right)}}{2} & e^{- i \\alpha m_{p}} e^{- i \\gamma m} \\cos{\\left(\\beta \\right)} & - \\frac{\\sqrt{2} e^{- i \\alpha m_{p}} e^{- i \\gamma m} \\sin{\\left(\\beta \\right)}}{2}\\\\\\left(\\frac{1}{2} - \\frac{\\cos{\\left(\\beta \\right)}}{2}\\right) e^{- i \\alpha m_{p}} e^{- i \\gamma m} & \\frac{\\sqrt{2} e^{- i \\alpha m_{p}} e^{- i \\gamma m} \\sin{\\left(\\beta \\right)}}{2} & \\left(\\frac{\\cos{\\left(\\beta \\right)}}{2} + \\frac{1}{2}\\right) e^{- i \\alpha m_{p}} e^{- i \\gamma m}\\end{matrix}\\right]$"], "text/plain": ["Matrix([\n", "[(cos(beta)/2 + 1/2)*exp(-I*alpha*m_p)*exp(-I*gamma*m), -sqrt(2)*exp(-I*alpha*m_p)*exp(-I*gamma*m)*sin(beta)/2,  (1/2 - cos(beta)/2)*exp(-I*alpha*m_p)*exp(-I*gamma*m)],\n", "[sqrt(2)*exp(-I*alpha*m_p)*exp(-I*gamma*m)*sin(beta)/2,            exp(-I*alpha*m_p)*exp(-I*gamma*m)*cos(beta), -sqrt(2)*exp(-I*alpha*m_p)*exp(-I*gamma*m)*sin(beta)/2],\n", "[(1/2 - cos(beta)/2)*exp(-I*alpha*m_p)*exp(-I*gamma*m),  sqrt(2)*exp(-I*alpha*m_p)*exp(-I*gamma*m)*sin(beta)/2,  (cos(beta)/2 + 1/2)*exp(-I*alpha*m_p)*exp(-I*gamma*m)]])"]}, "execution_count": 410, "metadata": {}, "output_type": "execute_result"}], "source": ["D"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "mainenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}