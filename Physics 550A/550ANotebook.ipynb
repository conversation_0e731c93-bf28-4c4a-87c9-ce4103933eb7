{"cells": [{"cell_type": "code", "execution_count": 320, "metadata": {}, "outputs": [], "source": ["from sympy import *"]}, {"cell_type": "code", "execution_count": 321, "metadata": {}, "outputs": [], "source": ["al, x, h, m, En = symbols('alpha x hbar m E_n', real=True)"]}, {"cell_type": "code", "execution_count": 322, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["phi(x)\n"]}], "source": ["phi = Function('phi')(x)\n", "print(phi)"]}, {"cell_type": "code", "execution_count": 323, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- \\frac{2 \\alpha^{2} \\sinh^{2}{\\left(\\alpha x \\right)}}{\\cosh^{3}{\\left(\\alpha x \\right)}} + \\frac{\\alpha^{2}}{\\cosh{\\left(\\alpha x \\right)}} - \\frac{2 \\alpha^{2}}{\\cosh^{3}{\\left(\\alpha x \\right)}} = \\frac{2 E_{n} m}{\\hbar^{2} \\cosh{\\left(\\alpha x \\right)}}\n"]}], "source": ["FF = Eq(-h**2/(2*m)*diff(phi,x,2)*(2*m/h**2) - h**2/m * al**2/cosh(al*x)**2*phi*(2*m/h**2), En*phi*(2*m/h**2)).subs(phi,1/cosh(al*x)).doit().expand()\n", "print_latex(FF)"]}, {"cell_type": "code", "execution_count": 324, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- \\frac{\\alpha^{2}}{\\cosh{\\left(\\alpha x \\right)}}\n"]}], "source": ["print_latex(FF.lhs.subs(sinh(al*x)**2, cosh(al*x)**2 - 1).expand())"]}, {"cell_type": "code", "execution_count": 325, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{2 \\alpha^{2} \\left(\\cosh^{2}{\\left(\\alpha x \\right)} - 1\\right)}{\\cosh^{3}{\\left(\\alpha x \\right)}} + \\frac{\\alpha^{2}}{\\cosh{\\left(\\alpha x \\right)}} - \\frac{2 \\alpha^{2}}{\\cosh^{3}{\\left(\\alpha x \\right)}}$"], "text/plain": ["-2*alpha**2*(cosh(alpha*x)**2 - 1)/cosh(alpha*x)**3 + alpha**2/cosh(alpha*x) - 2*alpha**2/cosh(alpha*x)**3"]}, "execution_count": 325, "metadata": {}, "output_type": "execute_result"}], "source": ["FF.lhs.subs(sinh(al*x)**2, cosh(al*x)**2 - 1)"]}, {"cell_type": "code", "execution_count": 326, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{2 \\alpha^{2}}{\\cosh{\\left(\\alpha x \\right)}} + \\frac{2 \\alpha^{2}}{\\cosh^{3}{\\left(\\alpha x \\right)}}$"], "text/plain": ["-2*alpha**2/cosh(alpha*x) + 2*alpha**2/cosh(alpha*x)**3"]}, "execution_count": 326, "metadata": {}, "output_type": "execute_result"}], "source": ["(-2*al**2*(cosh(al*x)**2 - 1)/(cosh(al*x)**3)).expand()"]}, {"cell_type": "code", "execution_count": 327, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle k$"], "text/plain": ["k"]}, "execution_count": 327, "metadata": {}, "output_type": "execute_result"}], "source": ["V, A, B, C, D, F = symbols('V_0 A B C D F')\n", "V, a, k = symbols('V_0 a k', real=True, positive=True)\n", "V = symbols('V_0', real=True, positive=True)\n", "a = symbols('a', real=True, positive=True)\n", "h = symbols('hbar', real=True, positive=True)\n", "m = symbols('m', real=True, positive=True)\n", "k = symbols('k', real=True, positive=True)\n", "k"]}, {"cell_type": "code", "execution_count": 328, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["A e^{i a k} + B e^{- i a k} = C e^{i a k} + D e^{- i a k}\n"]}], "source": ["Eq1 = Eq(A*E**(I*k*a)+B*E**(-I*k*a), C*E**(I*k*a)+D*E**(-I*k*a)).subs(V,-2*V*m/h**2)\n", "print_latex(Eq1)"]}, {"cell_type": "code", "execution_count": 329, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["C e^{- i a k} + D e^{i a k} = F e^{i a k}\n"]}], "source": ["Eq2 = Eq(C*E**(-I*k*a) + D*E**(I*k*a), F*E**(I*k*a)).subs(V,-2*V*m/h**2)\n", "print_latex(Eq2)"]}, {"cell_type": "code", "execution_count": 330, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["i F k e^{i a k} + i k \\left(C e^{- i a k} - D e^{i a k}\\right) = \\frac{2 F V_{0} m e^{i a k}}{\\hbar^{2}}\n"]}], "source": ["Eq3 = Eq(I*k*F*E**(I*k*a) + I*k*(C*E**(-I*k*a) - D*E**(I*k*a)), -V*F*E**(I*k*a)).subs(V,-2*V*m/h**2)\n", "print_latex(Eq3)"]}, {"cell_type": "code", "execution_count": 331, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["i A k e^{i a k} - i B k e^{- i a k} - i C k e^{i a k} + i D k e^{- i a k} = \\frac{2 V_{0} m \\left(A e^{i a k} + B e^{- i a k}\\right)}{\\hbar^{2}}\n"]}], "source": ["Eq4 = Eq(I*k*A*E**(I*k*a) - I*k*B*E**(-I*k*a) + I*k*D*E**(-I*k*a) -I*k*C*E**(I*k*a), -V*(A*E**(I*k*a)+B*E**(-I*k*a))).subs(V,-2*V*m/h**2)\n", "print_latex(Eq4)"]}, {"cell_type": "code", "execution_count": 332, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["A = -B*V_0**2*m**2*exp(4*I*a*k)/(V_0**2*m**2*exp(6*I*a*k) - V_0**2*m**2*exp(2*I*a*k) + 2*I*V_0*hbar**2*k*m*exp(2*I*a*k) + hbar**4*k**2*exp(2*I*a*k)) + B*V_0**2*m**2/(V_0**2*m**2*exp(6*I*a*k) - V_0**2*m**2*exp(2*I*a*k) + 2*I*V_0*hbar**2*k*m*exp(2*I*a*k) + hbar**4*k**2*exp(2*I*a*k)) - I*B*V_0*hbar**2*k*m*exp(4*I*a*k)/(V_0**2*m**2*exp(6*I*a*k) - V_0**2*m**2*exp(2*I*a*k) + 2*I*V_0*hbar**2*k*m*exp(2*I*a*k) + hbar**4*k**2*exp(2*I*a*k)) - I*B*V_0*hbar**2*k*m/(V_0**2*m**2*exp(6*I*a*k) - V_0**2*m**2*exp(2*I*a*k) + 2*I*V_0*hbar**2*k*m*exp(2*I*a*k) + hbar**4*k**2*exp(2*I*a*k))\n", "C = -I*B*V_0*hbar**2*k*m*exp(2*I*a*k)/(V_0**2*m**2*exp(4*I*a*k) - V_0**2*m**2 + 2*I*V_0*hbar**2*k*m + hbar**4*k**2)\n", "D = I*B*V_0*hbar**2*k*m/(V_0**2*m**2*exp(4*I*a*k) - V_0**2*m**2 + 2*I*V_0*hbar**2*k*m + hbar**4*k**2) + B*hbar**4*k**2/(V_0**2*m**2*exp(4*I*a*k) - V_0**2*m**2 + 2*I*V_0*hbar**2*k*m + hbar**4*k**2)\n", "F = B*hbar**4*k**2/(V_0**2*m**2*exp(4*I*a*k) - V_0**2*m**2 + 2*I*V_0*hbar**2*k*m + hbar**4*k**2)\n"]}], "source": ["\n", "# Solve system of equations for A, C, D, F in terms of B\n", "sols = solve((Eq1, Eq2, Eq3, Eq4), (A, C, D, F))\n", "\n", "# Print solutions\n", "for var, expr in sols.items():\n", "    print(f\"{var} = {expr}\")\n"]}, {"cell_type": "code", "execution_count": 333, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\frac{\\hbar^{4} k^{2}}{V_{0}^{2} m^{2} e^{4 i a k} - V_{0}^{2} m^{2} + 2 i V_{0} \\hbar^{2} k m + \\hbar^{4} k^{2}}\n"]}], "source": ["print_latex(sols[F].simplify()/B)"]}, {"cell_type": "code", "execution_count": 334, "metadata": {}, "outputs": [], "source": ["T = (sols[F]/B).simplify().simplify()\n", "numer = (T*T.conjugate()).as_numer_denom()[0]\n", "denom = (T*T.conjugate()).as_numer_denom()[1]\n", "Dnom = (re(factor(denom.simplify()).expand())+I*im(factor(denom.simplify()).expand()))"]}, {"cell_type": "code", "execution_count": 335, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{i \\hbar^{8} k^{4} \\sin{\\left(4 a k \\right)} + \\hbar^{8} k^{4} \\cos{\\left(4 a k \\right)}}{2 V_{0}^{4} m^{4} \\cos{\\left(4 a k \\right)} - V_{0}^{4} m^{4} \\cos{\\left(8 a k \\right)} - V_{0}^{4} m^{4} + 2 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(8 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(4 a k \\right)} + V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(8 a k \\right)} + V_{0}^{2} \\hbar^{4} k^{2} m^{2} + \\hbar^{8} k^{4} \\cos{\\left(4 a k \\right)} + i \\left(2 V_{0}^{4} m^{4} \\sin{\\left(4 a k \\right)} - V_{0}^{4} m^{4} \\sin{\\left(8 a k \\right)} - 2 V_{0}^{3} \\hbar^{2} k m^{3} \\cos{\\left(8 a k \\right)} + 2 V_{0}^{3} \\hbar^{2} k m^{3} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(4 a k \\right)} + V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(8 a k \\right)} + \\hbar^{8} k^{4} \\sin{\\left(4 a k \\right)}\\right)}$"], "text/plain": ["(I*hbar**8*k**4*sin(4*a*k) + hbar**8*k**4*cos(4*a*k))/(2*V_0**4*m**4*cos(4*a*k) - V_0**4*m**4*cos(8*a*k) - V_0**4*m**4 + 2*V_0**3*hbar**2*k*m**3*sin(8*a*k) + 2*V_0**2*hbar**4*k**2*m**2*cos(4*a*k) + V_0**2*hbar**4*k**2*m**2*cos(8*a*k) + V_0**2*hbar**4*k**2*m**2 + hbar**8*k**4*cos(4*a*k) + I*(2*V_0**4*m**4*sin(4*a*k) - V_0**4*m**4*sin(8*a*k) - 2*V_0**3*hbar**2*k*m**3*cos(8*a*k) + 2*V_0**3*hbar**2*k*m**3 + 2*V_0**2*hbar**4*k**2*m**2*sin(4*a*k) + V_0**2*hbar**4*k**2*m**2*sin(8*a*k) + hbar**8*k**4*sin(4*a*k)))"]}, "execution_count": 335, "metadata": {}, "output_type": "execute_result"}], "source": ["Numer = (re(factor(numer.simplify()).expand())+I*im(factor(numer.simplify()).expand()))\n", "(Numer*(Dnom.conjugate()))/(Dnom*(Dnom.conjugate()))\n", "Numer*Dnom.conjugate().expand()/(Dnom*Dnom.conjugate().expand())"]}, {"cell_type": "code", "execution_count": 337, "metadata": {}, "outputs": [], "source": ["\n", "T = (sols[F]/B).simplify().simplify()\n"]}, {"cell_type": "code", "execution_count": 338, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\left(2 V_{0}^{4} m^{4} \\cos{\\left(4 a k \\right)} - V_{0}^{4} m^{4} \\cos{\\left(8 a k \\right)} - V_{0}^{4} m^{4} + 2 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(8 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(4 a k \\right)} + V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(8 a k \\right)} + V_{0}^{2} \\hbar^{4} k^{2} m^{2} + \\hbar^{8} k^{4} \\cos{\\left(4 a k \\right)} + i \\left(- 2 V_{0}^{4} m^{4} \\sin{\\left(4 a k \\right)} + V_{0}^{4} m^{4} \\sin{\\left(8 a k \\right)} + 2 V_{0}^{3} \\hbar^{2} k m^{3} \\cos{\\left(8 a k \\right)} - 2 V_{0}^{3} \\hbar^{2} k m^{3} - 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(4 a k \\right)} - V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(8 a k \\right)} - \\hbar^{8} k^{4} \\sin{\\left(4 a k \\right)}\\right)\\right) \\left(2 V_{0}^{4} m^{4} \\cos{\\left(4 a k \\right)} - V_{0}^{4} m^{4} \\cos{\\left(8 a k \\right)} - V_{0}^{4} m^{4} + 2 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(8 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(4 a k \\right)} + V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(8 a k \\right)} + V_{0}^{2} \\hbar^{4} k^{2} m^{2} + \\hbar^{8} k^{4} \\cos{\\left(4 a k \\right)} + i \\left(2 V_{0}^{4} m^{4} \\sin{\\left(4 a k \\right)} - V_{0}^{4} m^{4} \\sin{\\left(8 a k \\right)} - 2 V_{0}^{3} \\hbar^{2} k m^{3} \\cos{\\left(8 a k \\right)} + 2 V_{0}^{3} \\hbar^{2} k m^{3} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(4 a k \\right)} + V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(8 a k \\right)} + \\hbar^{8} k^{4} \\sin{\\left(4 a k \\right)}\\right)\\right)$"], "text/plain": ["(2*V_0**4*m**4*cos(4*a*k) - V_0**4*m**4*cos(8*a*k) - V_0**4*m**4 + 2*V_0**3*hbar**2*k*m**3*sin(8*a*k) + 2*V_0**2*hbar**4*k**2*m**2*cos(4*a*k) + V_0**2*hbar**4*k**2*m**2*cos(8*a*k) + V_0**2*hbar**4*k**2*m**2 + hbar**8*k**4*cos(4*a*k) + I*(-2*V_0**4*m**4*sin(4*a*k) + V_0**4*m**4*sin(8*a*k) + 2*V_0**3*hbar**2*k*m**3*cos(8*a*k) - 2*V_0**3*hbar**2*k*m**3 - 2*V_0**2*hbar**4*k**2*m**2*sin(4*a*k) - V_0**2*hbar**4*k**2*m**2*sin(8*a*k) - hbar**8*k**4*sin(4*a*k)))*(2*V_0**4*m**4*cos(4*a*k) - V_0**4*m**4*cos(8*a*k) - V_0**4*m**4 + 2*V_0**3*hbar**2*k*m**3*sin(8*a*k) + 2*V_0**2*hbar**4*k**2*m**2*cos(4*a*k) + V_0**2*hbar**4*k**2*m**2*cos(8*a*k) + V_0**2*hbar**4*k**2*m**2 + hbar**8*k**4*cos(4*a*k) + I*(2*V_0**4*m**4*sin(4*a*k) - V_0**4*m**4*sin(8*a*k) - 2*V_0**3*hbar**2*k*m**3*cos(8*a*k) + 2*V_0**3*hbar**2*k*m**3 + 2*V_0**2*hbar**4*k**2*m**2*sin(4*a*k) + V_0**2*hbar**4*k**2*m**2*sin(8*a*k) + hbar**8*k**4*sin(4*a*k)))"]}, "execution_count": 338, "metadata": {}, "output_type": "execute_result"}], "source": ["(Dnom*Dnom.conjugate().simplify())"]}, {"cell_type": "code", "execution_count": 339, "metadata": {}, "outputs": [], "source": ["TT2 = re(T.as_numer_denom()[1])*(T.as_numer_denom()[0]).conjugate().expand()+I*im(T.as_numer_denom()[1])*(T.as_numer_denom()[0]).conjugate().expand()"]}, {"cell_type": "code", "execution_count": 340, "metadata": {}, "outputs": [], "source": ["TT1 = T.as_numer_denom()[0].expand()"]}, {"cell_type": "code", "execution_count": 341, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 2 V_{0}^{4} m^{4} \\cos{\\left(4 a k \\right)} - V_{0}^{4} m^{4} \\cos{\\left(8 a k \\right)} - V_{0}^{4} m^{4} + 2 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(8 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(4 a k \\right)} + V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(8 a k \\right)} + V_{0}^{2} \\hbar^{4} k^{2} m^{2} + \\hbar^{8} k^{4} \\cos{\\left(4 a k \\right)} + i \\left(2 V_{0}^{4} m^{4} \\sin{\\left(4 a k \\right)} - V_{0}^{4} m^{4} \\sin{\\left(8 a k \\right)} - 2 V_{0}^{3} \\hbar^{2} k m^{3} \\cos{\\left(8 a k \\right)} + 2 V_{0}^{3} \\hbar^{2} k m^{3} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(4 a k \\right)} + V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(8 a k \\right)} + \\hbar^{8} k^{4} \\sin{\\left(4 a k \\right)}\\right)$"], "text/plain": ["2*V_0**4*m**4*cos(4*a*k) - V_0**4*m**4*cos(8*a*k) - V_0**4*m**4 + 2*V_0**3*hbar**2*k*m**3*sin(8*a*k) + 2*V_0**2*hbar**4*k**2*m**2*cos(4*a*k) + V_0**2*hbar**4*k**2*m**2*cos(8*a*k) + V_0**2*hbar**4*k**2*m**2 + hbar**8*k**4*cos(4*a*k) + I*(2*V_0**4*m**4*sin(4*a*k) - V_0**4*m**4*sin(8*a*k) - 2*V_0**3*hbar**2*k*m**3*cos(8*a*k) + 2*V_0**3*hbar**2*k*m**3 + 2*V_0**2*hbar**4*k**2*m**2*sin(4*a*k) + V_0**2*hbar**4*k**2*m**2*sin(8*a*k) + hbar**8*k**4*sin(4*a*k))"]}, "execution_count": 341, "metadata": {}, "output_type": "execute_result"}], "source": ["Dnom"]}, {"cell_type": "code", "execution_count": 342, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle V_{0}^{4} m^{4} \\sin^{2}{\\left(4 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(4 a k \\right)} - 2 V_{0}^{4} m^{4} \\cos{\\left(4 a k \\right)} + V_{0}^{4} m^{4} + 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(4 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(4 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} + \\hbar^{8} k^{4}$"], "text/plain": ["V_0**4*m**4*sin(4*a*k)**2 + V_0**4*m**4*cos(4*a*k)**2 - 2*V_0**4*m**4*cos(4*a*k) + V_0**4*m**4 + 4*V_0**3*hbar**2*k*m**3*sin(4*a*k) + 2*V_0**2*hbar**4*k**2*m**2*cos(4*a*k) + 2*V_0**2*hbar**4*k**2*m**2 + hbar**8*k**4"]}, "execution_count": 342, "metadata": {}, "output_type": "execute_result"}], "source": ["TT2 = (re(T.as_numer_denom()[1])+ I*im(T.as_numer_denom()[1]))*(re(T.as_numer_denom()[1])- I*im(T.as_numer_denom()[1]))\n", "TT2.expand()\n"]}, {"cell_type": "code", "execution_count": 180, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\frac{\\hbar^{8} k^{4}}{\\left(- i V_{0}^{2} m^{2} \\sin{\\left(4 a k \\right)} + V_{0}^{2} m^{2} \\cos{\\left(4 a k \\right)} - V_{0}^{2} m^{2} - 2 i V_{0} \\hbar^{2} k m + \\hbar^{4} k^{2}\\right) \\left(i V_{0}^{2} m^{2} \\sin{\\left(4 a k \\right)} + V_{0}^{2} m^{2} \\cos{\\left(4 a k \\right)} - V_{0}^{2} m^{2} + 2 i V_{0} \\hbar^{2} k m + \\hbar^{4} k^{2}\\right)}\n"]}], "source": ["print_latex(factor(simplify(TT1**2/(factor(TT2.simplify())))))"]}, {"cell_type": "code", "execution_count": 184, "metadata": {}, "outputs": [], "source": ["Denom2 = factor(simplify(TT1**2/(factor(TT2.expand())))).as_numer_denom()[1]"]}, {"cell_type": "code", "execution_count": 212, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\hbar^{8} k^{4}}{- 2 V_{0}^{4} m^{4} \\cos{\\left(4 a k \\right)} + 2 V_{0}^{4} m^{4} + 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(4 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(4 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} + \\hbar^{8} k^{4}}$"], "text/plain": ["hbar**8*k**4/(-2*V_0**4*m**4*cos(4*a*k) + 2*V_0**4*m**4 + 4*V_0**3*hbar**2*k*m**3*sin(4*a*k) + 2*V_0**2*hbar**4*k**2*m**2*cos(4*a*k) + 2*V_0**2*hbar**4*k**2*m**2 + hbar**8*k**4)"]}, "execution_count": 212, "metadata": {}, "output_type": "execute_result"}], "source": ["TT1**2/(Denom2.subs(sin(4*a*k)**2, 1-cos(4*a*k)**2)).factor().expand()"]}, {"cell_type": "code", "execution_count": 211, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - 2 V_{0}^{4} m^{4} \\cos{\\left(4 a k \\right)} + 2 V_{0}^{4} m^{4} + 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(4 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(4 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} + \\hbar^{8} k^{4}$"], "text/plain": ["-2*V_0**4*m**4*cos(4*a*k) + 2*V_0**4*m**4 + 4*V_0**3*hbar**2*k*m**3*sin(4*a*k) + 2*V_0**2*hbar**4*k**2*m**2*cos(4*a*k) + 2*V_0**2*hbar**4*k**2*m**2 + hbar**8*k**4"]}, "execution_count": 211, "metadata": {}, "output_type": "execute_result"}], "source": ["((Denom2.subs(sin(4*a*k)**2, 1-cos(4*a*k)**2)).expand()).simplify"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 241, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\hbar^{4} k^{2} e^{- i \\operatorname{atan}{\\left(\\frac{V_{0}^{2} m^{2} \\sin{\\left(4 a k \\right)}}{V_{0}^{2} m^{2} \\cos{\\left(4 a k \\right)} - V_{0}^{2} m^{2} + \\hbar^{4} k^{2}} + \\frac{2 V_{0} \\hbar^{2} k m}{V_{0}^{2} m^{2} \\cos{\\left(4 a k \\right)} - V_{0}^{2} m^{2} + \\hbar^{4} k^{2}} \\right)}}}{\\sqrt{V_{0}^{4} m^{4} \\sin^{2}{\\left(4 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(4 a k \\right)} - 2 V_{0}^{4} m^{4} \\cos{\\left(4 a k \\right)} + V_{0}^{4} m^{4} + 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(4 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(4 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} + \\hbar^{8} k^{4}}}$"], "text/plain": ["hbar**4*k**2*exp(-I*atan(V_0**2*m**2*sin(4*a*k)/(V_0**2*m**2*cos(4*a*k) - V_0**2*m**2 + hbar**4*k**2) + 2*V_0*hbar**2*k*m/(V_0**2*m**2*cos(4*a*k) - V_0**2*m**2 + hbar**4*k**2)))/sqrt(V_0**4*m**4*sin(4*a*k)**2 + V_0**4*m**4*cos(4*a*k)**2 - 2*V_0**4*m**4*cos(4*a*k) + V_0**4*m**4 + 4*V_0**3*hbar**2*k*m**3*sin(4*a*k) + 2*V_0**2*hbar**4*k**2*m**2*cos(4*a*k) + 2*V_0**2*hbar**4*k**2*m**2 + hbar**8*k**4)"]}, "execution_count": 241, "metadata": {}, "output_type": "execute_result"}], "source": ["Tdenom = T.as_numer_denom()[1].expand()\n", "Tnumer = T.as_numer_denom()[0].expand()\n", "Tmag = abs(((Tnumer/(re(Tdenom) + I*im(Tdenom)))).expand())\n", "Targ = atan(im(T)/re(T)).expand().doit()\n", "(Tmag*E**(I*Targ)).doit()"]}, {"cell_type": "code", "execution_count": 255, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\frac{\\hbar^{4} k^{2} \\left(V_{0}^{2} m^{2} \\cos{\\left(4 a k \\right)} - V_{0}^{2} m^{2} + \\hbar^{4} k^{2}\\right)}{\\left(V_{0}^{2} m^{2} \\sin{\\left(4 a k \\right)} + 2 V_{0} \\hbar^{2} k m\\right)^{2} + \\left(V_{0}^{2} m^{2} \\cos{\\left(4 a k \\right)} - V_{0}^{2} m^{2} + \\hbar^{4} k^{2}\\right)^{2}}\n"]}], "source": ["print_latex(re(T))"]}, {"cell_type": "code", "execution_count": 257, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\frac{- V_{0}^{2} m^{2} \\sin{\\left(4 a k \\right)} - 2 V_{0} \\hbar^{2} k m}{V_{0}^{2} m^{2} \\cos{\\left(4 a k \\right)} - V_{0}^{2} m^{2} + \\hbar^{4} k^{2}}\n"]}], "source": ["print_latex((im(T)/re(T)))"]}, {"cell_type": "code", "execution_count": 275, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\frac{V_{0}^{4} m^{4} \\sin{\\left(2 a k \\right)}}{V_{0}^{4} m^{4} \\sin^{2}{\\left(2 a k \\right)} - 2 V_{0}^{4} m^{4} \\sin{\\left(2 a k \\right)} \\sin{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\sin^{2}{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(2 a k \\right)} - 2 V_{0}^{4} m^{4} \\cos{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(6 a k \\right)} - 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(6 a k \\right)} \\cos{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin^{2}{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(2 a k \\right)} \\sin{\\left(6 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos^{2}{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + \\hbar^{8} k^{4} \\sin^{2}{\\left(2 a k \\right)} + \\hbar^{8} k^{4} \\cos^{2}{\\left(2 a k \\right)}} - \\frac{V_{0}^{4} m^{4} \\sin{\\left(6 a k \\right)}}{V_{0}^{4} m^{4} \\sin^{2}{\\left(2 a k \\right)} - 2 V_{0}^{4} m^{4} \\sin{\\left(2 a k \\right)} \\sin{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\sin^{2}{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(2 a k \\right)} - 2 V_{0}^{4} m^{4} \\cos{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(6 a k \\right)} - 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(6 a k \\right)} \\cos{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin^{2}{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(2 a k \\right)} \\sin{\\left(6 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos^{2}{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + \\hbar^{8} k^{4} \\sin^{2}{\\left(2 a k \\right)} + \\hbar^{8} k^{4} \\cos^{2}{\\left(2 a k \\right)}} - \\frac{V_{0}^{3} \\hbar^{2} k m^{3} \\cos{\\left(2 a k \\right)}}{V_{0}^{4} m^{4} \\sin^{2}{\\left(2 a k \\right)} - 2 V_{0}^{4} m^{4} \\sin{\\left(2 a k \\right)} \\sin{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\sin^{2}{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(2 a k \\right)} - 2 V_{0}^{4} m^{4} \\cos{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(6 a k \\right)} - 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(6 a k \\right)} \\cos{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin^{2}{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(2 a k \\right)} \\sin{\\left(6 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos^{2}{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + \\hbar^{8} k^{4} \\sin^{2}{\\left(2 a k \\right)} + \\hbar^{8} k^{4} \\cos^{2}{\\left(2 a k \\right)}} - \\frac{V_{0}^{3} \\hbar^{2} k m^{3} \\cos{\\left(6 a k \\right)}}{V_{0}^{4} m^{4} \\sin^{2}{\\left(2 a k \\right)} - 2 V_{0}^{4} m^{4} \\sin{\\left(2 a k \\right)} \\sin{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\sin^{2}{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(2 a k \\right)} - 2 V_{0}^{4} m^{4} \\cos{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(6 a k \\right)} - 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(6 a k \\right)} \\cos{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin^{2}{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(2 a k \\right)} \\sin{\\left(6 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos^{2}{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + \\hbar^{8} k^{4} \\sin^{2}{\\left(2 a k \\right)} + \\hbar^{8} k^{4} \\cos^{2}{\\left(2 a k \\right)}} + \\frac{V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(2 a k \\right)}}{V_{0}^{4} m^{4} \\sin^{2}{\\left(2 a k \\right)} - 2 V_{0}^{4} m^{4} \\sin{\\left(2 a k \\right)} \\sin{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\sin^{2}{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(2 a k \\right)} - 2 V_{0}^{4} m^{4} \\cos{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(6 a k \\right)} - 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(6 a k \\right)} \\cos{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin^{2}{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(2 a k \\right)} \\sin{\\left(6 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos^{2}{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + \\hbar^{8} k^{4} \\sin^{2}{\\left(2 a k \\right)} + \\hbar^{8} k^{4} \\cos^{2}{\\left(2 a k \\right)}} - V_{0}^{2} m^{2} \\operatorname{im}{\\left(\\frac{e^{4 i a k}}{V_{0}^{2} m^{2} e^{6 i a k} - V_{0}^{2} m^{2} e^{2 i a k} + 2 i V_{0} \\hbar^{2} k m e^{2 i a k} + \\hbar^{4} k^{2} e^{2 i a k}}\\right)} - \\frac{V_{0} \\hbar^{6} k^{3} m \\cos{\\left(2 a k \\right)}}{V_{0}^{4} m^{4} \\sin^{2}{\\left(2 a k \\right)} - 2 V_{0}^{4} m^{4} \\sin{\\left(2 a k \\right)} \\sin{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\sin^{2}{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(2 a k \\right)} - 2 V_{0}^{4} m^{4} \\cos{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + V_{0}^{4} m^{4} \\cos^{2}{\\left(6 a k \\right)} - 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + 4 V_{0}^{3} \\hbar^{2} k m^{3} \\sin{\\left(6 a k \\right)} \\cos{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin^{2}{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\sin{\\left(2 a k \\right)} \\sin{\\left(6 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos^{2}{\\left(2 a k \\right)} + 2 V_{0}^{2} \\hbar^{4} k^{2} m^{2} \\cos{\\left(2 a k \\right)} \\cos{\\left(6 a k \\right)} + \\hbar^{8} k^{4} \\sin^{2}{\\left(2 a k \\right)} + \\hbar^{8} k^{4} \\cos^{2}{\\left(2 a k \\right)}} - V_{0} \\hbar^{2} k m \\operatorname{re}{\\left(\\frac{e^{4 i a k}}{V_{0}^{2} m^{2} e^{6 i a k} - V_{0}^{2} m^{2} e^{2 i a k} + 2 i V_{0} \\hbar^{2} k m e^{2 i a k} + \\hbar^{4} k^{2} e^{2 i a k}}\\right)}\n"]}], "source": ["R = (sols[A]/B).expand()\n", "print_latex(im(R).expand())"]}, {"cell_type": "code", "execution_count": 285, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{4 V_{0}^{2} \\hbar^{4} k^{2} m^{2}}{4 V_{0}^{2} \\hbar^{4} k^{2} m^{2} + \\hbar^{8} k^{4}} + \\frac{\\hbar^{8} k^{4}}{4 V_{0}^{2} \\hbar^{4} k^{2} m^{2} + \\hbar^{8} k^{4}}$"], "text/plain": ["4*V_0**2*hbar**4*k**2*m**2/(4*V_0**2*hbar**4*k**2*m**2 + hbar**8*k**4) + hbar**8*k**4/(4*V_0**2*hbar**4*k**2*m**2 + hbar**8*k**4)"]}, "execution_count": 285, "metadata": {}, "output_type": "execute_result"}], "source": ["(abs(<PERSON>.simplify().subs(a,0))**2+abs(T)**2).expand().subs(a,0)"]}, {"cell_type": "code", "execution_count": 368, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{V_{0} m \\left(- V_{0} m - i V_{0} m - \\hbar^{2} k + i \\hbar^{2} k - \\left(3 V_{0} m e^{4 i a k} - 4 V_{0} m - i V_{0} m + 3 i \\hbar^{2} k e^{4 i a k} + \\hbar^{2} k + 2 i \\hbar^{2} k\\right) e^{4 i a k}\\right) e^{2 i a k}}{V_{0}^{2} m^{2} \\cdot \\left(1 + i\\right) + 3 \\left(V_{0}^{2} m^{2} e^{4 i a k} - V_{0}^{2} m^{2} + 2 i V_{0} \\hbar^{2} k m + \\hbar^{4} k^{2}\\right) e^{8 i a k} + \\left(- V_{0}^{2} m^{2} - i V_{0}^{2} m^{2} + 2 V_{0} \\hbar^{2} k m - 2 i V_{0} \\hbar^{2} k m + \\hbar^{4} k^{2} + i \\hbar^{4} k^{2}\\right) e^{4 i a k}}$"], "text/plain": ["V_0*m*(-V_0*m - I*V_0*m - hbar**2*k + I*hbar**2*k - (3*V_0*m*exp(4*I*a*k) - 4*V_0*m - I*V_0*m + 3*I*hbar**2*k*exp(4*I*a*k) + hbar**2*k + 2*I*hbar**2*k)*exp(4*I*a*k))*exp(2*I*a*k)/(V_0**2*m**2*(1 + I) + 3*(V_0**2*m**2*exp(4*I*a*k) - V_0**2*m**2 + 2*I*V_0*hbar**2*k*m + hbar**4*k**2)*exp(8*I*a*k) + (-V_0**2*m**2 - I*V_0**2*m**2 + 2*V_0*hbar**2*k*m - 2*I*V_0*hbar**2*k*m + hbar**4*k**2 + I*hbar**4*k**2)*exp(4*I*a*k))"]}, "execution_count": 368, "metadata": {}, "output_type": "execute_result"}], "source": ["Rnumer = R.simplify().as_numer_denom()[0]\n", "Rdenom = R.simplify().as_numer_denom()[1]\n", "(((((Rnumer+Rnumer.conjugate())/2).expand() + (Rnumer-Rnumer.conjugate()/(2*I))).expand())/((((Rdenom+Rdenom.conjugate())/2).expand() + (Rdenom-Rdenom.conjugate()/(2*I))).expand())).simplify()"]}, {"cell_type": "code", "execution_count": 373, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{V_{0}^{2} m^{2} e^{4 i a k}}{V_{0}^{2} m^{2} e^{6 i a k} - V_{0}^{2} m^{2} e^{2 i a k} + 2 i V_{0} \\hbar^{2} k m e^{2 i a k} + \\hbar^{4} k^{2} e^{2 i a k}} + \\frac{V_{0}^{2} m^{2}}{V_{0}^{2} m^{2} e^{6 i a k} - V_{0}^{2} m^{2} e^{2 i a k} + 2 i V_{0} \\hbar^{2} k m e^{2 i a k} + \\hbar^{4} k^{2} e^{2 i a k}} - \\frac{i V_{0} \\hbar^{2} k m e^{4 i a k}}{V_{0}^{2} m^{2} e^{6 i a k} - V_{0}^{2} m^{2} e^{2 i a k} + 2 i V_{0} \\hbar^{2} k m e^{2 i a k} + \\hbar^{4} k^{2} e^{2 i a k}} - \\frac{i V_{0} \\hbar^{2} k m}{V_{0}^{2} m^{2} e^{6 i a k} - V_{0}^{2} m^{2} e^{2 i a k} + 2 i V_{0} \\hbar^{2} k m e^{2 i a k} + \\hbar^{4} k^{2} e^{2 i a k}}$"], "text/plain": ["-V_0**2*m**2*exp(4*I*a*k)/(V_0**2*m**2*exp(6*I*a*k) - V_0**2*m**2*exp(2*I*a*k) + 2*I*V_0*hbar**2*k*m*exp(2*I*a*k) + hbar**4*k**2*exp(2*I*a*k)) + V_0**2*m**2/(V_0**2*m**2*exp(6*I*a*k) - V_0**2*m**2*exp(2*I*a*k) + 2*I*V_0*hbar**2*k*m*exp(2*I*a*k) + hbar**4*k**2*exp(2*I*a*k)) - I*V_0*hbar**2*k*m*exp(4*I*a*k)/(V_0**2*m**2*exp(6*I*a*k) - V_0**2*m**2*exp(2*I*a*k) + 2*I*V_0*hbar**2*k*m*exp(2*I*a*k) + hbar**4*k**2*exp(2*I*a*k)) - I*V_0*hbar**2*k*m/(V_0**2*m**2*exp(6*I*a*k) - V_0**2*m**2*exp(2*I*a*k) + 2*I*V_0*hbar**2*k*m*exp(2*I*a*k) + hbar**4*k**2*exp(2*I*a*k))"]}, "execution_count": 373, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 379, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{4 V_{0}^{2} m^{2} \\left(- V_{0}^{2} m^{2} \\left(1 - \\cos{\\left(2 a k \\right)}\\right)^{3} + 3 V_{0}^{2} m^{2} \\left(1 - \\cos{\\left(2 a k \\right)}\\right)^{2} + 2 V_{0}^{2} m^{2} \\cos{\\left(2 a k \\right)} - 2 V_{0}^{2} m^{2} - V_{0} \\hbar^{2} k m \\left(1 - \\cos{\\left(2 a k \\right)}\\right)^{2} \\sin{\\left(2 a k \\right)} - V_{0} \\hbar^{2} k m \\sin{\\left(4 a k \\right)} - \\hbar^{4} k^{2} \\cos{\\left(2 a k \\right)}\\right)}{V_{0}^{2} m^{2} \\left(V_{0} m \\sin{\\left(4 a k \\right)} + 2 \\hbar^{2} k\\right)^{2} + \\left(8 V_{0}^{2} m^{2} \\sin^{4}{\\left(a k \\right)} - 8 V_{0}^{2} m^{2} \\sin^{2}{\\left(a k \\right)} + \\hbar^{4} k^{2}\\right)^{2}}$"], "text/plain": ["4*V_0**2*m**2*(-V_0**2*m**2*(1 - cos(2*a*k))**3 + 3*V_0**2*m**2*(1 - cos(2*a*k))**2 + 2*V_0**2*m**2*cos(2*a*k) - 2*V_0**2*m**2 - V_0*hbar**2*k*m*(1 - cos(2*a*k))**2*sin(2*a*k) - V_0*hbar**2*k*m*sin(4*a*k) - hbar**4*k**2*cos(2*a*k))/(V_0**2*m**2*(V_0*m*sin(4*a*k) + 2*hbar**2*k)**2 + (8*V_0**2*m**2*sin(a*k)**4 - 8*V_0**2*m**2*sin(a*k)**2 + hbar**4*k**2)**2)"]}, "execution_count": 379, "metadata": {}, "output_type": "execute_result"}], "source": ["(re(<PERSON>.simplify()).subs(E**(-I*2*a*k), cos(2*a*k) - I*sin(2*a*k))).simplify()"]}, {"cell_type": "code", "execution_count": 402, "metadata": {}, "outputs": [], "source": ["Denom22 = factor(((im(R.simplify()).subs(E**(-I*2*a*k), cos(2*a*k) - I*sin(2*a*k))).simplify()/((re(R.simplify()).subs(E**(-I*2*a*k), cos(2*a*k) - I*sin(2*a*k))).simplify())).subs(cos(a*k)**2,1-sin(a*k)**2)).as_numer_denom()[1]\n", "Numer22 = factor(((im(R.simplify()).subs(E**(-I*2*a*k), cos(2*a*k) - I*sin(2*a*k))).simplify()/((re(R.simplify()).subs(E**(-I*2*a*k), cos(2*a*k) - I*sin(2*a*k))).simplify())).subs(cos(a*k)**2,1-sin(a*k)**2)).as_numer_denom()[0]"]}, {"cell_type": "code", "execution_count": 429, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\frac{\\hbar^{2} k}{2 V_{0} m}\n"]}], "source": ["print_latex(((factor(Numer22).simplify()/factor(factor(Denom22).simplify()).simplify().expand())).subs(a,0))"]}, {"cell_type": "code", "execution_count": 436, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{B \\hbar^{2} k \\left(i V_{0} m + \\hbar^{2} k\\right)}{V_{0}^{2} m^{2} e^{4 i a k} - V_{0}^{2} m^{2} + 2 i V_{0} \\hbar^{2} k m + \\hbar^{4} k^{2}}$"], "text/plain": ["B*hbar**2*k*(I*V_0*m + hbar**2*k)/(V_0**2*m**2*exp(4*I*a*k) - V_0**2*m**2 + 2*I*V_0*hbar**2*k*m + hbar**4*k**2)"]}, "execution_count": 436, "metadata": {}, "output_type": "execute_result"}], "source": ["sols[D].simplify()"]}, {"cell_type": "code", "execution_count": 454, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{B \\left(8 i a m + \\pi \\hbar^{2}\\right)}{4 a m - i \\pi \\hbar^{2}}$"], "text/plain": ["B*(8*I*a*m + pi*hbar**2)/(4*a*m - I*pi*hbar**2)"]}, "execution_count": 454, "metadata": {}, "output_type": "execute_result"}], "source": ["((sols[A].subs(V,1)*E**(I*k*a)).subs(k,pi/(2*a)).expand() + B*E**(I*k*a).subs(k,pi/(2*a))).simplify()"]}, {"cell_type": "code", "execution_count": 477, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{i B V_{0} \\hbar^{2} k m e^{i a k}}{V_{0}^{2} m^{2} e^{4 i a k} - V_{0}^{2} m^{2} + 2 i V_{0} \\hbar^{2} k m + \\hbar^{4} k^{2}} + \\left(\\frac{i B V_{0} \\hbar^{2} k m}{V_{0}^{2} m^{2} e^{4 i a k} - V_{0}^{2} m^{2} + 2 i V_{0} \\hbar^{2} k m + \\hbar^{4} k^{2}} + \\frac{B \\hbar^{4} k^{2}}{V_{0}^{2} m^{2} e^{4 i a k} - V_{0}^{2} m^{2} + 2 i V_{0} \\hbar^{2} k m + \\hbar^{4} k^{2}}\\right) e^{- i a k} = 0$"], "text/plain": ["Eq(-I*B*V_0*hbar**2*k*m*exp(I*a*k)/(V_0**2*m**2*exp(4*I*a*k) - V_0**2*m**2 + 2*I*V_0*hbar**2*k*m + hbar**4*k**2) + (I*B*V_0*hbar**2*k*m/(V_0**2*m**2*exp(4*I*a*k) - V_0**2*m**2 + 2*I*V_0*hbar**2*k*m + hbar**4*k**2) + B*hbar**4*k**2/(V_0**2*m**2*exp(4*I*a*k) - V_0**2*m**2 + 2*I*V_0*hbar**2*k*m + hbar**4*k**2))*exp(-I*a*k), 0)"]}, "execution_count": 477, "metadata": {}, "output_type": "execute_result"}], "source": ["Eq((sols[C]*E**(-I*k*x)+ sols[D]*E**(-I*k*x)),0).subs(x,a)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "mainenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}