This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024/Arch Linux) (preloaded format=xelatex 2024.12.1)  4 DEC 2024 21:35
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/home/<USER>/WorkSpace/Physics 550A/HW4"
(/home/<USER>/WorkSpace/Physics 550A/HW4.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count184
\c@section=\count185
\c@subsection=\count186
\c@subsubsection=\count187
\c@paragraph=\count188
\c@subparagraph=\count189
\c@figure=\count190
\c@table=\count191
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (/usr/share/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks17
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (/usr/share/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2022/11/09 v4.1 Extensive control of page headers and footers
\f@nch@headwidth=\skip50
\f@nch@O@elh=\skip51
\f@nch@O@erh=\skip52
\f@nch@O@olh=\skip53
\f@nch@O@orh=\skip54
\f@nch@O@elf=\skip55
\f@nch@O@erf=\skip56
\f@nch@O@olf=\skip57
\f@nch@O@orf=\skip58
) (/usr/share/texmf-dist/tex/latex/listings/listings.sty (/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
)
\lst@mode=\count192
\lst@gtempboxa=\box51
\lst@token=\toks19
\lst@length=\count193
\lst@currlwidth=\dimen141
\lst@column=\count194
\lst@pos=\count195
\lst@lostspace=\dimen142
\lst@width=\dimen143
\lst@newlines=\count196
\lst@lineno=\count197
\lst@maxwidth=\dimen144
 (/usr/share/texmf-dist/tex/latex/listings/lstpatch.sty
File: lstpatch.sty 2024/02/21 1.10 (Carsten Heinz)
) (/usr/share/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2024/02/21 1.10 (Carsten Heinz)
\c@lstnumber=\count198
\lst@skipnumbers=\count199
\lst@framebox=\box52
) (/usr/share/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2024/02/21 1.10 listings configuration
))
Package: listings 2024/02/21 1.10 (Carsten Heinz)
 (/usr/share/texmf-dist/tex/latex/comment/comment.sty
\CommentStream=\write3
 Excluding comment 'comment') (/usr/share/texmf-dist/tex/latex/pdfpages/pdfpages.sty
Package: pdfpages 2024/01/21 v0.5y Insert pages of external PDF documents (AM)
 (/usr/share/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
) (/usr/share/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count266
\calc@Bcount=\count267
\calc@Adimen=\dimen145
\calc@Bdimen=\dimen146
\calc@Askip=\skip59
\calc@Bskip=\skip60
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count268
\calc@Cskip=\skip61
) (/usr/share/texmf-dist/tex/latex/eso-pic/eso-pic.sty
Package: eso-pic 2023/05/03 v3.0c eso-pic (RN)
\ESO@tempdima=\dimen147
\ESO@tempdimb=\dimen148
 (/usr/share/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (/usr/share/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
) (/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)) (/usr/share/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (/usr/share/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
)
\Gin@req@height=\dimen149
\Gin@req@width=\dimen150
)
\AM@pagewidth=\dimen151
\AM@pageheight=\dimen152
\AM@fboxrule=\dimen153
 (/usr/share/texmf-dist/tex/latex/pdfpages/ppxetex.def
File: ppxetex.def 2024/01/21 v0.5y Pdfpages driver for XeTeX (AM)
)
\pdfpages@includegraphics@status=\count269
\AM@pagebox=\box53
\AM@global@opts=\toks20
\AM@pagecnt=\count270
\AM@toc@title=\toks21
\AM@lof@heading=\toks22
\c@AM@survey=\count271
\AM@templatesizebox=\box54
) (/usr/share/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/share/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count272
\Gm@cntv=\count273
\c@Gm@tempcnt=\count274
\Gm@bindingoffset=\dimen154
\Gm@wd@mp=\dimen155
\Gm@odd@mp=\dimen156
\Gm@even@mp=\dimen157
\Gm@layoutwidth=\dimen158
\Gm@layoutheight=\dimen159
\Gm@layouthoffset=\dimen160
\Gm@layoutvoffset=\dimen161
\Gm@dimlist=\toks23
) (/usr/share/texmf-dist/tex/latex/stackengine/stackengine.sty
Package: stackengine 2021/07/22 v4.11\ Stacking text and objects in convenient ways
 (/usr/share/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count275
) (/usr/share/texmf-dist/tex/generic/listofitems/listofitems.sty (/usr/share/texmf-dist/tex/generic/listofitems/listofitems.tex
\loi_cnt_foreach_nest=\count276
\loi_nestcnt=\count277
)
Package: listofitems 2024/03/09 v1.65 Grab items in lists using user-specified sep char (CT)
)
\c@@stackindex=\count278
\@boxshift=\skip62
\stack@tmplength=\skip63
\temp@stkl=\skip64
\@stackedboxwidth=\skip65
\@addedbox=\box55
\@anchorbox=\box56
\@insetbox=\box57
\se@backgroundbox=\box58
\stackedbox=\box59
\@centerbox=\box60
\c@ROWcellindex@=\count279
) (/usr/share/texmf-dist/tex/latex/fontspec/fontspec.sty (/usr/share/texmf-dist/tex/latex/l3packages/xparse/xparse.sty (/usr/share/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (/usr/share/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count280
\l__pdf_internal_box=\box61
\g__pdf_backend_object_int=\count281
\g__pdf_backend_annotation_int=\count282
\g__pdf_backend_link_int=\count283
))
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: fontspec 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
 (/usr/share/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count284
\l__fontspec_language_int=\count285
\l__fontspec_strnum_int=\count286
\l__fontspec_tmp_int=\count287
\l__fontspec_tmpa_int=\count288
\l__fontspec_tmpb_int=\count289
\l__fontspec_tmpc_int=\count290
\l__fontspec_em_int=\count291
\l__fontspec_emdef_int=\count292
\l__fontspec_strong_int=\count293
\l__fontspec_strongdef_int=\count294
\l__fontspec_tmpa_dim=\dimen162
\l__fontspec_tmpb_dim=\dimen163
\l__fontspec_tmpc_dim=\dimen164
 (/usr/share/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (/usr/share/texmf-dist/tex/latex/fontspec/fontspec.cfg)))

Package fontspec Info: Font family 'TimesNewRoman(0)' created for font 'Times
(fontspec)             New Roman' with options [Ligatures=TeX].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"Times New
(fontspec)             Roman/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"Times New
(fontspec)             Roman/B/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"Times New
(fontspec)             Roman/I/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"Times New
(fontspec)             Roman/BI/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.: 


Package fontspec Info: Could not resolve font "Times New Roman Bold/B" (it
(fontspec)             probably doesn't exist).


Package fontspec Info: Font family 'TimesNewRomanBold(0)' created for font
(fontspec)             'Times New Roman Bold' with options [].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"Times New Roman
(fontspec)             Bold/OT:script=latn;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"Times New Roman
(fontspec)             Bold/I/OT:script=latn;language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"Times New
(fontspec)             Roman Bold/BI/OT:script=latn;language=dflt;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.: 

 (/usr/share/texmf-dist/tex/latex/cancel/cancel.sty
Package: cancel 2013/04/12 v2.2 Cancel math terms
) (/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip66

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks24
\ex@=\dimen165
)) (/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen166
) (/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count295
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count296
\leftroot@=\count297
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count298
\DOTSCASE@=\count299
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box62
\strutbox@=\box63
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen167
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count300
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count301
\dotsspace@=\muskip16
\c@parentequation=\count302
\dspbrk@lvl=\count303
\tag@help=\toks25
\row@=\count304
\column@=\count305
\maxfields@=\count306
\andhelp@=\toks26
\eqnshift@=\dimen168
\alignsep@=\dimen169
\tagshift@=\dimen170
\tagwidth@=\dimen171
\totwidth@=\dimen172
\lineht@=\dimen173
\@envbody=\toks27
\multlinegap=\skip67
\multlinetaggap=\skip68
\mathdisplay@stack=\toks28
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/share/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks29
\pgfutil@tempdima=\dimen174
\pgfutil@tempdimb=\dimen175
) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box64
) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/share/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/share/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks30
\pgfkeys@temptoks=\toks31
 (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks32
))
\pgf@x=\dimen176
\pgf@y=\dimen177
\pgf@xa=\dimen178
\pgf@ya=\dimen179
\pgf@xb=\dimen180
\pgf@yb=\dimen181
\pgf@xc=\dimen182
\pgf@yc=\dimen183
\pgf@xd=\dimen184
\pgf@yd=\dimen185
\w@pgf@writea=\write4
\r@pgf@reada=\read2
\c@pgf@counta=\count307
\c@pgf@countb=\count308
\c@pgf@countc=\count309
\c@pgf@countd=\count310
\t@pgf@toka=\toks33
\t@pgf@tokb=\toks34
\t@pgf@tokc=\toks35
\pgf@sys@id@count=\count311
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-xetex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count312
))) (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count313
\pgfsyssoftpath@bigbuffer@items=\count314
) (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen186
\pgfmath@count=\count315
\pgfmath@box=\box65
\pgfmath@toks=\toks36
\pgfmath@stack@operand=\toks37
\pgfmath@stack@operation=\toks38
) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count316
)) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen187
\pgf@picmaxx=\dimen188
\pgf@picminy=\dimen189
\pgf@picmaxy=\dimen190
\pgf@pathminx=\dimen191
\pgf@pathmaxx=\dimen192
\pgf@pathminy=\dimen193
\pgf@pathmaxy=\dimen194
\pgf@xx=\dimen195
\pgf@xy=\dimen196
\pgf@yx=\dimen197
\pgf@yy=\dimen198
\pgf@zx=\dimen199
\pgf@zy=\dimen256
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen257
\pgf@path@lasty=\dimen258
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen259
\pgf@shorten@start@additional=\dimen260
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box66
\pgf@hbox=\box67
\pgf@layerbox@main=\box68
\pgf@picture@serial@count=\count317
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen261
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen262
\pgf@pt@y=\dimen263
\pgf@pt@temp=\dimen264
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen265
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen266
\pgf@sys@shading@range@num=\count318
\pgf@shadingcount=\count319
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box69
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box70
) (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen267
\pgf@nodesepend=\dimen268
) (/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/usr/share/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen269
\pgffor@skip=\dimen270
\pgffor@stack=\toks39
\pgffor@toks=\toks40
)) (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count320
\pgfplotmarksize=\dimen271
)
\tikz@lastx=\dimen272
\tikz@lasty=\dimen273
\tikz@lastxsaved=\dimen274
\tikz@lastysaved=\dimen275
\tikz@lastmovetox=\dimen276
\tikz@lastmovetoy=\dimen277
\tikzleveldistance=\dimen278
\tikzsiblingdistance=\dimen279
\tikz@figbox=\box71
\tikz@figbox@bg=\box72
\tikz@tempbox=\box73
\tikz@tempbox@bg=\box74
\tikztreelevel=\count321
\tikznumberofchildren=\count322
\tikznumberofcurrentchild=\count323
\tikz@fig@count=\count324
 (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count325
\pgfmatrixcurrentcolumn=\count326
\pgf@matrix@numberofcolumns=\count327
)
\tikz@expandcount=\count328
 (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/mathtools/mathtools.sty
Package: mathtools 2024/03/11 v1.30 mathematical typesetting tools
 (/usr/share/texmf-dist/tex/latex/mathtools/mhsetup.sty
Package: mhsetup 2021/03/18 v1.4 programming setup (MH)
)
\g_MT_multlinerow_int=\count329
\l_MT_multwidth_dim=\dimen280
\origjot=\skip69
\l_MT_shortvdotswithinadjustabove_dim=\dimen281
\l_MT_shortvdotswithinadjustbelow_dim=\dimen282
\l_MT_above_intertext_sep=\dimen283
\l_MT_below_intertext_sep=\dimen284
\l_MT_above_shortintertext_sep=\dimen285
\l_MT_below_shortintertext_sep=\dimen286
\xmathstrut@box=\box75
\xmathstrut@dim=\dimen287
) (/usr/share/texmf-dist/tex/latex/changepage/changepage.sty
Package: changepage 2009/10/20 v1.0c check page and change page layout
\c@cp@cntr=\count330
\cp@tempcnt=\count331
) (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (/usr/share/texmf-dist/tex/latex/physics/physics.sty
Package: physics 
)
\c@theorem=\count332
\c@axiom=\count333
 (./HW4.aux)
\openout1 = `HW4.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 65.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 65.
 (/usr/share/texmf-dist/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 65.
LaTeX Font Info:    ... okay on input line 65.
\c@lstlisting=\count334
 (/usr/share/texmf-dist/tex/latex/pdflscape/pdflscape.sty
Package: pdflscape 2022-10-27 v0.13 Display of landscape pages in PDF
 (/usr/share/texmf-dist/tex/latex/pdflscape/pdflscape-nometadata.sty
Package: pdflscape-nometadata 2022-10-28 v0.13 Display of landscape pages in PDF (HO)
 (/usr/share/texmf-dist/tex/latex/graphics/lscape.sty
Package: lscape 2020/05/28 v3.02 Landscape Pages (DPC)
)
Package pdflscape Info: Auto-detected driver: dvipdfm (xetex) on input line 98.
))
*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(62.59596pt, 472.31595pt, 62.59596pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=472.31595pt
* \textheight=702.78308pt
* \oddsidemargin=-9.67403pt
* \evensidemargin=-9.67403pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 65.
LaTeX Font Info:    Redeclaring math accent \acute on input line 65.
LaTeX Font Info:    Redeclaring math accent \grave on input line 65.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 65.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 65.
LaTeX Font Info:    Redeclaring math accent \bar on input line 65.
LaTeX Font Info:    Redeclaring math accent \breve on input line 65.
LaTeX Font Info:    Redeclaring math accent \check on input line 65.
LaTeX Font Info:    Redeclaring math accent \hat on input line 65.
LaTeX Font Info:    Redeclaring math accent \dot on input line 65.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 65.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 65.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 65.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 65.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 65.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 65.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 65.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 65.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 65.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 65.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 65.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 65.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 65.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 65.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 65.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/TimesNewRoman(0)/m/n on input line 65.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 65.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRoman(0)/m/n on input line 65.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/TimesNewRoman(0)/m/n --> TU/TimesNewRoman(0)/m/n on input line 65.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/TimesNewRoman(0)/m/it on input line 65.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRoman(0)/b/n on input line 65.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 65.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 65.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/TimesNewRoman(0)/m/n --> TU/TimesNewRoman(0)/b/n on input line 65.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `bold'
(Font)                  TU/TimesNewRoman(0)/b/n --> TU/TimesNewRomanBold(0)/m/n on input line 65.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRomanBold(0)/b/n on input line 65.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/TimesNewRomanBold(0)/m/it on input line 65.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 65.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 65.
LaTeX Font Info:    Trying to load font information for U+msa on input line 67.
(/usr/share/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 67.
 (/usr/share/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
) [1

] [2]
Overfull \hbox (28.39485pt too wide) in paragraph at lines 140--143
\TU/TimesNewRoman(0)/m/n/12 Hence our Clebsch-Gordan coefficients are $[]\OML/cmm/m/it/12 ; []; []$ \TU/TimesNewRoman(0)/m/n/12 for the states $[] \OML/cmm/m/it/12 ; [] ; []$
 []

[3] [4] [5]
Overfull \hbox (23.35474pt too wide) detected at line 213
[]\OML/cmm/m/it/12 Y[]\OT1/cmr/m/n/12 (\OML/cmm/m/it/12 ^^R; ^^^\OT1/cmr/m/n/12 ) = []\OML/cmm/m/it/12 P[]\OT1/cmr/m/n/12 ([] [])\OML/cmm/m/it/12 e[] \OT1/cmr/m/n/12 = \OML/cmm/m/it/12 e[][]P[]\OT1/cmr/m/n/12 (\OMS/cmsy/m/n/12 ^^@ [] []\OT1/cmr/m/n/12 )\OML/cmm/m/it/12 e[]
 []

[6] [7] [8] [9] [10] [11] [12] [13] [14]
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 1>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 1>
 [15]
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 1>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 1>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 1>
 [16

]
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 2>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 2>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 2>
 [17

]
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 3>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 3>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 3>
 [18

]
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 4>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 4>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 4>
 [19

]
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 5>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 5>
File: How do I remove clock.pdf Graphic file (type pdf)
<use How do I remove clock.pdf, page 5>
 [20

] (./HW4.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********
 ) 
Here is how much of TeX's memory you used:
 20163 strings out of 476772
 423894 string characters out of 5803394
 1943839 words of memory out of 5000000
 41826 multiletter control sequences out of 15000+600000
 566134 words of font info for 90 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 84i,17n,93p,979b,440s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on HW4.pdf (20 pages).
