{"cells": [{"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["from sympy import *\n", "r, theta, phi = symbols('r theta phi', real=True)\n", "x = Function('x')(r,theta,phi)\n", "y = Function('y')(r,theta,phi)\n", "z = Function('z')(r,theta,phi)\n", "psi = Function('psi')(x,y,z)\n", "hbar = symbols('hbar')"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["x,y,z = symbols('x y z')\n", "psi = Function('psi')(x,y,z)\n", "def Lz(psi):\n", "    return -I*hbar*(x*diff(psi, y) - y*diff(psi, x))\n", "def Ly(psi):\n", "    return -I*hbar*(z*diff(psi, x) - x*diff(psi, z))\n", "def Lx(psi):\n", "    return -I*hbar*(y*diff(psi, z) - z*diff(psi, y))"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\hbar^{2} \\cdot \\left(2 \\sqrt{2} x + 2 \\sqrt{2} y + 2 z\\right)\n"]}], "source": ["print_latex(((Lz(Lz(psi)) + Ly(Ly(psi)) + Lx(Lx(psi))).simplify().subs(psi,sqrt(2)*x + sqrt(2)*y + z)).simplify())"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{\\sqrt{6} e^{i \\phi} \\sin{\\left(\\theta \\right)}}{4 \\sqrt{\\pi}}$"], "text/plain": ["-sqrt(6)*exp(I*phi)*sin(theta)/(4*sqrt(pi))"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["(psi.subs(psi,sqrt(2)*x + sqrt(2)*y + z)).subs(x,r*sin(theta)*cos(phi)).subs(y,r*sin(theta)*sin(phi)).subs(z,r*cos(theta))\n", "r, theta, phi = symbols('r theta phi')\n", "Y11 = -sqrt(3/(8*pi))*exp(I*phi)*sin(theta)\n", "Y11"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["x,y,z = symbols('x y z')\n", "r = Function('r')(x,y,z)\n", "f = Function('f')(r)\n", "PSI = (sqrt(2)*x + sqrt(2)*y + z)*f\n", "L2PSI = Lz(Lz(PSI)) + Ly(Ly(PSI)) + Lx(Lx(PSI))\n", "r, theta, phi = symbols('r theta phi',real=True)\n", "Y11 = -sqrt(3/(8*pi))*exp(I*phi)*sin(theta)\n"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- \\frac{\\sqrt{6} e^{i \\phi} \\sin{\\left(\\theta \\right)}}{4 \\sqrt{\\pi}}\n"]}], "source": ["\n", "from sympy import *\n", "r, theta, phi = symbols('r theta phi',real=True)\n", "Y11 = -sqrt(3/(8*pi))*exp(I*phi)*sin(theta)\n", "print_latex(Y11)"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["def L2(psi):\n", "    return hbar**2*((x*diff(psi,x))+(y*diff(psi,y))+(z*diff(psi,z))**2 + (x*diff(psi,x))+(y*diff(psi,y))+(z*diff(psi,z)) - (x**2+y**2+z**2)*(diff(psi,x,2)+diff(psi,y,2)+diff(psi,z,2)) )"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/IPython/core/async_helpers.py:129\u001b[0m, in \u001b[0;36m_pseudo_sync_runner\u001b[0;34m(coro)\u001b[0m\n\u001b[1;32m    121\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    122\u001b[0m \u001b[38;5;124;03mA runner that does not really allow async execution, and just advance the coroutine.\u001b[39;00m\n\u001b[1;32m    123\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    126\u001b[0m \u001b[38;5;124;03mCredit to <PERSON>\u001b[39;00m\n\u001b[1;32m    127\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    128\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 129\u001b[0m     \u001b[43mcoro\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01mN<PERSON>\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    130\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    131\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m exc\u001b[38;5;241m.\u001b[39mvalue\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/IPython/core/interactiveshell.py:3284\u001b[0m, in \u001b[0;36mInteractiveShell.run_cell_async\u001b[0;34m(self, raw_cell, store_history, silent, shell_futures, transformed_cell, preprocessing_exc_tuple, cell_id)\u001b[0m\n\u001b[1;32m   3282\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m store_history:\n\u001b[1;32m   3283\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhistory_manager \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m-> 3284\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhistory_manager\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstore_inputs\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecution_count\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcell\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mraw_cell\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3285\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m silent:\n\u001b[1;32m   3286\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlogger\u001b[38;5;241m.\u001b[39mlog(cell, raw_cell)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/IPython/core/history.py:792\u001b[0m, in \u001b[0;36mHistoryManager.store_inputs\u001b[0;34m(self, line_num, source, source_raw)\u001b[0m\n\u001b[1;32m    789\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_hist_parsed\u001b[38;5;241m.\u001b[39mappend(source)\n\u001b[1;32m    790\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_hist_raw\u001b[38;5;241m.\u001b[39mappend(source_raw)\n\u001b[0;32m--> 792\u001b[0m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdb_input_cache_lock\u001b[49m\u001b[43m:\u001b[49m\n\u001b[1;32m    793\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdb_input_cache\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mappend\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[43mline_num\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msource\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msource_raw\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    794\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# Trigger to flush cache and write to DB.\u001b[39;49;00m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["L2(f).simplify()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 2 \\pi \\left(- \\frac{\\sqrt{3} \\sqrt{\\pi}}{8} - \\frac{\\sqrt{3} i \\sqrt{\\pi}}{8}\\right)$"], "text/plain": ["2*pi*(-sqrt(3)*sqrt(pi)/8 - sqrt(3)*I*sqrt(pi)/8)"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["integrate((psi.subs(psi,sqrt(2)*x + sqrt(2)*y + z).subs(x,sin(theta)*cos(phi)).subs(y,sin(theta)*sin(phi)).subs(z,cos(theta)))*Y11, (theta,0,pi),(phi,0,2*pi))"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{\\sqrt{6} e^{- i \\phi} \\sin{\\left(\\theta \\right)}}{4 \\sqrt{\\pi}}$"], "text/plain": ["-sqrt(6)*exp(-I*phi)*sin(theta)/(4*sqrt(pi))"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["psit = (psi.subs(psi,sqrt(2)*x + sqrt(2)*y + z).subs(x,sin(theta)*cos(phi)).subs(y,sin(theta)*sin(phi)).subs(z,cos(theta)))\n", "Y11.conjugate()"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[65], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mintegrate\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpsit\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mY11\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconjugate\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexpand\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43msin\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtheta\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexpand\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43mtheta\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpi\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/integrals/integrals.py:1567\u001b[0m, in \u001b[0;36mintegrate\u001b[0;34m(meijerg, conds, risch, heurisch, manual, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1564\u001b[0m integral \u001b[38;5;241m=\u001b[39m Integral(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m   1566\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(integral, Integral):\n\u001b[0;32m-> 1567\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mintegral\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdoit\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mdoit_flags\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1568\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1569\u001b[0m     new_args \u001b[38;5;241m=\u001b[39m [a\u001b[38;5;241m.\u001b[39mdoit(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mdoit_flags) \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(a, Integral) \u001b[38;5;28;01melse\u001b[39;00m a\n\u001b[1;32m   1570\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m integral\u001b[38;5;241m.\u001b[39margs]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/integrals/integrals.py:499\u001b[0m, in \u001b[0;36mIntegral.doit\u001b[0;34m(self, **hints)\u001b[0m\n\u001b[1;32m    497\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m reps:\n\u001b[1;32m    498\u001b[0m     undo \u001b[38;5;241m=\u001b[39m {v: k \u001b[38;5;28;01mfor\u001b[39;00m k, v \u001b[38;5;129;01min\u001b[39;00m reps\u001b[38;5;241m.\u001b[39mitems()}\n\u001b[0;32m--> 499\u001b[0m     did \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mxreplace\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreps\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdoit\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mhints\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    500\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(did, \u001b[38;5;28mtuple\u001b[39m):  \u001b[38;5;66;03m# when separate=True\u001b[39;00m\n\u001b[1;32m    501\u001b[0m         did \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mtuple\u001b[39m([i\u001b[38;5;241m.\u001b[39mxreplace(undo) \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m did])\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/integrals/integrals.py:612\u001b[0m, in \u001b[0;36mIntegral.doit\u001b[0;34m(self, **hints)\u001b[0m\n\u001b[1;32m    610\u001b[0m     antideriv \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    611\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 612\u001b[0m     antideriv \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_eval_integral\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    613\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfunction\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mxab\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43meval_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    614\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m antideriv \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m meijerg \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m    615\u001b[0m         ret \u001b[38;5;241m=\u001b[39m try_meijerg(function, xab)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/integrals/integrals.py:955\u001b[0m, in \u001b[0;36mIntegral._eval_integral\u001b[0;34m(self, f, x, meijerg, risch, manual, heurisch, conds, final)\u001b[0m\n\u001b[1;32m    953\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m risch \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m:\n\u001b[1;32m    954\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 955\u001b[0m         result, i \u001b[38;5;241m=\u001b[39m \u001b[43mrisch_integrate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mseparate_integral\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    956\u001b[0m \u001b[43m            \u001b[49m\u001b[43mconds\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconds\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    957\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mNotImplementedError\u001b[39;00m:\n\u001b[1;32m    958\u001b[0m         \u001b[38;5;28;01mpass\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/integrals/risch.py:1827\u001b[0m, in \u001b[0;36mrisch_integrate\u001b[0;34m(f, x, extension, handle_first, separate_integral, rewrite_complex, conds)\u001b[0m\n\u001b[1;32m   1824\u001b[0m     fa, fd \u001b[38;5;241m=\u001b[39m frac_in((fa, fd), DE\u001b[38;5;241m.\u001b[39mt)\n\u001b[1;32m   1825\u001b[0m     \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[0;32m-> 1827\u001b[0m fa, fd \u001b[38;5;241m=\u001b[39m \u001b[43mfa\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcancel\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minclude\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m   1828\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m case \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mexp\u001b[39m\u001b[38;5;124m'\u001b[39m:\n\u001b[1;32m   1829\u001b[0m     ans, i, b \u001b[38;5;241m=\u001b[39m integrate_hyperexponential(fa, fd, DE, conds\u001b[38;5;241m=\u001b[39mconds)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/polytools.py:3856\u001b[0m, in \u001b[0;36mPoly.cancel\u001b[0;34m(f, g, include)\u001b[0m\n\u001b[1;32m   3853\u001b[0m dom, per, F, G \u001b[38;5;241m=\u001b[39m f\u001b[38;5;241m.\u001b[39m_unify(g)\n\u001b[1;32m   3855\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(F, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcancel\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[0;32m-> 3856\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[43mF\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcancel\u001b[49m\u001b[43m(\u001b[49m\u001b[43mG\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minclude\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43minclude\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3857\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:  \u001b[38;5;66;03m# pragma: no cover\u001b[39;00m\n\u001b[1;32m   3858\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m OperationNotSupported(f, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcancel\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/polyclasses.py:701\u001b[0m, in \u001b[0;36mDMP.cancel\u001b[0;34m(f, g, include)\u001b[0m\n\u001b[1;32m    698\u001b[0m lev, dom, per, F, G \u001b[38;5;241m=\u001b[39m f\u001b[38;5;241m.\u001b[39munify(g)\n\u001b[1;32m    700\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m include:\n\u001b[0;32m--> 701\u001b[0m     F, G \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_cancel\u001b[49m\u001b[43m(\u001b[49m\u001b[43mF\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mG\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlev\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdom\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minclude\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m    702\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    703\u001b[0m     cF, cG, F, G \u001b[38;5;241m=\u001b[39m dmp_cancel(F, G, lev, dom, include\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1867\u001b[0m, in \u001b[0;36mdmp_cancel\u001b[0;34m(f, g, u, K, include)\u001b[0m\n\u001b[1;32m   1864\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1865\u001b[0m     cp, cq \u001b[38;5;241m=\u001b[39m K\u001b[38;5;241m.\u001b[39mone, K\u001b[38;5;241m.\u001b[39mone\n\u001b[0;32m-> 1867\u001b[0m _, p, q \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1869\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m K0 \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   1870\u001b[0m     _, cp, cq \u001b[38;5;241m=\u001b[39m K\u001b[38;5;241m.\u001b[39mcofactors(cp, cq)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1581\u001b[0m, in \u001b[0;36mdmp_inner_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1561\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1562\u001b[0m \u001b[38;5;124;03mComputes polynomial GCD and cofactors of `f` and `g` in `K[X]`.\u001b[39;00m\n\u001b[1;32m   1563\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1578\u001b[0m \n\u001b[1;32m   1579\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1580\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m u:\n\u001b[0;32m-> 1581\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mdup_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1583\u001b[0m J, (f, g) \u001b[38;5;241m=\u001b[39m dmp_multi_deflate((f, g), u, K)\n\u001b[1;32m   1584\u001b[0m h, cff, cfg \u001b[38;5;241m=\u001b[39m _dmp_inner_gcd(f, g, u, K)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1513\u001b[0m, in \u001b[0;36mdup_inner_gcd\u001b[0;34m(f, g, K)\u001b[0m\n\u001b[1;32m   1510\u001b[0m         \u001b[38;5;28;01mexcept\u001b[39;00m HeuristicGCDFailed:\n\u001b[1;32m   1511\u001b[0m             \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[0;32m-> 1513\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdup_ff_prs_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1514\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1515\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m K\u001b[38;5;241m.\u001b[39mis_ZZ \u001b[38;5;129;01mand\u001b[39;00m query(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mUSE_HEU_GCD\u001b[39m\u001b[38;5;124m'\u001b[39m):\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1028\u001b[0m, in \u001b[0;36mdup_ff_prs_gcd\u001b[0;34m(f, g, K)\u001b[0m\n\u001b[1;32m   1025\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m result \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   1026\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m result\n\u001b[0;32m-> 1028\u001b[0m h \u001b[38;5;241m=\u001b[39m \u001b[43mdup_subresultants\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\n\u001b[1;32m   1029\u001b[0m h \u001b[38;5;241m=\u001b[39m dup_monic(h, K)\n\u001b[1;32m   1031\u001b[0m cff \u001b[38;5;241m=\u001b[39m dup_quo(f, h, K)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:402\u001b[0m, in \u001b[0;36mdup_subresultants\u001b[0;34m(f, g, K)\u001b[0m\n\u001b[1;32m    388\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdup_subresultants\u001b[39m(f, g, K):\n\u001b[1;32m    389\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    390\u001b[0m \u001b[38;5;124;03m    Computes subresultant PRS of two polynomials in `K[x]`.\u001b[39;00m\n\u001b[1;32m    391\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    400\u001b[0m \n\u001b[1;32m    401\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 402\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdup_inner_subresultants\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:372\u001b[0m, in \u001b[0;36mdup_inner_subresultants\u001b[0;34m(f, g, K)\u001b[0m\n\u001b[1;32m    368\u001b[0m f, g, m, d \u001b[38;5;241m=\u001b[39m g, h, k, m \u001b[38;5;241m-\u001b[39m k\n\u001b[1;32m    370\u001b[0m b \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m-\u001b[39mlc \u001b[38;5;241m*\u001b[39m c\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39md\n\u001b[0;32m--> 372\u001b[0m h \u001b[38;5;241m=\u001b[39m \u001b[43mdup_prem\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    373\u001b[0m h \u001b[38;5;241m=\u001b[39m dup_quo_ground(h, b, K)\n\u001b[1;32m    375\u001b[0m lc \u001b[38;5;241m=\u001b[39m dup_LC(g, K)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:1084\u001b[0m, in \u001b[0;36mdup_prem\u001b[0;34m(f, g, K)\u001b[0m\n\u001b[1;32m   1081\u001b[0m lc_r \u001b[38;5;241m=\u001b[39m dup_LC(r, K)\n\u001b[1;32m   1082\u001b[0m j, N \u001b[38;5;241m=\u001b[39m dr \u001b[38;5;241m-\u001b[39m dg, N \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m-> 1084\u001b[0m R \u001b[38;5;241m=\u001b[39m \u001b[43mdup_mul_ground\u001b[49m\u001b[43m(\u001b[49m\u001b[43mr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlc_g\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1085\u001b[0m G \u001b[38;5;241m=\u001b[39m dup_mul_term(g, lc_r, j, K)\n\u001b[1;32m   1086\u001b[0m r \u001b[38;5;241m=\u001b[39m dup_sub(R, G, K)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:272\u001b[0m, in \u001b[0;36mdup_mul_ground\u001b[0;34m(f, c, K)\u001b[0m\n\u001b[1;32m    270\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m []\n\u001b[1;32m    271\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 272\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m [ \u001b[43mcf\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mc\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m cf \u001b[38;5;129;01min\u001b[39;00m f ]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/domains/expressiondomain.py:105\u001b[0m, in \u001b[0;36mExpressionDomain.Expression.__mul__\u001b[0;34m(f, g)\u001b[0m\n\u001b[1;32m    102\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m f\u001b[38;5;241m.\u001b[39mex\u001b[38;5;241m.\u001b[39mis_Number \u001b[38;5;129;01mand\u001b[39;00m g\u001b[38;5;241m.\u001b[39mex\u001b[38;5;241m.\u001b[39mis_Number:\n\u001b[1;32m    103\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m f\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m(f\u001b[38;5;241m.\u001b[39mex\u001b[38;5;241m*\u001b[39mg\u001b[38;5;241m.\u001b[39mex)\n\u001b[0;32m--> 105\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msimplify\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mex\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mg\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mex\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/domains/expressiondomain.py:50\u001b[0m, in \u001b[0;36mExpressionDomain.Expression.simplify\u001b[0;34m(f, ex)\u001b[0m\n\u001b[1;32m     49\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21msimplify\u001b[39m(f, ex):\n\u001b[0;32m---> 50\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m f\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m(\u001b[43mex\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcancel\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mexpand(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39meflags))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/expr.py:3791\u001b[0m, in \u001b[0;36mExpr.cancel\u001b[0;34m(self, *gens, **args)\u001b[0m\n\u001b[1;32m   3789\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"See the cancel function in sympy.polys\"\"\"\u001b[39;00m\n\u001b[1;32m   3790\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msympy\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpolyto<PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m cancel\n\u001b[0;32m-> 3791\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mcancel\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mgens\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/polytools.py:6890\u001b[0m, in \u001b[0;36mcancel\u001b[0;34m(f, _signsimp, *gens, **args)\u001b[0m\n\u001b[1;32m   6887\u001b[0m                 \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[1;32m   6888\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m f\u001b[38;5;241m.\u001b[39mxreplace(\u001b[38;5;28mdict\u001b[39m(reps))\n\u001b[0;32m-> 6890\u001b[0m c, (P, Q) \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m, \u001b[43mF\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcancel\u001b[49m\u001b[43m(\u001b[49m\u001b[43mG\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   6891\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m opt\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpolys\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;28;01m<PERSON><PERSON>e\u001b[39;00m) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mgens\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m opt:\n\u001b[1;32m   6892\u001b[0m     opt[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mgens\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m R\u001b[38;5;241m.\u001b[39msymbols\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:2242\u001b[0m, in \u001b[0;36mPolyElement.cancel\u001b[0;34m(self, g)\u001b[0m\n\u001b[1;32m   2239\u001b[0m domain \u001b[38;5;241m=\u001b[39m ring\u001b[38;5;241m.\u001b[39mdomain\n\u001b[1;32m   2241\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (domain\u001b[38;5;241m.\u001b[39mis_Field \u001b[38;5;129;01mand\u001b[39;00m domain\u001b[38;5;241m.\u001b[39mhas_assoc_Ring):\n\u001b[0;32m-> 2242\u001b[0m     _, p, q \u001b[38;5;241m=\u001b[39m \u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcofactors\u001b[49m\u001b[43m(\u001b[49m\u001b[43mg\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2243\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[1;32m   2244\u001b[0m     new_ring \u001b[38;5;241m=\u001b[39m ring\u001b[38;5;241m.\u001b[39mclone(domain\u001b[38;5;241m=\u001b[39mdomain\u001b[38;5;241m.\u001b[39mget_ring())\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:2158\u001b[0m, in \u001b[0;36mPolyElement.cofactors\u001b[0;34m(f, g)\u001b[0m\n\u001b[1;32m   2155\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m h, cff, cfg\n\u001b[1;32m   2157\u001b[0m J, (f, g) \u001b[38;5;241m=\u001b[39m f\u001b[38;5;241m.\u001b[39mdeflate(g)\n\u001b[0;32m-> 2158\u001b[0m h, cff, cfg \u001b[38;5;241m=\u001b[39m \u001b[43mf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mg\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2160\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m (h\u001b[38;5;241m.\u001b[39minflate(J), cff\u001b[38;5;241m.\u001b[39minflate(J), cfg\u001b[38;5;241m.\u001b[39minflate(J))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/rings.py:2193\u001b[0m, in \u001b[0;36mPolyElement._gcd\u001b[0;34m(f, g)\u001b[0m\n\u001b[1;32m   2191\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m f\u001b[38;5;241m.\u001b[39m_gcd_ZZ(g)\n\u001b[1;32m   2192\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m: \u001b[38;5;66;03m# TODO: don't use dense representation (port PRS algorithms)\u001b[39;00m\n\u001b[0;32m-> 2193\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mring\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdmp_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/compatibility.py:663\u001b[0m, in \u001b[0;36mIPolys.dmp_inner_gcd\u001b[0;34m(self, f, g)\u001b[0m\n\u001b[1;32m    662\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdmp_inner_gcd\u001b[39m(\u001b[38;5;28mself\u001b[39m, f, g):\n\u001b[0;32m--> 663\u001b[0m     H, F, G \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mto_dense\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mto_dense\u001b[49m\u001b[43m(\u001b[49m\u001b[43mg\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mngens\u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdomain\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    664\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfrom_dense(H), \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfrom_dense(F), \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfrom_dense(G))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1584\u001b[0m, in \u001b[0;36mdmp_inner_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1581\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dup_inner_gcd(f, g, K)\n\u001b[1;32m   1583\u001b[0m J, (f, g) \u001b[38;5;241m=\u001b[39m dmp_multi_deflate((f, g), u, K)\n\u001b[0;32m-> 1584\u001b[0m h, cff, cfg \u001b[38;5;241m=\u001b[39m \u001b[43m_dmp_inner_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1586\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m (dmp_inflate(h, J, u, K),\n\u001b[1;32m   1587\u001b[0m         dmp_inflate(cff, J, u, K),\n\u001b[1;32m   1588\u001b[0m         dmp_inflate(cfg, J, u, K))\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1557\u001b[0m, in \u001b[0;36m_dmp_inner_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1554\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m HeuristicGCDFailed:\n\u001b[1;32m   1555\u001b[0m         \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[0;32m-> 1557\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdmp_rr_prs_gcd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:1068\u001b[0m, in \u001b[0;36mdmp_rr_prs_gcd\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1065\u001b[0m fc, F \u001b[38;5;241m=\u001b[39m dmp_primitive(f, u, K)\n\u001b[1;32m   1066\u001b[0m gc, G \u001b[38;5;241m=\u001b[39m dmp_primitive(g, u, K)\n\u001b[0;32m-> 1068\u001b[0m h \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_subresultants\u001b[49m\u001b[43m(\u001b[49m\u001b[43mF\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mG\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\n\u001b[1;32m   1069\u001b[0m c, _, _ \u001b[38;5;241m=\u001b[39m dmp_rr_prs_gcd(fc, gc, u \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m, K)\n\u001b[1;32m   1071\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m K\u001b[38;5;241m.\u001b[39mis_negative(dmp_ground_LC(h, u, K)):\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:549\u001b[0m, in \u001b[0;36mdmp_subresultants\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    529\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdmp_subresultants\u001b[39m(f, g, u, K):\n\u001b[1;32m    530\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    531\u001b[0m \u001b[38;5;124;03m    Computes subresultant PRS of two polynomials in `K[X]`.\u001b[39;00m\n\u001b[1;32m    532\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    547\u001b[0m \n\u001b[1;32m    548\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 549\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdmp_inner_subresultants\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/euclidtools.py:512\u001b[0m, in \u001b[0;36mdmp_inner_subresultants\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    507\u001b[0m f, g, m, d \u001b[38;5;241m=\u001b[39m g, h, k, m \u001b[38;5;241m-\u001b[39m k\n\u001b[1;32m    509\u001b[0m b \u001b[38;5;241m=\u001b[39m dmp_mul(dmp_neg(lc, v, K),\n\u001b[1;32m    510\u001b[0m             dmp_pow(c, d, v, K), v, K)\n\u001b[0;32m--> 512\u001b[0m h \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_prem\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    513\u001b[0m h \u001b[38;5;241m=\u001b[39m [ dmp_quo(ch, b, v, K) \u001b[38;5;28;01mfor\u001b[39;00m ch \u001b[38;5;129;01min\u001b[39;00m h ]\n\u001b[1;32m    515\u001b[0m lc \u001b[38;5;241m=\u001b[39m dmp_LC(g, K)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:1238\u001b[0m, in \u001b[0;36mdmp_prem\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m   1235\u001b[0m j, N \u001b[38;5;241m=\u001b[39m dr \u001b[38;5;241m-\u001b[39m dg, N \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m   1237\u001b[0m R \u001b[38;5;241m=\u001b[39m dmp_mul_term(r, lc_g, \u001b[38;5;241m0\u001b[39m, u, K)\n\u001b[0;32m-> 1238\u001b[0m G \u001b[38;5;241m=\u001b[39m \u001b[43mdmp_mul_term\u001b[49m\u001b[43m(\u001b[49m\u001b[43mg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlc_r\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mj\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mu\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1239\u001b[0m r \u001b[38;5;241m=\u001b[39m dmp_sub(R, G, u, K)\n\u001b[1;32m   1241\u001b[0m _dr, dr \u001b[38;5;241m=\u001b[39m dr, dmp_degree(r, u)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:184\u001b[0m, in \u001b[0;36mdmp_mul_term\u001b[0;34m(f, c, i, u, K)\u001b[0m\n\u001b[1;32m    182\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dmp_zero(u)\n\u001b[1;32m    183\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 184\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m [ \u001b[43mdmp_mul\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m cf \u001b[38;5;129;01min\u001b[39;00m f ] \u001b[38;5;241m+\u001b[39m dmp_zeros(i, v, K)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:828\u001b[0m, in \u001b[0;36mdmp_mul\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    825\u001b[0m     coeff \u001b[38;5;241m=\u001b[39m dmp_zero(v)\n\u001b[1;32m    827\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m j \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mmax\u001b[39m(\u001b[38;5;241m0\u001b[39m, i \u001b[38;5;241m-\u001b[39m dg), \u001b[38;5;28mmin\u001b[39m(df, i) \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m):\n\u001b[0;32m--> 828\u001b[0m         coeff \u001b[38;5;241m=\u001b[39m dmp_add(coeff, \u001b[43mdmp_mul\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m[\u001b[49m\u001b[43mj\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mj\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m, v, K)\n\u001b[1;32m    830\u001b[0m     h\u001b[38;5;241m.\u001b[39mappend(coeff)\n\u001b[1;32m    832\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m dmp_strip(h, u)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:828\u001b[0m, in \u001b[0;36mdmp_mul\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    825\u001b[0m     coeff \u001b[38;5;241m=\u001b[39m dmp_zero(v)\n\u001b[1;32m    827\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m j \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mmax\u001b[39m(\u001b[38;5;241m0\u001b[39m, i \u001b[38;5;241m-\u001b[39m dg), \u001b[38;5;28mmin\u001b[39m(df, i) \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m):\n\u001b[0;32m--> 828\u001b[0m         coeff \u001b[38;5;241m=\u001b[39m dmp_add(coeff, \u001b[43mdmp_mul\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m[\u001b[49m\u001b[43mj\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mg\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mj\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mK\u001b[49m\u001b[43m)\u001b[49m, v, K)\n\u001b[1;32m    830\u001b[0m     h\u001b[38;5;241m.\u001b[39mappend(coeff)\n\u001b[1;32m    832\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m dmp_strip(h, u)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/polys/densearith.py:819\u001b[0m, in \u001b[0;36mdmp_mul\u001b[0;34m(f, g, u, K)\u001b[0m\n\u001b[1;32m    815\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m f\n\u001b[1;32m    817\u001b[0m dg \u001b[38;5;241m=\u001b[39m dmp_degree(g, u)\n\u001b[0;32m--> 819\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mdg\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m<\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m:\n\u001b[1;32m    820\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m g\n\u001b[1;32m    822\u001b[0m h, v \u001b[38;5;241m=\u001b[39m [], u \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/decorators.py:236\u001b[0m, in \u001b[0;36m_SympifyWrapper.make_wrapped.<locals>._func\u001b[0;34m(self, other)\u001b[0m\n\u001b[1;32m    234\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(other, expectedcls):\n\u001b[1;32m    235\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m retval\n\u001b[0;32m--> 236\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mother\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/expr.py:376\u001b[0m, in \u001b[0;36mExpr.__lt__\u001b[0;34m(self, other)\u001b[0m\n\u001b[1;32m    373\u001b[0m \u001b[38;5;129m@sympify_return\u001b[39m([(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mother\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mExpr\u001b[39m\u001b[38;5;124m'\u001b[39m)], \u001b[38;5;28mNotImplemented\u001b[39m)\n\u001b[1;32m    374\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__lt__\u001b[39m(\u001b[38;5;28mself\u001b[39m, other):\n\u001b[1;32m    375\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mrelational\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Strict<PERSON>ess<PERSON>han\n\u001b[0;32m--> 376\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mStric<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mother\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/relational.py:833\u001b[0m, in \u001b[0;36m_Inequality.__new__\u001b[0;34m(cls, lhs, rhs, **options)\u001b[0m\n\u001b[1;32m    824\u001b[0m             \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mInvalid NaN comparison\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    825\u001b[0m     \u001b[38;5;66;03m# First we invoke the appropriate inequality method of `lhs`\u001b[39;00m\n\u001b[1;32m    826\u001b[0m     \u001b[38;5;66;03m# (e.g., `lhs.__lt__`).  That method will try to reduce to\u001b[39;00m\n\u001b[1;32m    827\u001b[0m     \u001b[38;5;66;03m# boolean or raise an exception.  It may keep calling\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    831\u001b[0m     \u001b[38;5;66;03m# exception).  In that case, it must call us with\u001b[39;00m\n\u001b[1;32m    832\u001b[0m     \u001b[38;5;66;03m# `evaluate=False` to prevent infinite recursion.\u001b[39;00m\n\u001b[0;32m--> 833\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_eval_relation\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlhs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrhs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    835\u001b[0m \u001b[38;5;66;03m# make a \"non-evaluated\" Expr for the inequality\u001b[39;00m\n\u001b[1;32m    836\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m Relational\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__new__\u001b[39m(\u001b[38;5;28mcls\u001b[39m, lhs, rhs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/relational.py:840\u001b[0m, in \u001b[0;36m_Inequality._eval_relation\u001b[0;34m(cls, lhs, rhs, **options)\u001b[0m\n\u001b[1;32m    838\u001b[0m \u001b[38;5;129m@classmethod\u001b[39m\n\u001b[1;32m    839\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_eval_relation\u001b[39m(\u001b[38;5;28mcls\u001b[39m, lhs, rhs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions):\n\u001b[0;32m--> 840\u001b[0m     val \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_eval_fuzzy_relation\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlhs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrhs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    841\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m val \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m    842\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mcls\u001b[39m(lhs, rhs, evaluate\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/relational.py:1167\u001b[0m, in \u001b[0;36mStrictLessThan._eval_fuzzy_relation\u001b[0;34m(cls, lhs, rhs)\u001b[0m\n\u001b[1;32m   1165\u001b[0m \u001b[38;5;129m@classmethod\u001b[39m\n\u001b[1;32m   1166\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_eval_fuzzy_relation\u001b[39m(\u001b[38;5;28mcls\u001b[39m, lhs, rhs):\n\u001b[0;32m-> 1167\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mis_lt\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlhs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrhs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/relational.py:1246\u001b[0m, in \u001b[0;36mis_lt\u001b[0;34m(lhs, rhs, assumptions)\u001b[0m\n\u001b[1;32m   1241\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mis_lt\u001b[39m(lhs, rhs, assumptions\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m   1242\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Fuzzy bool for lhs is strictly less than rhs.\u001b[39;00m\n\u001b[1;32m   1243\u001b[0m \n\u001b[1;32m   1244\u001b[0m \u001b[38;5;124;03m    See the docstring for :func:`~.is_ge` for more.\u001b[39;00m\n\u001b[1;32m   1245\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1246\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m fuzzy_not(\u001b[43mis_ge\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlhs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrhs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43massumptions\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/relational.py:1368\u001b[0m, in \u001b[0;36mis_ge\u001b[0;34m(lhs, rhs, assumptions)\u001b[0m\n\u001b[1;32m   1366\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m retval\n\u001b[1;32m   1367\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1368\u001b[0m     n2 \u001b[38;5;241m=\u001b[39m \u001b[43m_n2\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlhs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrhs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1369\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m n2 \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   1370\u001b[0m         \u001b[38;5;66;03m# use float comparison for infinity.\u001b[39;00m\n\u001b[1;32m   1371\u001b[0m         \u001b[38;5;66;03m# otherwise get stuck in infinite recursion\u001b[39;00m\n\u001b[1;32m   1372\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m n2 \u001b[38;5;129;01min\u001b[39;00m (S\u001b[38;5;241m.\u001b[39mInfinity, S\u001b[38;5;241m.\u001b[39mNegativeInfinity):\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/relational.py:1203\u001b[0m, in \u001b[0;36m_n2\u001b[0;34m(a, b)\u001b[0m\n\u001b[1;32m   1200\u001b[0m \u001b[38;5;66;03m# /!\\ it is very important (see issue 8245) not to\u001b[39;00m\n\u001b[1;32m   1201\u001b[0m \u001b[38;5;66;03m# use a re-evaluated number in the calculation of dif\u001b[39;00m\n\u001b[1;32m   1202\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m a\u001b[38;5;241m.\u001b[39mis_comparable \u001b[38;5;129;01mand\u001b[39;00m b\u001b[38;5;241m.\u001b[39mis_comparable:\n\u001b[0;32m-> 1203\u001b[0m     dif \u001b[38;5;241m=\u001b[39m \u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mb\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mevalf\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1204\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m dif\u001b[38;5;241m.\u001b[39mis_comparable:\n\u001b[1;32m   1205\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m dif\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/numbers.py:3489\u001b[0m, in \u001b[0;36mNegativeInfinity.evalf\u001b[0;34m(self, prec, **options)\u001b[0m\n\u001b[1;32m   3488\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mevalf\u001b[39m(\u001b[38;5;28mself\u001b[39m, prec\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions):\n\u001b[0;32m-> 3489\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_eval_evalf\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprec\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/numbers.py:3486\u001b[0m, in \u001b[0;36mNegativeInfinity._eval_evalf\u001b[0;34m(self, prec)\u001b[0m\n\u001b[1;32m   3485\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_eval_evalf\u001b[39m(\u001b[38;5;28mself\u001b[39m, prec\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m-> 3486\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mFloat\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m-inf\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/sympy/core/numbers.py:1045\u001b[0m, in \u001b[0;36mFloat.__new__\u001b[0;34m(cls, num, dps, precision)\u001b[0m\n\u001b[1;32m   1042\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(num, \u001b[38;5;28mstr\u001b[39m):\n\u001b[1;32m   1043\u001b[0m     \u001b[38;5;66;03m# Float accepts spaces as digit separators\u001b[39;00m\n\u001b[1;32m   1044\u001b[0m     num \u001b[38;5;241m=\u001b[39m num\u001b[38;5;241m.\u001b[39mreplace(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m \u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;241m.\u001b[39mlower()\n\u001b[0;32m-> 1045\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mnum\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstartswith\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m.\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(num) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m   1046\u001b[0m         num \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m0\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;241m+\u001b[39m num\n\u001b[1;32m   1047\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m num\u001b[38;5;241m.\u001b[39mstartswith(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-.\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(num) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m2\u001b[39m:\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["integrate((psit*Y11.conjugate().expand()*sin(theta)).expand(), (theta, 0, pi))"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\sin^{3}{\\left(\\theta \\right)}}{3}$"], "text/plain": ["sin(theta)**3/3"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["integrate(sin(theta)**2*cos(theta),theta)"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["bruh = (sin(theta)*psit*Y11.conjugate()).expand()"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\phi \\left(\\sqrt{3} \\pi \\left(-1 + i\\right) e^{7 i \\theta} + \\sqrt{3} \\pi \\left(9 - 9 i\\right) e^{5 i \\theta} + \\sqrt{3} \\pi \\left(9 - 9 i\\right) e^{3 i \\theta} + \\sqrt{3} \\pi \\left(-1 + i\\right) e^{i \\theta}\\right) e^{- 4 i \\theta}}{96 \\pi^{\\frac{3}{2}}} + \\frac{\\left(\\left(192 \\sqrt{6} \\pi^{\\frac{5}{2}} e^{11 i \\theta} - 576 \\sqrt{6} \\pi^{\\frac{5}{2}} e^{9 i \\theta} + 576 \\sqrt{6} \\pi^{\\frac{5}{2}} e^{7 i \\theta} - 192 \\sqrt{6} \\pi^{\\frac{5}{2}} e^{5 i \\theta}\\right) e^{- i \\phi} + \\left(\\sqrt{3} \\pi^{\\frac{5}{2}} \\cdot \\left(96 - 96 i\\right) e^{11 i \\theta} + \\sqrt{3} \\pi^{\\frac{5}{2}} \\left(-864 + 864 i\\right) e^{9 i \\theta} + \\sqrt{3} \\pi^{\\frac{5}{2}} \\left(-864 + 864 i\\right) e^{7 i \\theta} + \\sqrt{3} \\pi^{\\frac{5}{2}} \\cdot \\left(96 - 96 i\\right) e^{5 i \\theta}\\right) e^{- 2 i \\phi}\\right) e^{- 8 i \\theta}}{18432 \\pi^{3}}$"], "text/plain": ["phi*(sqrt(3)*pi*(-1 + I)*exp(7*I*theta) + sqrt(3)*pi*(9 - 9*I)*exp(5*I*theta) + sqrt(3)*pi*(9 - 9*I)*exp(3*I*theta) + sqrt(3)*pi*(-1 + I)*exp(I*theta))*exp(-4*I*theta)/(96*pi**(3/2)) + ((192*sqrt(6)*pi**(5/2)*exp(11*I*theta) - 576*sqrt(6)*pi**(5/2)*exp(9*I*theta) + 576*sqrt(6)*pi**(5/2)*exp(7*I*theta) - 192*sqrt(6)*pi**(5/2)*exp(5*I*theta))*exp(-I*phi) + (sqrt(3)*pi**(5/2)*(96 - 96*I)*exp(11*I*theta) + sqrt(3)*pi**(5/2)*(-864 + 864*I)*exp(9*I*theta) + sqrt(3)*pi**(5/2)*(-864 + 864*I)*exp(7*I*theta) + sqrt(3)*pi**(5/2)*(96 - 96*I)*exp(5*I*theta))*exp(-2*I*phi))*exp(-8*I*theta)/(18432*pi**3)"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["integrate(bruh.subs(sin(theta)**3,integrate(sin(theta)**3, theta)).subs(sin(theta)**2*cos(theta),integrate(sin(theta)**2*cos(theta), theta)), phi)"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\cos^{3}{\\left(\\theta \\right)}}{3} - \\cos{\\left(\\theta \\right)}$"], "text/plain": ["cos(theta)**3/3 - cos(theta)"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["integrate(sin(theta)**3, theta)"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle - \\frac{\\phi \\left(2 \\sqrt{3} \\pi \\left(9 - 9 i\\right) + 2 \\sqrt{3} \\pi \\left(-1 + i\\right)\\right)}{96 \\pi^{\\frac{3}{2}}} + \\frac{\\phi \\left(- 2 \\sqrt{3} \\pi \\left(-1 + i\\right) - 2 \\sqrt{3} \\pi \\left(9 - 9 i\\right)\\right)}{96 \\pi^{\\frac{3}{2}}} - \\frac{\\left(2 \\sqrt{3} \\pi^{\\frac{5}{2}} \\cdot \\left(96 - 96 i\\right) + 2 \\sqrt{3} \\pi^{\\frac{5}{2}} \\left(-864 + 864 i\\right)\\right) e^{- 2 i \\phi}}{18432 \\pi^{3}} + \\frac{\\left(- 2 \\sqrt{3} \\pi^{\\frac{5}{2}} \\left(-864 + 864 i\\right) - 2 \\sqrt{3} \\pi^{\\frac{5}{2}} \\cdot \\left(96 - 96 i\\right)\\right) e^{- 2 i \\phi}}{18432 \\pi^{3}}$"], "text/plain": ["-phi*(2*sqrt(3)*pi*(9 - 9*I) + 2*sqrt(3)*pi*(-1 + I))/(96*pi**(3/2)) + phi*(-2*sqrt(3)*pi*(-1 + I) - 2*sqrt(3)*pi*(9 - 9*I))/(96*pi**(3/2)) - (2*sqrt(3)*pi**(5/2)*(96 - 96*I) + 2*sqrt(3)*pi**(5/2)*(-864 + 864*I))*exp(-2*I*phi)/(18432*pi**3) + (-2*sqrt(3)*pi**(5/2)*(-864 + 864*I) - 2*sqrt(3)*pi**(5/2)*(96 - 96*I))*exp(-2*I*phi)/(18432*pi**3)"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["(integrate(bruh.subs(sin(theta)**3,integrate(sin(theta)**3, theta)).subs(sin(theta)**2*cos(theta),integrate(sin(theta)**2*cos(theta), theta)), phi).subs(theta,pi)-integrate(bruh.subs(sin(theta)**3,integrate(sin(theta)**3, theta)).subs(sin(theta)**2*cos(theta),integrate(sin(theta)**2*cos(theta), theta)), phi).subs(theta,0))"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{8 \\pi}{3}$"], "text/plain": ["8*pi/3"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["(((((integrate(bruh.subs(sin(theta)**3,integrate(sin(theta)**3, theta)).subs(sin(theta)**2*cos(theta),integrate(sin(theta)**2*cos(theta), theta)), phi).subs(theta,pi)-integrate(bruh.subs(sin(theta)**3,integrate(sin(theta)**3, theta)).subs(sin(theta)**2*cos(theta),integrate(sin(theta)**2*cos(theta), theta)), phi).subs(theta,0))).subs(phi,2*pi) - (integrate(bruh.subs(sin(theta)**3,integrate(sin(theta)**3, theta)).subs(sin(theta)**2*cos(theta),integrate(sin(theta)**2*cos(theta), theta)), phi).subs(theta,pi)-integrate(bruh.subs(sin(theta)**3,integrate(sin(theta)**3, theta)).subs(sin(theta)**2*cos(theta),integrate(sin(theta)**2*cos(theta), theta)), phi).subs(theta,0)).subs(phi,0)).expand())*((((integrate(bruh.subs(sin(theta)**3,integrate(sin(theta)**3, theta)).subs(sin(theta)**2*cos(theta),integrate(sin(theta)**2*cos(theta), theta)), phi).subs(theta,pi)-integrate(bruh.subs(sin(theta)**3,integrate(sin(theta)**3, theta)).subs(sin(theta)**2*cos(theta),integrate(sin(theta)**2*cos(theta), theta)), phi).subs(theta,0))).subs(phi,2*pi) - (integrate(bruh.subs(sin(theta)**3,integrate(sin(theta)**3, theta)).subs(sin(theta)**2*cos(theta),integrate(sin(theta)**2*cos(theta), theta)), phi).subs(theta,pi)-integrate(bruh.subs(sin(theta)**3,integrate(sin(theta)**3, theta)).subs(sin(theta)**2*cos(theta),integrate(sin(theta)**2*cos(theta), theta)), phi).subs(theta,0)).subs(phi,0)).expand()).conjugate()).expand()"]}, {"cell_type": "code", "execution_count": 104, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "incomplete input (1844840553.py, line 2)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[104], line 2\u001b[0;36m\u001b[0m\n\u001b[0;31m    bruh.subs(sin(theta)**3, integrate((sin(theta)**3, (theta, 0, pi))).subs(sin(theta)**2*cos(theta), integrate((sin(theta)**2*cos(theta), (theta, 0, pi)))\u001b[0m\n\u001b[0m                                                                                                                                                            ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m incomplete input\n"]}], "source": ["bruh = (sin(theta)*psit*Y11.conjugate()).expand()\n", "bruh.subs(sin(theta)**3, integrate((sin(theta)**3, (theta, 0, pi))).subs(sin(theta)**2*cos(theta), integrate((sin(theta)**2*cos(theta), (theta, 0, pi)))"]}, {"cell_type": "code", "execution_count": 125, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{2}{5}$"], "text/plain": ["2/5"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["abs(integrate(bruh.subs(sin(theta)**3, integrate(sin(theta)**3, (theta,0,pi))).subs(sin(theta)**2*cos(theta), integrate(sin(theta)**2*cos(theta), (theta,0,pi))),(phi,0,2*pi)))**2*A**2"]}, {"cell_type": "code", "execution_count": 126, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{3}{20 \\pi}$"], "text/plain": ["3/(20*pi)"]}, "execution_count": 126, "metadata": {}, "output_type": "execute_result"}], "source": ["A = 1/(sqrt(integrate(abs(psit)**2*sin(theta), (theta, 0, pi),(phi, 0, 2*pi)))).expand()\n", "A**2 "]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAzMAAAK9CAYAAAAQZjKqAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/H5lhTAAAACXBIWXMAAA9hAAAPYQGoP6dpAACndElEQVR4nO3deXxU5dk//s9kD1nZA5XVhU1ZRKW4V5FFH6vVuhUrUoutBVGpbaW1AtoK1g2tCtWniBtV21/10X4VRazWBTcsVluwIFhRCIiQFcg6vz/SGTLJTDLnnHs/n/frlVfLMHPmzESS85nruq87Eo1GoyAiIiIiIrJMhu4TICIiIiIi8oNhhoiIiIiIrMQwQ0REREREVmKYISIiIiIiKzHMEBERERGRlRhmiIiIiIjISgwzRERERERkJYYZIiIiIiKyEsMMERERERFZiWGGiMiDSCSC+fPnCz3m8uXLEYlE8Omnnwo9LhERkesYZohIiXfffRezZs3CiBEjUFBQgP79++P888/Hv//973b3PfnkkxGJRBCJRJCRkYHi4mIMGTIE3/3ud7Fq1SpPz/vss8/ipJNOQq9evdClSxcMHjwY559/PlauXCnqpaXt5ptvxtNPP638eTtSX1+Pu+66C2PGjEFxcTFKS0sxYsQIXH755diwYYPu0yMiIupQJBqNRnWfBBG579vf/jbeeOMNnHfeeRg5ciTKy8txzz33oKamBm+99RYOP/zw+H1PPvlkfPLJJ1i4cCEAoLa2Fps2bcKf//xnbN68Geeffz4effRRZGdnd/ict912G37yk5/gpJNOwllnnYUuXbpg06ZNeOmllzBq1CgsX77c8+uIRCKYN2+er+pMYWEhvv3tb7d73qamJjQ0NCA3NxeRSMTzcYM488wz8fzzz+Oiiy7C+PHj0dDQgA0bNuAvf/kLbrrpJlx66aVKz4eIiMiLLN0nQEThMGfOHKxYsQI5OTnx2y644AIcccQRWLRoER599NGE+5eUlODiiy9OuG3RokWYPXs27rvvPgwcOBC33HJLyudrbGzETTfdhNNOOw0vvvhiu7/fuXNnwFckTmZmJjIzM5U/77vvvou//OUv+PWvf42f//znCX93zz33oKKiQvk5ERERecE2MyJS4thjj00IMgBw6KGHYsSIEVi/fn1ax8jMzMTdd9+N4cOH45577kFlZWXK++7atQtVVVU47rjjkv59r169Ev68c+dOXHbZZejduzfy8vIwatQoPPTQQ52e06WXXoqBAwe2u33+/PkJVZZIJILa2lo89NBD8Ra6WNUj1ZqZ++67DyNGjEBubi769u2LmTNntgsYJ598Mg4//HD861//wje+8Q106dIFX/va1/Cb3/ym03P/5JNPACDpe5SZmYnu3bu3ez0bNmzA+eefj+LiYnTv3h1XXXUV9u/fn/DYBx98EKeccgp69eqF3NxcDB8+HEuWLEl6Ds8//zxOOukkFBUVobi4GEcffTRWrFiRcJ+3334bkydPRklJCbp06YKTTjoJb7zxRqevj4iI3McwQ0TaRKNR7NixAz169Ej7MZmZmbjooouwd+9evP766ynv16tXL+Tn5+PZZ5/F7t27Ozzmvn37cPLJJ+ORRx7B1KlTceutt6KkpASXXnop7rrrrrTPrSOPPPIIcnNzccIJJ+CRRx7BI488gh/84Acp7z9//nzMnDkTffv2xe23345zzz0Xv/vd7zBx4kQ0NDQk3HfPnj2YPHkyRo0ahdtvvx1Dhw7Fz372Mzz//PMdntOAAQMAAI899hgaGxvTeh3nn38+9u/fj4ULF+L000/H3XffjcsvvzzhPkuWLMGAAQPw85//HLfffjv69euHH/3oR7j33nsT7rd8+XKcccYZ2L17N+bOnYtFixZh9OjRCeuZXn75ZZx44omoqqrCvHnzcPPNN6OiogKnnHIK3nnnnbTOmYiIHBYlItLkkUceiQKI/v73v0+4/aSTToqOGDEi5eOeeuqpKIDoXXfd1eHxb7jhhiiAaEFBQXTKlCnRX//619G1a9e2u9/ixYujAKKPPvpo/Lb6+vro+PHjo4WFhdGqqqr47QCi8+bNi/952rRp0QEDBrQ75rx586Jtf8QWFBREp02b1u6+Dz74YBRAdMuWLdFoNBrduXNnNCcnJzpx4sRoU1NT/H733HNPFEB02bJl8dtOOumkKIDoww8/HL+trq4uWlZWFj333HNTvjfRaDTa3Nwcf3zv3r2jF110UfTee++N/uc//0n5er75zW8m3P6jH/0oCiD6wQcfxG/bu3dvu8dPmjQpOnjw4PifKyoqokVFRdFx48ZF9+3b1+68Yv976KGHRidNmhS/LXb8QYMGRU877bQOXx8REbmPlRki0mLDhg2YOXMmxo8fj2nTpnl6bGFhIQCgurq6w/stWLAAK1aswJgxY/DCCy/gF7/4BcaOHYsjjzwyobXtueeeQ1lZGS666KL4bdnZ2Zg9ezZqamrw6quvejq/oF566SXU19fj6quvRkbGgR/TM2bMQHFxMf7f//t/CfcvLCxMWF+Uk5ODY445Bps3b+7weSKRCF544QX86le/QteuXfGHP/wBM2fOxIABA3DBBRckXTMzc+bMhD9feeWVAFrew5j8/Pz4/6+srMSuXbtw0kknYfPmzfHWwFWrVqG6uhrXXXcd8vLy2p0XAKxbtw4bN27Ed77zHXz11VfYtWsXdu3ahdraWpx66qn429/+hubm5g5fIxERuY1hhoiUKy8vxxlnnIGSkhL86U9/8rz4vaamBgBQVFTU6X0vuugivPbaa9izZw9efPFFfOc738Hf//53nHnmmfG1Hv/5z39w6KGHJgQHABg2bFj871WKPd+QIUMSbs/JycHgwYPbnc9BBx3Ubgpa165dsWfPnk6fKzc3F7/4xS+wfv16bNu2DX/4wx/w9a9/HU8++SRmzZrV7v6HHnpowp8PPvhgZGRkJKz3eeONNzBhwgQUFBSgtLQUPXv2jA8YiIWZ2Hqd1lPs2tq4cSMAYNq0aejZs2fC1//+7/+irq6uw3VTRETkPk4zIyKlKisrMWXKFFRUVOC1115D3759PR/jo48+AgAccsghaT+muLgYp512Gk477TRkZ2fjoYcewttvv42TTjrJ8/O3lmqUclNTU6DjepEqDEY9Tt7v06cPLrzwQpx77rkYMWIEnnzySSxfvhxZWal/VbR9/Z988glOPfVUDB06FHfccQf69euHnJwcPPfcc7jzzjs9VVJi97311lsxevTopPeJVemIiCicGGaISJn9+/fjzDPPxL///W+89NJLGD58uOdjNDU1YcWKFejSpQuOP/54X+dx1FFH4aGHHsL27dsBtCyE/8c//oHm5uaE6kxs08jYQvlkunbtmrQdK1k1J909ZGLP9/HHH2Pw4MHx2+vr67FlyxZMmDAhreP4lZ2djZEjR2Ljxo3YtWsXysrK4n+3ceNGDBo0KP7nTZs2obm5OT7R7dlnn0VdXR2eeeYZ9O/fP36/v/71rwnPcfDBBwNoCaapQmnsPsXFxdJfMxER2YltZkSkRFNTEy644AKsWbMGf/zjHzF+/Hhfx5g9ezbWr1+P2bNno7i4OOV99+7dizVr1iT9u9iUr1gb1+mnn47y8nI88cQT8fs0Njbit7/9LQoLCzus3hx88MGorKzEP/7xj/ht27dvx1NPPdXuvgUFBWnt3TJhwgTk5OTg7rvvTqiu/P73v0dlZSXOOOOMTo+Rjo0bN+Kzzz5rd3tFRQXWrFmDrl27omfPngl/13Yi2W9/+1sAwJQpUwAcqBK1Pu/Kyko8+OCDCY+bOHEiioqKsHDhwnajnWOPHTt2LA4++GDcdttt8dbC1r788su0XicREbmLlRkiUuLHP/4xnnnmGZx55pnYvXt3u00y226QWVlZGb/P3r17sWnTJvz5z3/GJ598ggsvvBA33XRTh8+3d+9eHHvssfj617+OyZMno1+/fqioqMDTTz+N1157DWeffTbGjBkDALj88svxu9/9DpdeeinWrl2LgQMH4k9/+hPeeOMNLF68uMO1ORdeeCF+9rOf4Vvf+hZmz56NvXv3YsmSJTjssMPw/vvvJ9x37NixeOmll3DHHXegb9++GDRoEMaNG9fumD179sTcuXOxYMECTJ48Gd/85jfx8ccf47777sPRRx/d7r3y64MPPsB3vvMdTJkyBSeccAK6deuGL774Ag899BC2bduGxYsXt2th27JlC775zW9i8uTJWLNmDR599FF85zvfwahRowC0hJScnByceeaZ+MEPfoCamho88MAD6NWrV7wSBrRUW+688058//vfx9FHH43vfOc76Nq1Kz744APs3bsXDz30EDIyMvC///u/mDJlCkaMGIHp06fja1/7Gr744gv89a9/RXFxMZ599lkh7wUREVlK7zA1IgqL2AjgVF8d3bewsDB66KGHRi+++OLoiy++mNbzNTQ0RB944IHo2WefHR0wYEA0Nzc32qVLl+iYMWOit956a7Suri7h/jt27IhOnz492qNHj2hOTk70iCOOiD744IPtjos2o5mj0Wj0xRdfjB5++OHRnJyc6JAhQ6KPPvpo0tHMGzZsiJ544onR/Pz8KID4mOa2o5lj7rnnnujQoUOj2dnZ0d69e0evuOKK6J49e9q9V8nGWKcaGd32NS9atCh60kknRfv06RPNysqKdu3aNXrKKadE//SnPyXcN/Z6/vWvf0W//e1vR4uKiqJdu3aNzpo1q91o5WeeeSY6cuTIaF5eXnTgwIHRW265Jbps2bKkr/GZZ56JHnvssdH8/PxocXFx9Jhjjon+4Q9/SLjP3//+9+g555wT7d69ezQ3Nzc6YMCA6Pnnnx9dvXp1h6+PiIjcF4lGPa4QJSKi0Jk/fz4WLFiAL7/80tMmp0RERDJxzQwREREREVmJYYaIiIiIiKzEMENERERERFbimhkiIiIiIrISKzNERERERGQlhhkiIiIiIrKS1ZtmNjc3Y9u2bSgqKkIkEtF9OkRERETURjQaRXV1Nfr27YuMDPM+R9+/fz/q6+u1PHdOTg7y8vK0PLcrrA4z27ZtQ79+/XSfBhERERF1YuvWrTjooIN0n0aC/fv3Y+CAAuzY2azl+cvKyrBlyxYGmgCsDjNFRUUAgIl/vgTZBTlpP25HbaGn56ncm/5/YPtrc9M/cE36b39WbfqfZGTWpl+lyq5J+64t969N/745Nd5/MORUNnl+DADkVDX4ehwAZFbv9/3YZCIV1UKPJ0Lzngrhx8zoWir8mEFFS4uEHq+pKNgvl/ribH+PK8n0dv9Cb590NhR4uK+HH5dNBenPk2ks8PDzobAx7bvmFdSlfd+SLt7+7fcu8PgDE8DX8io9P6Zf/leeH9PawOwvAz2+tf7Zu4UdK6iBWernFX3aaE7Xx2cN3YQd69OGnoEev3Vfd0/3r69twPLTn45ft5mkvr4eO3Y246N3y1BUpLZqVF3djMOPLkd9fT3DTABWh5lYa1l2QY6nMJMFD4EDQCbS/w8so9nDsZvSf/szmjyEGQ8/fDM9ZIDsGgDpv83IyvYeZrKyvYeZnMoGIMvbxV9rmZnifkFG9lQDGd7++5Klefee+P/PiHj4xqWrYu+B43frKv74flTVI9pV3C/LSFawXy7N2f7CTHO2t/+em3K8/QL28mPKy32jeen/W8rI9/DzIT/9MJPZJf2ff1kewhcAZBd4a0M5KL8CgPf/BvLy/f9qHpyzE4D/n4etDcz+CiYsrR0cDzHqg8XI//7vZgNCTZcGMd9XABiO3dhc38v343My/f1sM3lJQFFRBooVhxkSw+owQ27xW5UJIrNqn7BjRfbor8i0DjA6nteYUCNIZtU+NBXn6z6NcPNQlckvTL8q41VZgZp/3wPydyl5HtMN1lCF6UjsfHSGmoHZX+HTBm8VEaIwYJhxTFaNnBYzr+1oudVqek9zKgO0lzkUZHSFmLZMCDWRPdVCqzMkXmOhnt701koLxP37T6alKqNWS1VGjJaqjHqmhZi2BmdFjajSENEBDDMWyKpxv+zppyoTJMiIpDPImBJi2jIh1FByXtbAeNFYaPZFaBA2VGVsDzKmh5jWdFZpWJ0has/9q+SAKmoltZh4WPxvG1VVmSBEVmV0aN69x9gg05quc9RdKXOFrOAjg8wWM690VGVEYZBJn63nTeQahhmB9tWYsfA7HTJbzLxSXZWxub3MlhDTmm3nSwbxsF7GC9ktZn6YUJVRHWQGZ0WtDwS2nz+RCxhmHOJlvQwFpyPI2Mrmc7e9ikfBeG0xU12VsTHIuBBiWlP9WkR9r0S2JhLpxDBjONvWy3htMbO1KqMyyNhYjUlG9eswodXMlHVdJjFh8b9pwjTBzKUQ05qrryuVMP03S+az60qZlJPdYmYj1UHGNS6+JtLLy3oZE1vM/LKtKuP6Bb/rr4/IVAwzISQroISlKqOKyxf9Lr+2MJIyyUzSehkvVLSY6f6Em0FGLFWvU9fobCITuTtSy2QSJplxvUzHbGov44W+ONxzhlwloiqj4oI4LCGmNe5FQ6QWKzNEaWKQEStMr1WmuiL+GE+XzBYzG6sysoUxyMSE+bUTqRa634LltXI+pZUxllnG4n9TRjLbvEmmLGG8uA/ja7aFjD1mZCz+l7W/jKqNMv2woSrDi3k7cKIZuSB0YYbkMHmjTBEtZrKrMryol0fE98629VZkPperMgwyLWS/D1w3Q9SCYYas4HJVJuxBJuyvn5IwYPG/Fyr3ljG9KsMgk4jvB5F8DDMdqKjN130KaZGx+N+0FjO/TK/K8EK+Bd8HNWS0jtnCy3oZk1vMguKn+UTkGoYZ1SRMMtPN5BazoBhkiDomZSxzmmStl5HNb4uZ6esbWIVIju8LkVwMMyFi6waYflvMuM7BLjLDncqNTolMxfYy97DSRsQwQ4qpbDELilUZInFkTDIzgcr1MqZikOkc3yMieRhmDCVjLHOYmFyVYZBJje8NyWDSehnXWsx4kU5EuvGK2XK6F//LXi+ja4qZrKoML9aJOmHZJDNbsB1JPwY/IjkYZojIKAx8lA4TFv+rajFjVYZkMvW/L6J0aQ8zX3zxBS6++GJ0794d+fn5OOKII/Dee+/pPi3nmLD4X9V6maAtZqzKuCno99Xk1kXRwjzCWSZdG2XKqMowyJiDVTcKO61zgvfs2YPjjjsO3/jGN/D888+jZ8+e2LhxI7p27arztDzbV5Or+xSc5NJGmQwyREQ0OCuKzY3i28OJwkxrmLnlllvQr18/PPjgg/HbBg0alPL+dXV1qKs70FpQVVUl9fyoYy7vL0N6Ne/eg4xudn2oQWYyafG/DqzKEJHrtLaZPfPMMzjqqKNw3nnnoVevXhgzZgweeOCBlPdfuHAhSkpK4l/9+vVTeLbhYEI7WhAmtpixKkOu0rlhZlhwPQMRtXbvvfdi4MCByMvLw7hx4/DOO++k9bjHH38ckUgEZ599dsLtl156KSKRSMLX5MmTJZy5PFrDzObNm7FkyRIceuiheOGFF3DFFVdg9uzZeOihh5Lef+7cuaisrIx/bd26VfEZB1SjtRCmlU37yxCRWC7uMeN18b+u9TKisSoTHN9D8uuJJ57AnDlzMG/ePLz//vsYNWoUJk2ahJ07O/7Q49NPP8W1116LE044IenfT548Gdu3b49//eEPf5Bx+tJoDTPNzc048sgjcfPNN2PMmDG4/PLLMWPGDCxdujTp/XNzc1FcXJzw5aJ095iRMZbZFK6sl2FVxj++dyHEscxCcWE4kVvuuOMOzJgxA9OnT8fw4cOxdOlSdOnSBcuWLUv5mKamJkydOhULFizA4MGDk94nNzcXZWVl8S/b1q5rDTN9+vTB8OHDE24bNmwYPvvsM01nRGEma4oZEYllwlhmFUxrMWNFwVw6gqsr1UYTVFVVJXy1Xh8eU19fj7Vr12LChAnx2zIyMjBhwgSsWbMm5bFvvPFG9OrVC5dddlnK+7zyyivo1asXhgwZgiuuuAJffWXXByFa+56OO+44fPzxxwm3/fvf/8aAAQM0nZGbZKyDMXHxv2mjc1lZME9kTzWiXYuUPmdOZQPqS7KVPid55+LifyJK36eNERQqnjRX89/na7sGfN68eZg/f37Cbbt27UJTUxN69+6dcHvv3r2xYcOGpMd//fXX8fvf/x7r1q1LeQ6TJ0/GOeecg0GDBuGTTz7Bz3/+c0yZMgVr1qxBZmam9xelgdYwc8011+DYY4/FzTffjPPPPx/vvPMO7r//ftx///06T4uIDMGpZkT+iP6knlUZsTiimVrbunVrwtKJ3NzgW35UV1fju9/9Lh544AH06NEj5f0uvPDC+P8/4ogjMHLkSBx88MF45ZVXcOqppwY+DxW0hpmjjz4aTz31FObOnYsbb7wRgwYNwuLFizF16lSdp0WC2bD4X3SLGasyRPp5GcvsRVgX/xORHOmsA+/RowcyMzOxY8eOhNt37NiBsrKydvf/5JNP8Omnn+LMM8+M39bc3NJVk5WVhY8//hgHH3xwu8cNHjwYPXr0wKZNmxhm0vU///M/+J//+R/dp0EwZyyzK4v/iYiIiETIycnB2LFjsXr16vh45ebmZqxevRqzZs1qd/+hQ4fiww8/TLjt+uuvR3V1Ne66666U25t8/vnn+Oqrr9CnTx/hr0EW7WHGVBW1+bpPgTwwbb0MEVEQJi3+Z4sZkRnmzJmDadOm4aijjsIxxxyDxYsXo7a2FtOnTwcAXHLJJfja176GhQsXIi8vD4cffnjC40tLSwEgfntNTQ0WLFiAc889F2VlZfjkk0/w05/+FIcccggmTZqk9LUFwTBDJBhbzMhEDYW6z4BU4UhmO3DdDHl1wQUX4Msvv8QNN9yA8vJyjB49GitXrowPBfjss8+QkZH+oOLMzEz84x//wEMPPYSKigr07dsXEydOxE033SRk3Y4qDDMUehzJbDYOASAikmtwzk5sru+l+zQoDbNmzUraVga0jFjuyPLlyxP+nJ+fjxdeeEHQmemjdZ8ZIiIiIiIivxhmiARii5nbuDbLTdxjJjWulyEi0zHMkGcmbphJRGQijmUmIpKLYcZSWTV2LBr0uscMxzIThVRhY1p3yy+sk3wiHfO6x4wfJk0yIyIyHcOMYbJqxH5LTNk7RqYgrT9c/B8+/J6r1VjISq5KnGQWXvzeU1gxzBAJwvUyROQSrpchIhswzBCR8RgUiYiIKBmGmYD21aS5qVANt/QhIvNxc00iIrIJwwwBCMfaGiISp7GQLUhEQbGVjyg4hhkiInJOaQH3BCIiCgOGGSIiIiIishLDDBERERERWYlhhoiIiIiIrMQwQ0REREREVmKYISIiIiIiKzHMEBERERGRlRhmiIiIiIjISgwzRERERERkJYYZAgA0FOo+AyIicSpq83WfAhERKcAwE1B+YV16dyxslHsiRA7L6NZV9ymERnZNevfLqonIPRGiENjcyH9HREExzBAJwgtuInIJL7SJyAYMM0REREREZCWGGcM0FjbrPoVQiXYt0n0KRE7LquGvGZU+beiu+xRIE37vKayydJ8A+dNYGE2rZ72hMP0eeBnqSzKRU9kk9TmaivORWbVP6nOQOxhg7bavJjf9tYpERGn6rKEbujRkKn3OvQ1NAL5Q+pwu4kdmZJT6kmzdp0BEOtTY8dna5/tKpT/H5vpe0p+DiMgVDDPkWV0R/7NJhUMA3NZUzHG/LiqvlVet+8++HtKOTUREDDNERESUAieaEZHpGGYo9LiGwmysdhGRi0wKimxtJJsxzBAJxotvMpHOQSCkFqdaEVGYMMykUFrA6Vg24VoGIiI5TKogEBG1xTBDRERkGLb9EBGlh2GG4hoK5Ry3vsTb3HYd45lFr5thqxmRfhW1ciq2Xsczc6IZEZE8DDPkC8czkwoMhUT+iF43w1Yzsfh+EonDK9IQkFVxMY1p62Z4IW4eHZPruBGsHWTuNUMkG4c+UJgxzBD9F0c0E9lhX02u7lNQwrR1M6wmUGtsnyRTMMxYrLEwqvsUpOGn2cTKVgjVZOk+A6fw03oiCgOGGQM1FjbrPgXhvA4BcAUvyImArBr+qnHlU2xWZ4Lje0gkFn/DqFTYqPsMOmX7+pqg62ZktJox0JCrsmrCe1HmdaIZERHJwTBDvnGiGcnCAEiiyBrPrErQdTMyWs1YWSAik/BqVID8wjrdp+Akl9bN8OKcyHycaEayMQgSiccwExImtI+pWjdjYqsZwECjW9Dvq2mjv2XKrtF9Bm7StW6G1Rm3cdADhR3DDBEZhaGP0mHCeGZV62ZMG9Ecw0DjBlP/+yJKF8OM5WSMZ/ZSxZG9bkZXqxmrM0REJBLDH5EcDDPkJJNbghhoUuN7QzL2mvEyBED2uhmXWs0AXqATkX4MM4Zyca8ZwK79ZmRVZ4jCiHvNBGNyKxADTef4HhHJw98uIWLCEAA//LaasTpjF5nvCYMpyWLTfjMyF4rzYl0PLv4nYphRz4KNM71yeb8ZmRfBDDTkAp0bZ5owBMAPv61mJldnAAaaVPi+EMnl7lWoAKUF+3SfgjYyqzgqW81Mrs4ADDQxfB9INpPWzejET/KJyDUMMw6QMdHMNDo30JTdosQLeVLFmv1jJAwBkEllq5mI6gzbzdTh+0EkH8MMURoYaOSR/dpFfO9Mr/CRfXRNNVOBF/At+D4QqRG6MFNWUC3luPmFdcKPKWOimZf2MZn7zfhpNXNxEEBrYQw0YXzNtpBRxZEx0UzWuhmTW81Mr84AvJBXQcT30PR1WETpCF2YIfJLxUSsMF3ch+m1ypRb7eYYdxm8rJvxyk+rmcvVGSDcgSbMr51INYYZHSRMNAvDupkgRFVnGGjswpHM5CobqjNAy0V92C7sw/Z6iXRjmCFhTG41A+xpNwNaAo3Locbl1xZGUsYzGzAEwGurmY3VGVXTzcJyga/qdXIqHdEB+n9bkHINhen3w3u5b1hEuxYhskfO2qu2Mrp1RfPuPUqeSxUGGRJtX01u2usWK2rznRm7v7m+Fwbn7Ax8nE8bumNg9lcCzqhjmxsjGJzlbhdBWAKbqz5t6Im8erWXxfsb3Nt7UAdWZgwnYwiASWytzqhsX3KpSqPydZjQYqZzpDiRiXjB7wbdFUWi1hhmHGLCuhmvrWY2U32xbHOgsTmQ2dSeaAMZE81kMr3VTNQ0KpVtSy6uo1H5ekR9rzjJjFxh128Vw8kYzyyLrBHNfthanQH0BBrbQoFt50sGkbRuRuZUM79M+KRb9ToMFwKNi8GMyDYMM52Q1lstYaKZKWyoztj+abstoUbXOZrQYuYCm9bLydpvxg8/1ZkgRH7CriPQ2BoGbD1vIteYf9VJzq+b8cuU9Qg6L5xNDTWmnhfJCyhSJpoZQtUGmia0mwF6JmXZFmp0nSunmBG1xzDjGC/rZmxvNWt5XDjbzdoyKTyYch5kLhPWzchuNVNdnXGF6aHG9PMjCiP9v1HISTa0mgFuBRrgQKhRHSh0PW8yIr8PtrcjOsHDuhmZrWaszqhlYmjQfT66vydEprLjipOM4GJ1RjQTAk2MioBhSoAhMpGO6oxLgQY4EGp0BQndzy8LJ5mRSxhmBPM00czDEAAv62ZktZp55ac64zfQBCH603eTAk1M62ATJHyIOo4sJr73ZC6vrWY2VGcA9wJNjKpgYWKAMen7QGQatVudEklSX5KNnMoG349vKs5HZpW4yXXRrkWI7KkWdjzRTAwiQTHIyJFdk/6HHlk1kbQ/TMmqyUj/Q5qarLQ//NlXk2vUmPzP95XioPwK3acRyKcN3TEw+yvdp5EgWdAYnOVvrzWTQgsReccwQ540FHqbhlRXlIHcam/T2OpLMpFT2eTxzBhoiMi7itp8eSP4A/jPvh4YkL/L9+M31/fC4Jydws7HxEDTFkMJUTixzSwNJv6iE0n2WpgwY7VADRnvMxf/UzJ+Ws38rp0xqd0MYKuTLqLfd66XIdeEMsyUFRj0abkB62a88hp+VK6dCToMQMYFLANNOJk0mKItmzbDjDNkqpltGGjsZuL7HTRkE4kWyjBDbjM10DDUyMH3FZ5bOWXysnmmCfvNAGoGAeiqzsjwaUN3Iy+yiSiczPhN4hhTFp/KnGpmcnVGBFktRrzwtgNbzBLZWMlxqTpjWrtZDAONXHx/idLDMGMZL61mLtC59wwDjfn4XoaMh1Yzr0yuzojAQGMXWe8r18uQixhmTOBh3YxMJlZngjA90PBCPBi+f24wpdXMD9vazWQGGoYaItLF3t8ilBaZgwD8UN1uZnKgAXhB7hffN7N5WTcjk9dWM6/VGdVMDjQAqzSi8H0k8oZhJk0mjWeW2WomuzpjK9mBhhfn5uB6GcNJbDXzQ3W7GQON2/j+EXnHMCOJKUMATGRjdQaQf5HLQJMevk9ieF3UL3MIgMxWM1OrM64HGl6Um4frZchVDDOmkLhuxmurmanVmbAEGl6sp8b3hgA4UZ0xgeyLWwYab/h+EfnDMGMp26ea+R0GEIZAA/CiPRkV74mo763JG2aqYsq6GYDVmY6oCDS8SO+c7PdI1PfZxH2PiBhmQsLE6ozq6WaAfYGGoaYF3wf32TzVDPBfnQlDoAEYalLh+0IUnN2/PQIoK6iW/hye180YMqLZdEE307Qp0AAMNWF+7aYxavNMj61mKqozDDSd44X7AXwviMQIbZjxw6SJZoD3VjOXqjNhCzRA+EKN6tdr+xSz3GrzWk9NajVzmY2BJuwX8ipfPxf+k+sYZkg7FwINQ41Yrr8+UxhVaYGPVjNWZ+JEBhqGGrnC+JqJZGKYMY3HVjMXqjNBBA00Iqn+ZD8Waly68Nf1emyvypA8YQw0gNpP82OhJgwX+WF4jSTXvffei4EDByIvLw/jxo3DO++8k/K+f/7zn3HUUUehtLQUBQUFGD16NB555JGE+0SjUdxwww3o06cP8vPzMWHCBGzcuFH2yxCKYUayMO43Y8swAED81CldF8UuhBrbzz/G9UlmXqs5XlvNTKvOqBbWQBPjaqjR9brYYuaWJ554AnPmzMG8efPw/vvvY9SoUZg0aRJ27tyZ9P7dunXDL37xC6xZswb/+Mc/MH36dEyfPh0vvPBC/D6/+c1vcPfdd2Pp0qV4++23UVBQgEmTJmH//v2qXlZgDDPUKVXVFp3tZiIvQFW3nbVmY7VG9/myKkOdUVmdEcH2QAO4U61x4TXEcCyzfnfccQdmzJiB6dOnY/jw4Vi6dCm6dOmCZcuWJb3/ySefjG9961sYNmwYDj74YFx11VUYOXIkXn/9dQAtVZnFixfj+uuvx1lnnYWRI0fi4YcfxrZt2/D0008rfGXBaA0z8+fPRyQSSfgaOnSozlPqlJIhAIa1mvlhY7uZK1WaGJODjcnnRvqZWJ2xqd0MEB9odH7Cb2sg0H3OrMrYo6qqKuGrrq59V099fT3Wrl2LCRMmxG/LyMjAhAkTsGbNmk6fIxqNYvXq1fj4449x4oknAgC2bNmC8vLyhGOWlJRg3LhxaR3TFNq3UR4xYgReeuml+J+zsrSfknD5hXXGtzZ0pqHQe3uJn8fUFWX4nspUX5KJnMomX489cIxs5FQ2BDpGa03F+cis0j8Fr21oiOyRP5o8nfPQTXfgNEF2jfwPH7JqIko+RDFReW2Rr60APt9XioPyKwI993/29cCA/F2BjtHa5vpeGJyTvJ1FhbbhYGD2V5rOJDXdAYb827qvO3Iy1bYJ1+9rud7o169fwu3z5s3D/PnzE27btWsXmpqa0Lt374Tbe/fujQ0bNqR8jsrKSnzta19DXV0dMjMzcd999+G0004DAJSXl8eP0faYsb+zgfbkkJWVhbKyMt2nYb3GwmZPn2Y2FkaNHZvqYqABYESoiUkWKmQEHNPCC4mhIgB5VpPlqaq9rybX85rGitp8pSP6GWg61jo46Aw2JgYYVmXssnXrVhQXF8f/nJsr7gPwoqIirFu3DjU1NVi9ejXmzJmDwYMH4+STTxb2HLppDzMbN25E3759kZeXh/Hjx2PhwoXo379/0vvW1dUllN6qqqoCPXdZQbXWvuYOFTZ6bp3wymugUVWdAdwLNIA5VZpUwhY8WJVRy2t1Jqsmw3MLrVeqAo3f6gxgbqABYEyoAZIHClkBx8TwQnYrLi5OCDPJ9OjRA5mZmdixY0fC7Tt27OiwKJCRkYFDDjkEADB69GisX78eCxcuxMknnxx/3I4dO9CnT5+EY44ePdrnq1FP65qZcePGYfny5Vi5ciWWLFmCLVu24IQTTkB1dfIf+gsXLkRJSUn8q21ZThU/n8ypmGom+xc/4O/TWL+f4AaZcGbiGhpA73AAOkDW90D3JDMTN85USvIHQEEE+eDMtDU0MaZ/+t96iEBnX14eYzoZ3xcu/tcvJycHY8eOxerVq+O3NTc3Y/Xq1Rg/fnzax2lubo4XBgYNGoSysrKEY1ZVVeHtt9/2dEzdtIaZKVOm4LzzzsPIkSMxadIkPPfcc6ioqMCTTz6Z9P5z585FZWVl/Gvr1q2Kz1gxj4MA/FDVx85Ak4iBhkzip3qqYsNNz4MAfFA1DABwN9CYHmrSYUtQ6YwL3wtKbc6cOXjggQfw0EMPYf369bjiiitQW1uL6dOnAwAuueQSzJ07N37/hQsXYtWqVdi8eTPWr1+P22+/HY888gguvvhiAEAkEsHVV1+NX/3qV3jmmWfw4Ycf4pJLLkHfvn1x9tln63iJvhj1MVZpaSkOO+wwbNq0Kenf5+bmCu0jdJHXtTN++G0d08HUljPAzLU0YcAgqY+SQQAe184AatfPuNZyFmNi6xmRay644AJ8+eWXuOGGG1BeXo7Ro0dj5cqV8QX8n332GTIyDlwD1tbW4kc/+hE+//xz5OfnY+jQoXj00UdxwQUXxO/z05/+FLW1tbj88stRUVGB448/HitXrkReXp7y1+dXJBqNGjNipqamBv3798f8+fMxe/bsTu9fVVWFkpISnPHC95FdkOPrOf1+Uub3kznPnwL6aJvwE2b8DANQ+Wlu0PaZoIHmwHHEh5oYhho1ZIYZUZW8IFVFv9VMVS2kfsKM5xZaH1VtP63AQYYB+A00AAIHmhgZoQZgoNFFZlUmaFWvvqYB95/0R1RWVna6NkS12LXk5a+eh5xCxdPMDH5fbKK1zezaa6/Fq6++ik8//RRvvvkmvvWtbyEzMxMXXXSRztNKi7KJNj5+KatYO+OXjnYzQEzLWctx5P2g43oa+fj+iuXnwwklUxR9fAikst0sKBEtZ4C8dRCutJ7ZhO83hZnWMPP555/joosuwpAhQ3D++eeje/fueOutt9CzZ0+dpxVKfj4t9RtMGGg6xgtuOWS/r7oX/wdlcuuor9ZZwwNN0EmapgcagKHGFVz8T6bTumbm8ccf1/n0ANSPZ/a1gaaPMc1+1s742XtG9fqZICObATFraFqO03LhKqvtjOtpxGJANItrm2jqWD8DiFlDA8hbRxPD9TRyMTBS2GmtzJAbVI5rBsyp0LQcS36Vhu1nwYTtvVM9nlnVhxkmV2cANyo0sj+BZ6VGPL6fRAwzgajcCVrV2hmVn5Yy0HjDUOOdqvfL9hYzSuQ30PhlSqAB1LQUMdSIwfeQqAXDjAYqNtAMwob1M4B5gYahxix8j7zzW2VRNQhAVXXGryADAUwLNAw1ZlP1vnG9DNmAYcYmhk82C3ugaTmeuk/oGWpS4/viLlfbzQAxgca2Kg3AUOMV3yuiRAwzAfltNVNZnVHZbmZroLG1SgNwXU1bfB/soWRMcwA2BhrAvrazGIaazvH9IWqPYQbBNi9Tzkd1xi/V04Z0BhrA7ipNTJhDja7X7tp6GZWtZn6pbDdjoFHXdhYTCzW8cE+k+v1gixnZgmFGI9OrM34FCSUuBhqdoSYswSYsrzNdqieaBaG0OsNAE4jqUAMw2ACsWBF1hmFGAKVTzQDf1Rkb2s2CPlZUoHEl1ABuBxtXX5et/FZnlA0DAJQOBADcCzSAvk/sw3ZRH7bXS+QXw4ytLGg3sznQAOKrNC3H1NuS5EqwMeU16P5+yqKyZSwI34HGhyDjmk0INC5UaWLCUK3R/drYYkY2YZjRTPWYZr/tZgw04uis0rTWOtiYEAzSYdO5hpXK6oxvitvNAP2BBnCrShPTOtjoDgAiuPI6iFRimPmvoEMAlLeaAUrbzVoeZ1+gMbXtrOW4ZoSamLbhxpTQYNr5xJj0vQsz1e1mOgONyW1nukNNjI3BxsZzJjKJ2uZhSiq/sE75jtOqNRT6/8Q2yGOBllAjYkF0fUkmciqbAh+n/XFbLopzKhuEHzuoZAEis0pucDcttNgqt7o5UJjPrvH/YYLfx2bVRHx9aJJVk+HvQ5qaLF8fCu2ryfVdVa+ozQ/04Vd5bVHgD99igeag/IpAx2krFmgG5O8Sely/koWDwTk7NZxJeyYHF1OCKVG6GGZsV9jo6xPGxsJmX59oNhZGfbeDMNB0dmxzQ01r6YaNtqGHIYWMFNJAA7SEGtGBBjAv1LSWKkTIDjkmhxci2zHMCFRasM93G0Gg6gwDTVpEBhoAUkON6YEmHS6HF7aYdc6a6kwArgQaQHyVBjA71LTlNWy0Dj8uBRVWZchGXDPTiu7NM1UPAwDUr58B9K2hAcStowHkDAc4cOxs49bUUPjommrm98MSHeOada2hAcQNBgDkrKWJMWlNjShc50JkDoYZwbQMAgCUjmqOsTXQAGKnnckMNS3PwVBjEpu+F7o3z9QRhsIYaEROO2OoCS9+b8hWDDOGCVSdUTzdrOWxDDQAQ01YhPH9t606E4ilgQawp0oDMNQQkVgMMwSAgUYU2YGm5TkYasgeQcKQ8nYzgIHmv2RXaQCGGpPw+0A2Y5hpQ8S6maCtZjqqM4C9gcakdTSAmipNy/Mw1KgU5vdaV3UmCFsDjUltZ4D8Kg3AUENEwTDMuEhToAkiaCAxtUqjMtSE+WKbUtO9bgbQU51peax9gQYIZ5UGYKjRhe852Y5hRhKt1RlNglRnAHMCjY2tZweei6FGBr6nwaszDDTemBZoAPWhhhfZ8vE9JhdwnxmD6dh7BvC//0zLY/3vQQME30sm6ONjRO1JEyN7b5r2z2fHBpw2YJAxg9/9Z1oeG2APGp+bagLB9qEBgu9FAxwINCK3HpC12WYyNu1VQ3b7Yn8JsjNzlD5nw/56pc/nKlZmkhD1Q1/bmOYYC9fPAGIqNCZXaXRUanhB7o8r75sJrWaAnWtvAFhfoQHsrdLEsFojHt9LcgXDjOECt5uFNNCIOgYgfi0NoD7UtDwng40XfJ/a0x1GtLWbAdoDjai2M9tDDcC1NSLw/SOXMMxIJqI6Y3Og0TXlTOQxADlVGkDteprE52Ww6Qjfl9R0rp0BwhtoAHOrNIDeUMMLc2/4fpFrGGZSENlfrL3dLKCgE86CBhoTBgPEuFKlSXx+BpvWXH0fTGk1A8IdaExqO3Ml1AAMNuni+0MuYpixhM7qDKA30ADmrKMB5FZpdIaalnMId7AJ6+v2SkS7WVgDDWBO2xkgp0oD6As1AINNKnw/yFUMM4rY3m4G2B9oRB0jRkagAcwINS3nkR2acBOG10iJbA80gPlVGkBvqAEYbACuMSL3Mcx0QGSrmSgMNIEeHj+G6VUawJxQE+NqsHHt9XREZKuZ7dWZlscz0LTmcqgBEoNNWC7uw/I6KdwYZhQyZu2MA4HGxCpNWEIN4EbVxuZzdwkDjZh1NKJDjSwmhJoY18ONi6+JKBlumtmJsoJqqT/Y/Qi0mWZMgE01gWAba7Y8PtjmmoCYDTJjgUbUyFnRm222pnrjTS+ShQITN+xkeBEru0bMhwJBjxNkQ82WxwfYVBMItLFmTNANNgExm2zGyNhss7VYoFG1+WY62l7827hRJwMMhRHDjGKlBfuEfILGQNNCRKAReRzgwFoa2aEGMDPYxJgScBhgEuVWNwutIooKNEEx0LSI/X5hqAnOpnDDEENhxjCTBtHVGQaa1o9vufgIEmpEVVdkVGkAuSNxTa7WJNNZsAgSdhha7CYiFBkRaIBAoUZEoAHEVmmAllAjcx2pyaEmJlVg0BVyGGCIWjDMWM6FQNNyDDerNABDjRcMJGqYWp1xItAAgas0sZ/pYavSAHaEmrY6ChWigg6DC1FqDDOaiKrOAAw0rYkMNID4UCN740JXQg3Zh4GmDUPazgA5VRqAoSYdDCFE8nGaWZpk/NA2ZrpZjIApZ7onnQFiRy+LXgsgc+pZa7EJaKZNQSNzyA7WQYj4EEHElDPdk84AMdPOAPETzwC5o5xjYtPPTJmARkTmYZjxwORAI+LTOwCBAw0gZnSzqFAjgshwFKMq1ABmjnYmN4msZJoQaFqOISDQCAo1ItgaagCzxjoTkTkYZhziUqBpOYZ5VRqGGnKJjOoMA00KBgUaQOxmmzGqtjFgqCGi1hhmPDK5OgMw0KQiMoTIGEOrI9Qw2JAsDDQpGNR2BthdpQHYgkZELRhmDOFyoDGp7czkKg2gNtQArNaQ2WtnRDIq0BhYpbE51ACs1hCFGcOMD7ImuLgaaAC3qzQuhRoGGxLFtOoMIC7QuFilAeS1nukINQw2ROHBMGMYBprOjmFelSZ2PBlUhxqAwSaMZFVnXA00LccxI9AA5ldpAPWhBmC1higsGGZ8kjlf39hAY8g6mpbjhKdKA+gJNQDb0Cg4UwONi21nDDXJsVpD5DaGmQBCF2gAY9bRtBzH3CqNq6GGwcZdMtfOiA40JlVpTGo7A+xoPQP0hBqA1RoiFzHMGEx0oHGx7azlOOKqNAw1nWOwcZctgUbk8YxrOwtR6xmgP9Qw2BDZj2EmIJnVGRlcDjQmtp7JOF5rOkMNwDY08oaBJk0GV2lcCzUAgw2R7RhmBLCl3SzGxHU0rNIEY0qoYbCxn+xRzSYHGuPazgys0gDuhhqAwYbIRgwzFjA60ADOV2kYarxhsLFfWAMNwCqNF7ICDaA/1ABcX0NkC4YZQWS3m5UW7BMeakwNNKZVaQA5rWeuhxqAwcZmDDQijiMw0BgaamRWaQCzQg2DDZGZ9F/tOETF+hnjAw2rNNqP2ZopoQZgsKH2TA80RrWdAcICDWBX6xlgRqgBGGyITGTGVY5DbA00rNKkj6EmOAYbO8iuzgByAg2rNOmR1XqmItQw2BBRjDlXNw6xMdAAZradAWZWaQA54UNVqGGwoXTZGGhEH1NkoGGoEceUUAMw2BDpJK5mTQnKCqql/5AtLdgn/JdFfmGduF9qsUAj4BduLNCIuBCIBRoRFyix4CH6YqyhUM4FXmuxQKPiYjVdrQNNTmWTxjOh1nKrm6UH4Owa8UFe5DFjPy9EfCCSVZMh7EMa1GQJ+/Ao9rNf5Adbsd9RMj6Ai4n9rjVlq4TWgeag/Apt50He7KgtRBbEhvrONNYK/BA5xMz5eNZBNldoXK/StBwr3K1nMaZVamJYsTGLzRUaVmm8EV2lAcJXqYlhxYZIPvOuYBxja6ABzB4OYHLrGUONeGEKNia/RlsDjejjihoO0HIsgf/mBAcahhqxGGyI5DDzysUxqgKN8etoAKOrNGFfTxNjcqgBwhVsTKQq0Ji+jgYIT5VGVqiRzdRQAzDYEIlk7hWLY1T18lrTdmZglableGZXaWQety3TQw3AYKOLqnVWtrSdMdT4o6JKA5gdaoDEYMNwQ+Sd2VcqjrE50ADmV2nC1Hom87ht2RBqgMRgw3Ajn82BRsZxRQWalmOZ2XoG2LueBjA/1MQw2BB5w2lmiqmYcgYcCDQypp0BAn+hCZx4BrSEGlEXAiKnngFyJ5/JOG5bJk4/6wgno8mnYsoZcOC/bZOnnQHiJ561HEvAv7fYz1eDp54BaiafAUj4HWzKBLRUOBmNqHPmf9zqIJU/PFmlEXE8cVUawJ1KjQ3VmhhWbORRGW5taDsDDK/SGN56Bqir1AD2VGsAVmyIUrHnasQxrgQaU9fSAGa3ngHygoeqUAPY04LWGtvRxLM90Mg4rrFraQCGmiRsCjUA19kQtWbXVYhjygqqla6jsapKE6KpZww1ejHYiJFb3ax0HU1YqzSmr6dhqNGDwYbCzM6rD8ewSpOCoVWaluPZFWpUsTnUAAw2IrhSpTF14lnL8cyt0gByhgQADDXpYrChsOEAAEOoGgwAyBsOALSEGqG/yCQMCADEXQw0FkaFXqTYPiQgxrZhAcm0DTQcIpA+VYMBAHnDAWLHDsWAAMCaIQGAukEBwIFhAaYPCkilbaDhEAFykb0foTpIZdsZYGGVJiStZ4D9QwJibK/UtMaqjTcq284Ae6o0gAWtZxaspwFYqfGDVRtykRtXGY5RHWisCzWCmN56BrixngZwK9QAHCLghepAE8YBAS3HC+d6GkBOl0EqroQagMGG3ME2M0PFAg1bz5KwoPWs5Xhm708j+9jJuNB+lgz3tOlY7PutsvVMVtsZYG7rWcvxMoxtPQPs36Mmxvb2s7bYjkY2c+ejUkep/kFpXZXG0NazluPZ0Xom+9jJuFapaS1Ixcb1Ko9LVRq2ngXjQusZ4FalpjVWbcgmrMxYwLUqDSD4F1lhY2iqNIB7lRrXqjStcYhAe65UaWQcW0aVpuV4ZlZqXBkSALhXqWmNVRsynZsfjTpKx4CAMO9NY8N6Go5ztktHVRvXqzJtsUqTmtGjnAHr1tOwUiMWqzZkGlZmLKRyjDPQEmqsqtIAoavUuFKlAdxbT5NK2MJLMjqqNIBd62nCUqUBWKmxEas2ZIJwfBzqINeqNDZUakSyrUrDSg3JpDrAygzoNlRpbFhP48LkM0Bde7gpWLUhHZyozHwtrxI5+dkA2n9K4Dqup+kE19NYcexkwlapCTtWaToWtvU0QMvvAlZp7NX6eqxhf72+EyHnOffx50H5FfGvMHFlw02A62lEkFlJUVmlAdRd3JIZdGy2yfU0AllWpeF6GiL7OVGZSaVtoAlD1UblehorqzSA0ZUaVmmSC3OVpu0EtLCstWGlpmNcTyMOKzVEdgvVR55hqdpwPU0nDF5PY+PUM66nUSunsin+FQY6KjW2HDus62lk4XoaIjs5XZnpSBiqNlxP0wmupxF6bFVVGsD9/WliOgssYara5FY3s0qTghXraVilSYpVGqLgwv0RZysuV21cq9QIxfU0Qo/LKo1erldtuJ6mY0avp7Fw6pnq9TRE5A+vBJJwNdi4EmpsaT0THWpEcq31zEVBA4nLwYahpmNsPROHAwLINPfeey8GDhyIvLw8jBs3Du+8807K+/7zn//Eueeei4EDByISiWDx4sXt7jN//nxEIpGEr6FDh0p8BeK5eRUgUOtg40q4cWXymS2hRtyx7KnSyD52W64GGlFaBxuXwg1DTWphW0/DKg2FwRNPPIE5c+Zg3rx5eP/99zFq1ChMmjQJO3fuTHr/vXv3YvDgwVi0aBHKyspSHnfEiBHYvn17/Ov111+X9RKk4BWARy4FG5WhxsohAYKY3nrmSpWGbWfpcy3YuBZqRDK69QywrvVMFVZpKJk77rgDM2bMwPTp0zF8+HAsXboUXbp0wbJly5Le/+ijj8att96KCy+8ELm5qf9dZGVloaysLP7Vo0cPWS9BCv7mD8CVqg1DTQpsPRN6bFUYaLxxqWrjSqgxfT2N6VUaQF7rGas0JENVVVXCV11d+2uZ+vp6rF27FhMmTIjflpGRgQkTJmDNmjWBnn/jxo3o27cvBg8ejKlTp+Kzzz4LdDzVQjvNTIbWgcbG6Wgqp59ZNflMwv40pk89s33iWZj3pQmqdaCxdTqaK3vUhHJ/GoumnqmceMZpZ/JV7s1DJvKUPmfT3pbf3f369Uu4fd68eZg/f37Cbbt27UJTUxN69+6dcHvv3r2xYcMG3+cwbtw4LF++HEOGDMH27duxYMECnHDCCfjoo49QVGRHmGaYkcTmYONSqDF1082wbripY7NNBhr/bA82OkKNDeOcjR7lLGnDTdvHOHOEs9u2bt2K4uLi+J87agkTbcqUKfH/P3LkSIwbNw4DBgzAk08+icsuu0zZeQTBMKOArXvauBBqwrQ/Das0yTHQiGHznjYqQ41Ne9TICDVCqzSAsFDDKg2ZrLi4OCHMJNOjRw9kZmZix44dCbfv2LGjw8X9XpWWluKwww7Dpk2bhB1TNjaXa2DbOhsX1tTYsJ5G3LHsGBCgejgAiWXjOpvYmhoV4TasQwKkrKcRyIW1NFxHE045OTkYO3YsVq9eHb+tubkZq1evxvjx44U9T01NDT755BP06dNH2DFlY2VGM5va0VoHGtk/TK2p1ISs9czmKg0rNPLY2I6mqlrD9TTmtZ65UKVhhSac5syZg2nTpuGoo47CMcccg8WLF6O2thbTp08HAFxyySX42te+hoULFwJoGRrwr3/9K/7/v/jiC6xbtw6FhYU45JBDAADXXnstzjzzTAwYMADbtm3DvHnzkJmZiYsuukjPi/TBmI8rFy1ahEgkgquvvlr3qWhjU8VGVbVGZqVGKEOnnsmo0sjACo07bJuOZnulxob9aYSxqEqjAis04XPBBRfgtttuww033IDRo0dj3bp1WLlyZXwowGeffYbt27fH779t2zaMGTMGY8aMwfbt23HbbbdhzJgx+P73vx+/z+eff46LLroIQ4YMwfnnn4/u3bvjrbfeQs+ePZW/Pr+MqMy8++67+N3vfoeRI0fqPhVj2LLORtW6GhmVGpOrNID4qWeiLlBsHw7ACo1atlRtWKlJJHI9TRirNKzQkCyzZs3CrFmzkv7dK6+8kvDngQMHIhrt+N/w448/LurUtNH+MWVNTQ2mTp2KBx54AF27dtV9OsYyvWpjc6XG5PU0rNLIwQqNHjZUbFyo1IjEKo1/qtbRsEJDYaf9N/rMmTNxxhlnJGwClEpdXV27jYXCyORgozLUiCYl1Ahi6mabMocDyMZAo5fpwcbmUGNy65nQAQGCN9vcV5MrLdTIxkBDYab1t/njjz+O999/P75QqTMLFy5ESUlJ/KvtJkNhZGqwURFqrJh8ZnCVRiQGGgrC5GDDUHOA6FAjjCVVGtkYaCistP0m37p1K6666io89thjyMtLb8fVuXPnorKyMv61detWyWdpFxODje2hRhjBoUbMccxvO2OgCR9Tg42qsc62hBoxxzG7SiMaAw2RHNoGAKxduxY7d+7EkUceGb+tqakJf/vb33DPPfegrq4OmZmJi0Vzc3OV7opqM9NGPqsY62zNkADDxjiLHg4gYzAAhwKEk6nDA1QMC8iukTMkwNQBAUI32xQ4HMDGwQAcCkBhoy3MnHrqqfjwww8Tbps+fTqGDh2Kn/3sZ+2CDPlnarAJbagRvDeNqEADiPnEVcZUMgYaMjHYyA41MiafyZh6ZuTEM4OnncV+N8kMNQw0FCbawkxRUREOP/zwhNsKCgrQvXv3dreTOCYFG1tDDas06REdQFSMbmagsYNpwSbsocbIKo2EEc62VWkYaCgs2CweYqassZG9rkb0mhqTBwSIOQ7X0ZA9TFpjI3tNjenraUQNCBC+lkYQG9fRcA0NhYERm2bGtN3sh9SJBRqd1RoVlRqjW88cbjuzaR0NqzP2MqViY2ulxsTWM9OqNKzQEJmHlRlKYEK1RmalRtamm0IIqtKYOMLZtgoNJ5zZz4SKjW2VGlZp0iNjPxoVk86IXMXf2JSS7mBjU6gR3nomgImBhi1ipEMYQo3o44kMNWKOY1agAcS3nckMNGw3I5cxzFBadAabUIYaw6o0pq6jYXWGvNBdrZEZamStpxFBZJVGCAYaIqfwtzV5xlDTORerNCI32WSgId10BhubQo1pVRphbWchDTRELuJvavJNV7VGdqgRxcQqjQhhCzTkPl3BxrZQI4JRVZqaLKHraGzA6gy5iGGGhHAp1LhcpWGg8Y7VmXBxMdSIPJZpVRohDAw0bDcjSh9/S5NQOqo1NoQaoVWagExbR8NAQybSUa2RFWpMrtIEP4ZZbWe2BBoil/A3NEnjUqgRxbW2szAFGgovhhp5xzKu7UwAGwINqzPkEoYZkk51tUZGqHG5ShP8GOYFGhlYnSHV1RpbQo0IDDSpsUJD1DH+dialGGoOYKBpT1SgMT0Ykf1cCTWijiOqShP8GAw06WJ1hlzBMENaqA41ohlVpRHQdiZiHU0YAg2rM9SW7aHGtCqNiLYzIetoDAw0RJQcfzOTVqpa0FilSY8pgUYUVmhIFZUtaLJCjajjOFOlMSzQsDpDlJy4XaMosAH5u3w/9j/7egg8Ez1igebzfaXSniMWaET+AC8t2Cfkl0x+YV3wX3qFjYF/ATcWNge6CGgsjAa+EGkoFL+buSh1RRnSxuiSG2KBpr4kU+rz5FY3C60Wxv7NifgQILsm+HGyaiKBPyDJqskI9iFNTZaQD4r21eQK+dCqojZf6FAaIhcwzGgQJLT4PaZNYcfGUBP75RI01MR+2QUKNQw0Uo5D/uVUNvh6XH1JtuAzUUtFqIkFa9GhRlSgAYIdK/ZzJEioMSXQmKq8tkjaRtREKjDMKCIjwAR9ftMDjqpQ42SVJvaLN0CoYaAhr/yGFj/Hsyno2BhqXKvSmBBoWJ0hkoNhRiLdAaYztgQc2aHG5CqN7rYzlwKNSGw1ayE6vIh4bpNDTuv1NLKCjYxQw0DzXw4HGlZngP21uchoVjuwoXmfWetMbcUwI4HpIaYjbc/dpHDTekiAjGAjI9S40HZmQqARwcRQZCudIaYzyc7NxIAju1ojcj2NqCqNqLYzBhoiao3TzAQakL/L6iCTTOw1mfbaZE5AEzn5TNTEMyHjmwPQPeXMxHHNYRzTnFPZYHSQSSV23iaev8wJaKKnnpkywlnE6OZABEw5EzHhjJtpErUI329jSUy60JfJtGAjO9SIwkBjTqAh70wMAUGYGGxsCTWiRi8z0IghMtBwTDPZimFGAFMu7FUzqWpjS5UmKAaaQA8XdowwMemCXwbTqjayQ40opgSaIKFGd6DhhppEYjDMBGDCRbxJdAcbG6o0ItrO8gvrgoUazYHGJa63mplwca+aKcFGVqgxrUoj4hhhDzRsN6Owc/s3sUQMMR3TGWxkhRqnqjSFjYFCTZBAw+qM+Uy4mDeBCcHGllCj+xhhDzSisNWMbMQw4wODjDe6go3MUCOC9kADhDrQUHIMMcnpDjYyQ40IoQ80mrE6Q2Fm979eso4roUZUlUZU21kglgaaoEQFIpdazRhk0qMz2JhcpRHVdhaEtkDjUHWGyDbu/BZWhFUZMXRUa2SFGhEYaLxjdUYsBhl/dAQb16s0DDT+iKrOsNWMbMMw4wGDjBw6Qo1IIqs0QeiedKZD0EDDQEQiuRBqGGj0Bhoi8o5hJk0MMvKprNaYWqWxNdDY3G4mgu2tZqzKiGV7qBHZdqbz8TYGGlOqM0Q2sfs3MDnL1lBjwjqawIHGJ1vbzcJenWGQkUd1C5qJVZqg62iCPj7o5pphxVYzsgnDTBpYldFHZagRxYS2M1370IS9QkOUiqpQwypNe34DDaszROK99tpruPjiizF+/Hh88cUXAIBHHnkEr7/+uu9jMsx0gkHGDCpCjalVGr8Cba6pKdD4xeqMP6zKqGdzqAmKgSZ9nG5GLvr//r//D5MmTUJ+fj7+/ve/o66u5RqlsrISN998s+/jMsyQVVSFGlF0B5pANAQam6eb2bZuhkFGL1UtaAw0wdm4Bw2rM2SiX/3qV1i6dCkeeOABZGdnx28/7rjj8P777/s+rn3/QhViVcZcskONyCqNiLYzG1vO/GK7GYWN7FAjskojou1MV6DRMhDA4uoM182QaB9//DFOPPHEdreXlJSgoqLC93EZZshqKkKNKGELNLa1m+mu7KjEqoyZVIQaUcIYaHzjuGYiAEBZWRk2bdrU7vbXX38dgwcP9n1c/gtzxOCcndKOvbm+l7RjixILNP/Z10P4sWOB5vN9pYGPVVZQHejTrtKCfb7bB/IL6/x/0lfY6OsXcmNhs69PNBsLo5xCRKEVCzT1Jdmd3NPPsZv+e+zMwMfKrW4O1F6ZXRPsQwS/j8+qifiqAGfVZCj/kGZfTa7vD6MqavP1tSkTJTFjxgxcddVVWLZsGSKRCLZt24Y1a9bg2muvxS9/+Uvfx2WYScHkFjOZwcXL85kYcmSHGgYa839kNBT6/9Q2yGOBlnUzojYdlMWmqkxmlZwLsaZiO9YT5FQ2SAk0LcduMibQAP5DjTWBpibLyo2JiUS67rrr0NzcjFNPPRV79+7FiSeeiNzcXFx77bW48sorfR/X/CsTAqA+wKSj7TmZFG5khRpRVZpYy5nfUKMt0PjA6gylS1Z4Sfd5TAw5NlRpYgFeZ5XGD1sCjc7qTHltkZBBNkQAEIlE8Itf/AI/+clPsGnTJtTU1GD48OEoLAz2j59hJgmTqjImhphUTAw3MkON7iqNlkBjSbtZ0AqLq0ysyqgKMOlIdi6mBBzZocaUKo2fQKMjCBGRfzk5ORg+fLiw4zHMGMqmEJOKSeFmQP4uBpo2VAcaWzAIqWFSiOlI2/PUHW5khZqwBpowVGeITPGNb3wDkUjqDyhffvllX8d194rEUi6EmFRavzYdwUZGlUZk25nrgYbVGbIlwHSk9WvQGWxkhBqRbWcMNObgIAAyxejRoxP+3NDQgHXr1uGjjz7CtGnTfB+XYaYNXS1mLoeYZGKv16VQE7pA44PfQGMTU4cA6G4xcyHItGVCsJExJEBElSYsgcYXVmcopO68886kt8+fPx81Nf4/fXT7qsISYQsyrQ3O2Rn/Uk10cBWxJ02QhZbKP3lTOJlH9Uaa7L8XJ7Nqn5NBpq3Y69TxWmXsTyNiTxpdoV5lRdb1D2cAbp5J8l188cVYtmyZ78e7/6/QcGEOMm3pCDaiN920NdD4/rTPR6BR2ZbBUKJXGEJMMjpDjdjj6Q00QUKJn8f6nZ7oK9D4XHfot4rut3JPpMKaNWuQl5fn+/FsM2tFdYsZg0xqqtvQRA4IELGORkfLmentZhzV7J/qFrOwhpi2dLShiV5LI2IdTZCWM9WTypS2mxGFzDnnnJPw52g0iu3bt+O9994LtGkmKzMa6GqrspHK98q0Ko01FRrDN4LzeyHEqo4/DDLJqa7WmFal0VGh8fs4Px+aqKzOENmqpKQk4atbt244+eST8dxzz2HevHm+j8t/SYoxxPijslIjukoTigqNoulmrM6YjUGmcyqrNTKqNLZVaFzcg8bvIABONSPdHnzwQSnHZWXmv1S0mDHIBKeqUhP2Cg0FE2SKk60YZLxTVa0RWaUJS4WG1ZlEHAJAJrPjXxFRGyoqNSJHOOus0PjhWnUmzHvOqFgvwyATTOz9k1mpEVmlsbFC44fJ62c4plmCmiygSfFl8T73L8O7du3a4UaZre3evdvXc7j/LhqCVRk5VIUamwON6e1mJgtzCCL1VIWaMAYaVSHI10aaPvedIbLB4sWLpT+HO1cdBmOQkU92qAlroFGB1Rm7sSojXmbVPgYaCfwEGj/VGV+BRhGumyHVpk2bJv05wtfYnYTM9TIMMmrJXFMjatqZrjU0fn6BuTjdjMRhkJFH9noaURtt5lQ2BVpH43cNjeoJZ9L5qGirGqVPJMP+/ftRVVWV8OUXwww5SWaItDnQ+KGiL9vUTzFtJ3O9DIOMGipCjW6qA41XyoYBEDmutrYWs2bNQq9evVBQUICuXbsmfPnFf20SsSqjl+wqTVBBA40fytoLFFRn/CzM9dMz77fP3uWJZgwy6skMNaIqNEGoDDSqppt5pmi9oaktx+S+n/70p3j55ZexZMkS5Obm4n//93+xYMEC9O3bFw8//LDv47r721YzBhlzyAo1ugONa+1mrM4QdY6Bxh4qqjMqW804npmCevbZZ3Hffffh3HPPRVZWFk444QRcf/31uPnmm/HYY4/5Pi4HAITYwOyvhB/z04buwo8pyuCcncIHBIgYDBBkKIDKgQC+pptJxk001WNVRj9ZU89EDAYIOhTAD1XTzUwe1Uxkg927d2Pw4MEAgOLi4vgo5uOPPx5XXHGF7+OGPszIWPxvYlVGRnBJ93lMCjgypp7ZGmiU8Diq2c9kM6841cxtkT1i15NFuxr6bwtypp7pDjR+J5yZOq7Z82Qzjmkmhw0ePBhbtmxB//79MXToUDz55JM45phj8Oyzz6K0tNT3cdlm5riB2V8pCzKdnYPu82hNdOC0seVMWbuZA1Rt0ieSjIXdJlZlInuqE75kH1/GcwQhYy2NiElnNk0488LESrBpFXOiVKZPn44PPvgAAHDdddfh3nvvRV5eHq655hr85Cc/8X3c0FdmRDOhKmNSaGir7bnprNqIbjvTXaHxQ8n+M5KrM2w1Cx/dgaLt85tQvTGxSqOjQuOVqs00PVFQneF+M6TStddei+9///u45ppr4rdNmDABGzZswNq1a3HIIYdg5MiRvo/PyoxDTKt+pEP3OYseDqCzQqNqXHNYqzNhZ0JVxsTKCABjqjayqjTBHh9sKIBXJlZnOKaZwu7//u//MGLECBx77LFYtmwZamtrAQADBgzAOeecEyjIAAwzQumqyugOBCLobkULc6BR8umc5MlmXhflGvdJLHXIhKCQLhOCjSuBRtV0M66hI5Jr48aN+Otf/4rDDjsMV111FcrKyvC9730Pb775ppDjM8xYzvYQk4yuYGNaoFHJa6BhdSY9ruw1o6sqozsUBKXz/MMcaFTsPSO9OuNxzxmumyHTnXjiiVi+fDnKy8tx1113YePGjTj++OMxbNgw3HbbbdixY4fvY7vxm9YnkRecqqsyLlRj0qH6dYpsOwv635dz7WaWT+ixqZpjwq7uQdkcYtrSVa0R3XbmeqChzhk7OZOsUVBQgO9973t47bXX8O9//xvnnHMOFi5ciP79+/s+ZqjDjK3CEGLa0hFqRLAp0Ji2GFR2qxmlR3VVxvZqTGd0hRpRXAjKqRhXnZFM+vAXog7U1tbitddew6uvvoo9e/bE95/xw6x/WdSpMAaZ1lSGmjAGGq9sbjezqdISFi6HmLZUhxpTAg2rMx6w1Ywc9Prrr+N73/se+vTpg9mzZ+Owww7Da6+9hvXr1/s+JsOMAKpazMIeZFoLW6BRRXp1RvIgABJLZVUmTEGmNZWhxoSJdIDZAwFkV2eIwmj79u1YtGgRhg4dihNPPBEbNmzAHXfcge3bt2PZsmU47rjjAh2f+8xYgkGmvdh7InuvGlH70QTZh8bv/jNlBdXSe5zzC+v4iSAFEtYg01rsPZC9Z42o/Wh07kHjhWn7yGTVZPBDGgqdfv36oXv37vjud7+Lyy67DMOGDRN6fFZmLMAg0zEVrWcmVGj8tpt5ZVp1xgvZ62ZMuihyBYNMIhXvh6gKjY71MyrazYxqT/PYakZkoieffBJffPEFbrvtNuFBBmCYCUx2ixmDTPoYaJJTMQxA5toZmZ9iMpykpqIliUEmORWtZyYEGpPbzbwwqdXMa5WcQwBIhXPOOQdZWfKCeWjDjA1rGBhkvJNdpdG1MWpQqsY1p83yMc0UHINM52SHGpsDjVcmVWdMm2pGZDv+iwpA5oUtg0wwpgcatpvJE/YRzTaMzmWQ8cb1QOMHqzNEFMMwQ86SWaWxMdDIrs7Y2mpG7clsMWOQ8UdmlUb3lLMwVmc84boZog4xzBiIVRmxXA001jOk1Uz2upm6Iv6YjWGQCc7kQKO63cy06owXNreayZ6QSeSVvf+aHMUgI4fJgcYvFdUZkwYBEFELVwONCnLXwpjRasZR+WSq2tpa/PKXv8Sxxx6LQw45BIMHD0748ou1S59sXQgeZrL2pQm6D02Q/WfCpLGwOe1PMxsLo9IuLBoKDWo/sQirMmJF9lRL2ZNGxD40fveg8bP3TG51s9TqpzH71NRkGVPNJvLr+9//Pl599VV897vfRZ8+fRCJiPk9zcqMQViVUUPG+xw03PptNzOxOuMJfzkbR8b6CQYZOWSto9G5hkbF+hlTPowwpdWM45ntcu+992LgwIHIy8vDuHHj8M4773R4/z/+8Y8YOnQo8vLycMQRR+C5555L+PtoNIobbrgBffr0QX5+PiZMmICNGzdKOffnn38ef/zjH3HLLbfg6quvxlVXXZXw5Vegf0mbNm3Ciy++iDfffBO7doW4918ABhm1XHq/VU03SxdbzYjUMjHQqGw3k712xkv4MaXVjNz0xBNPYM6cOZg3bx7ef/99jBo1CpMmTcLOnck/UH3zzTdx0UUX4bLLLsPf//53nH322Tj77LPx0Ucfxe/zm9/8BnfffTeWLl2Kt99+GwUFBZg0aRL2798v/Py7du2Kbt26CT+urzCzc+dOnHDCCRgyZAgmT56M448/Hn369MH06dNRXc1P4MgOogONruqMH0ZVZwxgRBuJo1iVUcOlQKNquhmRbe644w7MmDED06dPx/Dhw7F06VJ06dIFy5YtS3r/u+66C5MnT8ZPfvITDBs2DDfddBOOPPJI3HPPPQBaqjKLFy/G9ddfj7POOgsjR47Eww8/jG3btuHpp58Wfv433XQTbrjhBuzdu1focX2FmRkzZiAzMxOvvfYaqqurUVFRgRdffBHvvvsufvjDHwo9QROJXi9jS5VgcFbU05cNXAk0VldnPLSaeRnRHPb9Zih8whwcvVZnTGk1k4VDAOxSVVWV8FVX1/53aH19PdauXYsJEybEb8vIyMCECROwZs2apMdds2ZNwv0BYNKkSfH7b9myBeXl5Qn3KSkpwbhx41IeM4jbb78dL7zwAnr37o0jjjgCRx55ZMKXX74GALzyyiu4/fbbUVhYiE8++QQA0L17d/zsZz/DFVdcgQ8//BDRaMuFxMiRI32fnCyhHoebJhFhJNUxNjeaVYYfmP2V0KEAQQcCqFJWUO1pxGZpwT72VhsuaGuP6LUSNlxcN+/e4+n+Gd26SjoTMUQPBgg6EEDlMACZvAwCyKqJpP1hSlZNRvof0nAIgFRZtRnIaFK7jql5f8vz9evXL+H2efPmYf78+Qm37dq1C01NTejdu3fC7b1798aGDRuSHr+8vDzp/cvLy+N/H7st1X1EOvvss4UfE/AZZqZPn44f/OAH8cASE5tKMHr0aESjUUQiETQ1sVzcEdOqMioqKq2fw5RgIzrQBOF3utlB+RX4fF+p+BMicojX8JLO400LOK4EGq+8TjYzZlIZhd7WrVtRXFwc/3NurpuVtXnz5kk5rq8wM2TIEHzwwQcoLi5OCDTvv/8+pk2bhg8//FDYCZJ8OlvCTAo2IgONLeOavVZnvMgvrEu/1aGwkbtcO8akqkzQAOPl+KYEG9MCjR+mVWeIZCkuLk4IM8n06NEDmZmZ2LFjR8LtO3bsQFlZWdLHlJWVdXj/2P/u2LEDffr0SbjP6NGjvb6MtK1duxbr168HAIwYMQJjxowJdDxf9bQ1a9bg3HPPxbvvvovMzExkZmbivffew89+9jNccMEFGDBgQPyLzGXa2hYTzkdkpUzHXkSy186YMAjAy7oZWfhprh2ad++RHmRSPafq503GpECparqZzMlmsqaamTCimS3E5svJycHYsWOxevXq+G3Nzc1YvXo1xo8fn/Qx48ePT7g/AKxatSp+/0GDBqGsrCzhPlVVVXj77bdTHjOInTt34pRTTsHRRx+N2bNnY/bs2Rg7dixOPfVUfPnll76P6+tf0D333IOjjz4a559/fjy0TJ06FZMnT8Zvf/tb3ydjA5EXqDpbzHSHho7oDjWmtP5xbVdwXoYAhD2giFwvo/si2pQwYcJ5iPxe6Nh/RvZkM+sGAbCCHWpz5szBAw88gIceegjr16/HFVdcgdraWkyfPh0AcMkll2Du3Lnx+1911VVYuXIlbr/9dmzYsAHz58/He++9h1mzZgFoWR5y9dVX41e/+hWeeeYZfPjhh7jkkkvQt29fKetbrrzySlRXV+Of//wndu/ejd27d+Ojjz5CVVUVZs+e7fu4vv5VFBcX49FHH8V9992HzZs3IzMzEwcffDC6dOni+0RIDZNDTFuxc9XRfiaq5UzHMACva2eMaTUjCkh3cEgldl66WtBEtpwFaTczde2Mq/bV5HLfL8dccMEF+PLLL3HDDTegvLwco0ePxsqVK+ML+D/77DNkZBz4b//YY4/FihUrcP311+PnP/85Dj30UDz99NM4/PDD4/f56U9/itraWlx++eWoqKjA8ccfj5UrVyIvL0/4+a9cuRIvvfQShg0bFr9t+PDhuPfeezFx4kTfxw0U8YuLi6X21LlMx6f/NgWZ1nSGGhGCBBpVa2e8kDbVjOtmKABTg0xrOkON6DU0KsleO+NlEACHBpBus2bNildW2nrllVfa3XbeeefhvPPOS3m8SCSCG2+8ETfeeKOoU0ypubkZ2dntP9DIzs5Gc7P/FlF+dBEStgaZ1lS/BlPazVTwuommbiasm/EiTJ8S62gxsyHItKar/UzU9yZIu5mqtTMm8LJuhigMTjnlFFx11VXYtm1b/LYvvvgC11xzDU499VTfxw3Pb9gQcyHIxKheTyMq0ARZa+Vn7Yxpm2gSyWJbkGnNhDU1tvC6dkbmIAAZTBgC4IWs1mRy2z333IOqqioMHDgQBx98MA4++GAMGjQIVVVVgdbcs6dDA5Wf+LsUZFobnBVV1nZm8/oZWby0muleN9NYGOUnpI5yJQg0796jrPVMVLuZDWtnvGCrGZF8/fr1w/vvv4+XXnopvtHnsGHDMGHChEDH1fpRwJIlSzBy5Mj4fO3x48fj+eef13lKHdIxajcIV4NMjOuvrzUV1RkjWs24u7UWoqZUqWwxcyXIxKh8PSa0m1EAXFtIFotEIjjttNNw5ZVX4sorrwwcZADNlZmDDjoIixYtwqGHHopoNIqHHnoIZ511Fv7+979jxIgROk/NemG50Fc1HIDVGSJzuBZkYlQOCNA9EMBPdcbrIAATpppl1UQ8jYgncs3dd9+Nyy+/HHl5ebj77rs7vK/f8cxaw8yZZ56Z8Odf//rXWLJkCd566y1nw4yKFrOwBJnWVLSdiQo0fpk22UzaVLM0NRY2C+8zbyi0cN+JkHE1yLSmsu0sqCDtZiQfxzOTbnfeeSemTp2KvLw83HnnnSnvF4lE7AwzrTU1NeGPf/wjamtrU+46WldXh7q6A/8oq6qqPD8PNyJ0l8p1NEGorM6YsueM7nUzMjD4JFLRYhaGIBOjItCEoTrjhe51M1k1GVonNVbU5qO0gK2DJNaWLVuS/n+RtI/P+PDDD1FYWIjc3Fz88Ic/xFNPPYXhw4cnve/ChQtRUlIS/+rXr5/iszVfGKsyrcl+/WEa16wN1834EqaRt2GhIryJCKEmr52xbaoZkctuvPFG7N27t93t+/btC7TPjfYwM2TIEKxbtw5vv/02rrjiCkybNg3/+te/kt537ty5qKysjH9t3bpV8dmaLexBJsaGQON3mATHNFNYhakq05rrrzsMIZzTFIlaLFiwADU17Vsa9u7diwULFvg+rvYwk5OTg0MOOQRjx47FwoULMWrUKNx1111J75ubmxuffBb7sgk/1VeHwc4/L1PNbGlJ4AJcu7l+Qd8Z2a/ftuqM1z1nvLCmdZQTzchC0WgUkUj7cP/BBx+gW7duvo9r3L+G5ubmhHUxpjB9LDMv3tuTuYZG5zAA0wYBpMvFdTMuEHERqnIkc1jZNBTANLKmmnG/GaL0dO3aFZFIBJFIBIcddlhCoGlqakJNTQ1++MMf+j6+1jAzd+5cTJkyBf3790d1dTVWrFiBV155BS+88ILO0yKHmDwUgGOag5Mx0YzMEvaqTGsyA42IYQB+J5uZuIkmEYmzePFiRKNRfO9738OCBQtQUlIS/7ucnBwMHDgw5fCvdGgNMzt37sQll1yC7du3o6SkBCNHjsQLL7yA0047TedpWYdVGT10j2r2wutUMyITMMi0xwpNC5lTzXTSPdGMSIZp06YBAAYNGoTjjjsOWVli44fWMPP73/9e59MrxfUy+rhYnZHdauZlRLOU/WYKG9kTTpSCrECje1SzKeSMXebmmUS1tbVYvXo1Jk2alHD7Cy+8gObmZkyZMsXXcdmfYTlWZdIj631iSHUPe+C9kbVehlUZPYJ+P/2uwZI91SzMI5q5VpFMcd1116Gpqf0Aj2g0iuuuu873cRlmKDTCHvxMGNHMnaiJxGDYM4M108+IDLBx48ake0kOHToUmzZt8n1chhmLhf3i3BRBqzMq95whIneYGmhUjWmWOaKZiMQrKSnB5s2b292+adMmFBQU+D4uwwyFCgNg+rzsN2MD9qvbw9SL9LDQNWo7DBtoCsV1hWSZs846C1dffTU++eST+G2bNm3Cj3/8Y3zzm9/0fVyGGSIBwr52RufmmZz8Q2HG4Jc+L+tm2D5GJN5vfvMbFBQUYOjQoRg0aBAGDRqEYcOGoXv37rjtttt8H5exPg1BN8yUcaHLCoN/Jk43U7XnjFUjmjnRzHjcLNMMMqabBZ1s5nfPGeJ4ZnJXSUkJ3nzzTaxatQoffPAB8vPzMXLkSJx44omBjssrBQolEwONH7JHNJPbVK1t8IKVBvLCpv1mOJ6ZCIhEIpg4cSImTpwo7JgMM0SC2LSJpk75hXXOjAptKGQ7CpnBlc00cyobUF+Srfs0PJGxL40uFbX5WtuGyX2rV6/G6tWrsXPnTjQ3J1Ygly1b5uuYXDNjIbaYiWHa+xi0nVEG14YAEFH62EZIRCItWLAAEydOxOrVq7Fr1y7s2bMn4csvVmaIiMgIbDELxrTqjInrZnKrm1FXxM9xiXRYunQpli9fju9+97tCj8swQ6Emeu2MDa1msoYAlBbsQ0WtWRcuRER+udQ+Rp3LrI0gU/Fa2qY6+9fuelFfX49jjz1W+HH58QSR5bh5JhHFiK5u6Wg187rfDDfPJLLD97//faxYsUL4cVmZkUz0WGbT1nmQWKpGNBMFxfUURETkxf79+3H//ffjpZdewsiRI5GdnTjs44477vB1XIYZCj1XxjQT2YzrZdxk4roZItLjH//4B0aPHg0A+OijjxL+LhLxfx3GMEMkmA3rZqhjHLlMNhM9CCDoBppERADw17/+VcpxuWaGiDqkdTxzYaO+5yYiEiyrRk8XgCt7exElw8oMEexvNRuQvwv/2ddD92mkTfTGmY2Fzciq6fyzmcbCqLaLibqiDORWN3d+RyKSSsZ4ZtGTz7JqMtBYmMbPi5osbR/6lNcWcS8y8uQb3/hGh+1kL7/8sq/jMswQGUbFEABZ45mJyAym7TnjVU5lA+pLsju/Y/z+TagvyZR4RkQUVGy9TExDQwPWrVuHjz76CNOmTfN9XIYZi3CSGRG5iIv/3cYhAEQEAHfeeWfS2+fPn4+aGv8LVblmhkgC0SO5bVFasE/3KRCRBGEdxc1BIETyXXzxxVi2bJnvxzPMEBERERGRFmvWrEFeXp7vx7PNjOi/bB8CQETUmu3rZojILeecc07Cn6PRKLZv34733nsPv/zlL30fl2GGiIiIiIikKikpSfhzRkYGhgwZghtvvBETJ070fVyGGSIiIiIikmLz5s0YNGgQHnzwQSnH55oZIiIiIiKS4tBDD8WXX34Z//MFF1yAHTt2CDs+wwwREREREUkRjSZuLfLcc8+htrZW2PEZZoiIiIiIyEoMM0REREREJEUkEkEkEml3mygcAEBERERERFJEo1FceumlyM3NBQDs378fP/zhD1FQUJBwvz//+c++js8wQ0REREREUkybNi3hzxdffLHQ4zPMEBERERGRFLJGMsdwzQwREREREVmJYYaIiIiIiKzEMENERERERFZimCEiIiIiIisxzBARERERkZUYZoiIiIiIyEoMM0REREREZCWGGSIiIiIishLDDNF/bW6M6D4FIiJhMrp11X0KRETSZek+ASIiIiITNRTqPgNSJbsGyGxQ+5wZdWqfz1WszBBJ8GlDd92noEVFbb7uUyAiCaJdi3SfAhFRUgwzFmEbFBER2aapmB9yEJE8DDNEhtlc30v3KRApxbUd4tn+ntaXZHu8f6akMyEi0zHMEMH+qtd/9vXwdP/P95XKOZE07avJ1fK8WTX6vs+51c3anpuI5BK9tqaxMM2fF4WNYp/Yg7KCam3PTdQawwwRdai8VmOvfE16M0qyavijjIjSU1ek7+dFY2FUy/PmF3KlObmLVwBEgoV18b9Lsmt0nwGRf6JbzLj4n4hMxjBDoWd7ixmRC2xf40HJcfE/EcnGMCOZ6E/peeHtNi7+J1vw03oiIjIBwwwREZEjXGgx8zrJjIjCjWGGQk10pUvHehlTJplxw0wi0kHWWGbRE8qISA6GGSIiMgLXzQTD969zOieZEZEc/FdtIa6bEcO099HE9TJaxzITkVZBW8zCsvifFRwivRhmiAThSOb06NowUwaOcCZTuFKV4XoZvUoL9uk+BSLPGGYolEyryvjldb0MUWsmfnLuykU5UVu6Nswkch3DTBqCtv/I+MTelYtxUkvW4n8parJ0nwF1guOZzSAjANrSYiZr8b9OjYXNuk+ByCoMMxQ6MoJg0MBq4noZW2TV8McYEXXOy+J/roMhsgevAihUWNFKn5fF/zaMZc6q4ffeFmw1S5+JVRm/uF7Go8JG3WdAZASGGYvxwtwMuhb+c70MUbiZGvrYYkZEKjHMUGiYGv5UtZiZsF7GpUlmRDqZGmTChu1oRPoxzFjO1At008h6nziO2T0ct+yNrJYkXqzroWvhP1vM5MkvrNN9CkRSMcwowoteSsZvVUZ2i5n29TKcZEaUEoOed7oX/3MsM5E8DDMOYHWmY6zKENmJF+3tyXxPbBq1zfUyRBTDMENOY9BrYcJ6GRk4ltl9DDQHmB5k2GIWHPeYIfKOVwJpMn0fEF60tyfzPRFRlTG1xUwWLv43k4jJUzZ9om8rhjr/vLSYecHF/0RmYJhRiG1J6jDc+ad9vYwE3GPGbmG/kJf9+nVWZfyQ2WJmTUDhHjNEcQwzDuEFfAvZ74NtVRlXW8woXMIaaFx/3WFoMePifyK5GGbIKQx0DuAkM1/CcFEYNiqCjG1VGSJKz+7duzF16lQUFxejtLQUl112GWpqOt574P7778fJJ5+M4uJiRCIRVFRUtLvPwIEDEYlEEr4WLVok6VWkJ3Rhxtb1BukK88W8iteusyqjgpcWs7DjfjSJVKybcb1K0ZotQSYIPwHca4uZTetldC/+Ly3Yp/X5SaypU6fin//8J1atWoW//OUv+Nvf/obLL7+8w8fs3bsXkydPxs9//vMO73fjjTdi+/bt8a8rr7xS5Kl7xo9AFfu0oTsGZn8l9Tk2N0YwOCtcZW1bgkwQprWYeVkvI2PxPyeZhVNGt65o3r1H92lIZVNoc6kqY816GQ+4YWY4rV+/HitXrsS7776Lo446CgDw29/+Fqeffjpuu+029O3bN+njrr76agDAK6+80uHxi4qKUFZWJvKUA+HVgKPCVKGxKciYXJUhVltsYdPFvhcZ3boqe21hqMoQ2aKqqirhq64uWAhds2YNSktL40EGACZMmICMjAy8/fbbQU8XixYtQvfu3TFmzBjceuutaGzUO5CClRkPNtf3wuCcnbpPI22uV2jCFNhUVGWMaDHjehktmorzkVkVvMUk2rUIkT3VAs6oc65VaFQGNFFBxvSqjKwWMy+kLP7nJDMpsmuBzAa1z5lR3/K//fr1S7h93rx5mD9/vu/jlpeXo1evxA9Ps7Ky0K1bN5SXl/s+LgDMnj0bRx55JLp164Y333wTc+fOxfbt23HHHXcEOm4QvHLQQEWrWYyrgUZlkGFVpj3dLWZecCyzu1wJNDYGmSBMHFbhpcXMxXY00mvr1q0oLi6O/zk3N/nvzeuuuw633HJLh8dav3690HNra86cOfH/P3LkSOTk5OAHP/gBFi5cmPK8ZWOYCQGXAo3qaowJQcb1oRVEQdgcaGxul1NdlTFl4b8suhf/e1VWoKYCGxbFxcUJYSaVH//4x7j00ks7vM/gwYNRVlaGnTsTO4kaGxuxe/du4Wtdxo0bh8bGRnz66acYMmSI0GOni2EmJFwINGFqKwvKyhYzD2xb/J9bbdeFShAqW81ibAw0OoKMCe1lJlZliGzRs2dP9OzZs9P7jR8/HhUVFVi7di3Gjh0LAHj55ZfR3NyMcePGCT2ndevWISMjo11bm0p2XRE4RMdkLFvDwObGiJZzZ1UmOS8tZp5wvQwFYEuVQ+Ui/9ZMaC/zS/bCf1ktZtwsk3QZNmwYJk+ejBkzZuCdd97BG2+8gVmzZuHCCy+MTzL74osvMHToULzzzjvxx5WXl2PdunXYtGkTAODDDz/EunXrsHv3bgAtgwUWL16MDz74AJs3b8Zjjz2Ga665BhdffDG6dtX3M5hhJmR0BQO/dJ2r7jHMQcgcx+yV7vUyFC66gkI6dJ6byCBjQ1XGthYzWTiWOdwee+wxDB06FKeeeipOP/10HH/88bj//vvjf9/Q0ICPP/4Ye/fujd+2dOlSjBkzBjNmzAAAnHjiiRgzZgyeeeYZAC1reR5//HGcdNJJGDFiBH7961/jmmuuSTiuDvwo1CORE81UDgJoy+S2M91hS2SQsaEqY1uLmRdeFv+HfSyzqIlmgJ5Ws9ZioUF365kJwcqUIOOXSVUZI3CSGaWpW7duWLFiRcq/HzhwIKLRxOvA+fPndzhF7cgjj8Rbb70l6hSFYZgJsVhoMCXU6A4xgDlBxi/ZVRlpLWYemLBeJuzBxxa6Qo0JIQYwq7XMhaqMrBYzExb/lxaI+SCDSAeGGdIaakwIMDEmBRkXqjKeWsy4XsY5uqszrbUOF7KCjSkBJkZ0kHGxKkNEbuAVhGY6W83aah0sZAYbkwJMjElrZPwGGZPWyhCZSkSwMS24tGVakDG1KmNdixkRJRXKMPOffT0wIH+X7tMwWtvAESTcmBheWhMdZGzZHNNrVcaEFjPqWH1JNnIq/W9hLXLdDGBWdSYZ00OJH64EGdOqMrKCjwktZkS2C2WYCUrkEADArOpMKqYHEr9MCzKuVGVktZh5WS/jZfE/kQtMWiOjmklVGWkjmT0s/uckMwoT/StpiTQxLcioxKpMIi7olyfMF9gqyXifWZUhIhswzBjCpDUbYWDi+x3KqgwRBeZSkPFD9r4y3CiTyGwMMwYx8QLbRTLeZ5vay4yqynCKmXFkTK1idUYeE4NMECqqMqYs/Od6GSIxGGZ8sqmliFp82tDdyCBD6TFlvQxb0vxhoBEr2rXI2CCjsr3MpKqMVBI3y+QeM2Q7rWFm4cKFOProo1FUVIRevXrh7LPPxscff6zzlLRjdUYOWe+riCBjclXGK7aYEcknKxjqDDKqWLnw3yMu/qew0RpmXn31VcycORNvvfUWVq1ahYaGBkycOBG1tbU6T0s7BhqxXAwyqoShxUx2pSW3mq0kMazOBOdqkDGxKiOTzS1mZQXmjluncNJ6NbFy5cqEPy9fvhy9evXC2rVrceKJJ2o6K3KFzFCoO8iErSrjpcWMghO930xrpu89YyqZQVDnGhlA3fQyr1WZMLSYEbnAqCuEyspKAEC3bt2S/n1dXR2qqqoSvnSSuVaC1ZlgTA8yQaiaXmbrOOaw7y9jepsPwAqNVzYEGdX/3ZlWlTGlxYwojIz5adDc3Iyrr74axx13HA4//PCk91m4cCFKSkriX/369fP9fKa39wAMNH7ZEGRU//cnuyrjmSEtZqQPA03nZC3yjzEhyISxKmNzixmRiYwJMzNnzsRHH32Exx9/POV95s6di8rKyvjX1q1bFZ6hHgw06ZM1rSzGhCCjqr3Ma1XG1hYzTiZLTUXrEQNNarLfG5uDDKsyqXld/M9JZuQCIz4enTVrFv7yl7/gb3/7Gw466KCU98vNzUVurlnTkjbX98LgnJ1Sn+PThu4YmP2V1OewmYrAZ2uQMZLFVRmGH5JNRcAzIcj45SfImFSV8YzrZYg6pfXjjWg0ilmzZuGpp57Cyy+/jEGDBuk8HaOxQpNcWIKMX7ZXZbwK+3oZG7E600J2S1mM7sX+Maray0zDFjMi8bR+RDpz5kysWLEC//d//4eioiKUl5cDAEpKSpCfb8YP3HSoqM4ArNC0pircmRJkwrron1PM9JI51ay12EV8GKecqQxzIoOMLe1lsqsyJrWYEYWV1iuFJUuWoLKyEieffDL69OkT/3riiSd0npbRwl6hkb0upjXbg4yKRf+eqzIWt5iRfGGq0qiqxMQwyFjIY4sZN8uksNJ6ZRGN8hMNP8JYoVEd4nSPX45RGWRsr8p4bTHj+pf0qKrOxLi+D42OwGZKkDGd7KoMW8yI5Aj1x6T/2dcDA/J3CTmWqlazmNjFveuhxvYQ4/KC/7BVZWwKP/Ul2cipbNB9Gr652HZme4gBggcZVmXM4meSWVmBO/8myR12X12Qk1UaXa10JgWZIFyoyrgit5qfxAZhe5VGZ9tcmIOMH8ZVZTjFjChtDDMCqa7OxLhSpdG5Hsi0IGN6e5nsqgxbzMyiutWstdaBwIZgY8K6H1eCjF9hrMpwvQyFGcOMQ2wMNSYMNAhzkPHDpFHMpI7OQBNjavuZCQEGkDN2WWeQUdVeZlxVhog8YZgRTFd1pjXTQ40JAQaQs8jflhHMMUrayyRXZSh8dFdrTAkvrTHI+GNkFUdBi5mf9TKuy6lpRla22uDZ2MCgKwLDjMNahwadwcaU8NKaadUYIFiQMba9TAEVG2Xa2JYmYwiACdWZtmQHGxODS1sMMi1UBBMT95VhixmFXejDjMiJZjEmVGfaahsoZIUbE4NLayZWYwD1QUYZA6syNgYTSo/X4BHZU21FWElFRogB9I5fVhlk1IQfLvwnki30YSas0gkdscBjekBJF4PMAazKkF8mVmf8sjXImB5iVC/4V8XEqgwRMcxIY2J1xiuGmI7pDjJ+KQsylu8rQ2Qil4NM6KsyPqhsMeMeM2QqrrSVyJRd5MPM5SBjdHuZD6a2mPltS3N5jxlZF9SUWlNxPoNMEqqCjJKqjKIWMy7+J9fwo1NykswgaXOQcakqwxazzskYAhDjUruZyWQHx7AFGT/8BBmOYyZSh5UZyN2pndUZtTbX95JajWGQSYOPIMNxzHZihUYume9vfUl2KIOMkaOYAV9VGU4xI2rByowCLqyfMZ3s0Cgq8NoUZFTxE2T8VGU4xYxsEYZqDKBuL5kYVe1lrMoQqcWPQxVhhUYeBhk5TG0vU4kBqHOszogjc11MjAtBxtQF/74pHMfM9TLkIreuPChUVAREF4KMyWOYVVVlwkzmupkYrp8JRkUgFLl3jG1Bxi+TqzJsMSM6gJWZ/5K5biaG1RkxZK6LiRG1PgawL8j4ZnBVRnWFxeVJZqmwQuOdikoMwCBjdHuZJZtkciwzmczcqw9Hcf2MPyqDoMhga2OQMXnRP6syZmOFpnOqQx+DjO+nNJbfqgxbzMhVDDMaxC7MGWo6Z2uIAUIUZAzntyrD9TL+MNAkZ3OIaTleeIIMqzJEdmGbWSsqWs1aY9tZaipayVpjkAkQZFiVMZ7oC9vOqGqfsoGO98KlIKOaks0xiUgoVmY0Y9vZATrCnWkhBmCQEYnVFb3CWqXRFeRMCzFA8CBjQ3uZyqoMW8yI2mOYMUDYA42uChWDTEAKF/yrrsowBIkTlkCjsxIlo/IWxiBj8vQyIkqNYcYQYVtHo7PFTkY7oe4gE4TKdTIqqzK6mDrJTMWI5lRiF/quhRoTWukYZMQ8Tml7meKqTFCcZEamY5hp4z/7emBA/i5tz+9yqDFhjZCrQcaG9jK//FZlWF0xjwuhxoQAA5gbYoDwBBlbqjJsMSPXMcwYyoVQY0J4iTE1xAD6WstUBxlbqjIMQfK1DgSmBxtTwkuMrGEOYQ0yfvkOMpZVZYhswDCThO7qTGu2hRqTAgwgb0Idg4w3foMMqzJi6Ww1S8W0YGNaeGmNQUY8Ti8jsh/DjCVahwRTgo1pwaU100MMYGGQUYyjmMOnbZCQGW5MDi1tmR5iAL1Bxpr2Mg1VmaAtZlwvQzZgmLFQ2xChItyYHFxak7lXkAnVGEBjkLGkvSxIVSZoRcfUxf+tmVidSaWzwJEq7NgUVDoic38gU6oxAIMMEQXDMJOCSa1mnUknaCQLPLYElHQxyEhkSXsZhYsroaUtG0IMEL4gowPXyhB1jmEmJFwLLq3ZEGIA/UHGlnUyQeisytjEpupMmMgMMS3HNyfIBF0foyPI2FaV4RQzCguGmQ7YVJ0JI5khBmCQAaB0BHMMqzIUNmEKMYC+IBOEjiCjuyrD9TJkC4YZsorsAAOYFWIAO4OMjvYy3ZUVG9bLtMbqjH6yQ0zLczDIxLi+n0xrrMpQmDDMdILVGTPYFmIABhmVggYZ3UGIwkNFgGl5HnEhBmCQ8cXiqgyRTRhm0sBAo4+KEAMwyIgSJMiwvUw9VmfUCXOIAewMMoFonF7GqgyFDcMMGSnMIQbQHGQ0LPjX2V4W9qoMA41cqkJMy3MxyLSlZcF/QCZUZbhehmzCMJMmVmfkUxVgYkwMMtqrMVzw74tt62VILpUBpuX5xIYYwP62MkBjkNHYXsaqDIURw4wHDDRyMMS0sDnI6FgnA7AqIwqrM2IwxBzAIEO2yalsQlZ2k9LnzGhQ+3yuYpjxiIFGDNUBBhAfYgAGGcDe9jJKxEDjj+oAc+B5GWRSsTXIsCpD5A/DDCnFENMeg4weosKQSy1mDDTp0RVgWp7b3RAj4hg2rpEBzFgnE8P1MmQbhhkfWJ3xRkeAAeSEGMCcagwQ3iDDqow8DDTJ6QwwLc8vPsQADDIHHhswyGhuL2NVhsKMYcYnBpqO6QowMa5XYwB7g0xQIoIMw1DHGGha6A4wLedgdogBGGR0t5cRhR3DTACxC3aGmha6AwxgfjUGYJDR3V4mkkstZm2FNdCYEGAAeSEGcKsaAzDIiKzKsMWMbMQwI0CYqzQmBBiAISZtAUcv6w4yrMqoFbuwdz3UmBJgADtCDMAgA8CIIENEDDPChCXQmBJeYmSFGIBBpi0Xggz541qoMSm8xNgSYgAzgkyQENPyeLvXyMSwKkPEMCOUq21npgUYIFwhBmCQEUVkIHK5xSwVW0ONieElRmaIAcyrxog4jgtBxrT2MiKbMcxIYHuoMTG8xNgSYgBDqjGAE0GG7WVmMTnUmBxcWrMpxABmVGMABhkiao9hRqLWocDUYGNycGlNZogBHK3GAAwyJFXr4KAj2NgSXFoLY4gRdRztQUYAUUFGdFWGLWZkM4YZRUwINrYElxjZAQYwsxoDMMjEiAoyogNRGFvMOpMqWAQNOTYGlrZkBxhAfIgBGGTaMWTBP9vLiBIxzGiQLFSICDi2hZVUGGICChhiAAYZEseFMOKXihADsBrT+TH0BxmTsSpDtmOYMYQrQSQIG0MMwCDT/vHmBBkZWJWhztgaYgAGmaQMWifDqgxRewwzpJ2KEAOYW40BGGRkMTkUkXsYYsQdx4ggI6gaY3KQYVWGXMAwQ1qoCjBACKoxgPb1MS3HEBNkTG4vY1WG2lIVYACzQ4zIYzHIEJEXDDOkFEPMAa5UY1qOYVaQIZLN9hADmBdkjAgxgJFBhlUZotQYZkgJhpgDTKnGAO4GGVZlSAaVAQYIT4gBGGQ6wnUyRB1jmCFpVAYYQE6IARhkOj5GOIIMhRtDjLzjiQgxLcdhkPGCVRlyCcMMCccQ055JIQZgkPGKVZnwUR1gAHkhBnC3GtNyHAYZojBjmCEhVAcYwI4QA5gVZESEmJbjMMiQmxhi1BzLmLYywMggIxOrMuQahhkKxKUQA7Aak95xwhNkKBx0BJiYMLWUAYZVYwBjgwzby4jSxzBDnukIMIA9IQZwM8iI3EPGlvDBqoy7XAwwgLkhBjAsyAgKMYA9QYbIVQwzlBZdAQYIaYgBnGwrA8QHGVuCEZnB1RADmBtkjAoxQGiDDKsy5CqGGUpJZ4ABGGKCYpAJhlUZd+gMMEB4QwzAIOMFgwyRPwwz1I7LIQZgkPF2HPPWx8g8ZgyDjP10BxiAIUbMcQT+WzR0fQzA1jKiIBhmCID+AAMwxIhgWogBGGRIHRMCDGBfiBF9TOOCjMHVGEB+kGFVhlwn9ycuGe3zfaXxL53Ka4ukt5QxyHg5TniDDNmnviQz/qVbXVGG9MX9MqoxItfGMMh4wyBDsuzevRtTp05FcXExSktLcdlll6GmpuNfoD/4wQ9w8MEHIz8/Hz179sRZZ52FDRs2JNzns88+wxlnnIEuXbqgV69e+MlPfoLGRnH/zvxgZSZkdAeX1myrxABuh5iWY4U7yLAqYw8TwkuM7EoMYHZLGWBgNQZgkGGQCbWpU6di+/btWLVqFRoaGjB9+nRcfvnlWLFiRcrHjB07FlOnTkX//v2xe/duzJ8/HxMnTsSWLVuQmZmJpqYmnHHGGSgrK8Obb76J7du345JLLkF2djZuvvlmha8uUSQajYr5CaRBVVUVSkpKcPmr5yGnMFv36RjLpAADMMSICjGAmdUYgEFGhJzKJt2nYByTAgxgZ4gRfUxRIablWOZVYwAGGREaauvx/yb9LyorK1FcXKz7dBLEriWPPW0BsrLzlD53Y8N+vLlqnvD3Zf369Rg+fDjeffddHHXUUQCAlStX4vTTT8fnn3+Ovn37pnWcf/zjHxg1ahQ2bdqEgw8+GM8//zz+53/+B9u2bUPv3r0BAEuXLsXPfvYzfPnll8jJyRH2Grxgm5mjTGkha83WdjITg0xWTYaRQSa7Rs7EsrAFGTrApDayGNntZED4WspMDDL5hXVSJpaFLchQ56qqqhK+6uqC/Xe3Zs0alJaWxoMMAEyYMAEZGRl4++230zpGbW0tHnzwQQwaNAj9+vWLH/eII46IBxkAmDRpEqqqqvDPf/4z0DkHwTYzh5gUXFqTXYkBWI3xf6xwt5UBDDKmMSm0tMVKTAtWY/zhxDKz5VQ1ICtL7c+fjMYGAIiHhZh58+Zh/vz5vo9bXl6OXr16JdyWlZWFbt26oby8vMPH3nffffjpT3+K2tpaDBkyBKtWrYpXXMrLyxOCDID4nzs7rkwMM5YLc4ABLAgxgPNrYwAGGQom7AEGYIgJhEEmjlUZO23dujWhzSw3N/l1yHXXXYdbbrmlw2OtX78+0LlMnToVp512GrZv347bbrsN559/Pt544w3k5altwfOCYcZCpgYYgCEmAasxxh23NQYZvUwOMIDdIUb0cY0NMYDxi/wBBhnqXHFxcVprZn784x/j0ksv7fA+gwcPRllZGXbu3Jlwe2NjI3bv3o2ysrIOH19SUoKSkhIceuih+PrXv46uXbviqaeewkUXXYSysjK88847CfffsWMHAHR6XJkYZixhcoABGGIShCDEAAwy5J3pAQZgiGnL2CBjQTUGYJAhsXr27ImePXt2er/x48ejoqICa9euxdixYwEAL7/8MpqbmzFu3Li0ny8ajSIajcbX8IwfPx6//vWvsXPnzngb26pVq1BcXIzhw4f7eEViMMwYzPQAA9gdYgAGGb9sbSsDGGRUsyHAAAwxbRkbYgBWY9pgkKG2hg0bhsmTJ2PGjBlYunQpGhoaMGvWLFx44YXxSWZffPEFTj31VDz88MM45phjsHnzZjzxxBOYOHEievbsic8//xyLFi1Cfn4+Tj/9dADAxIkTMXz4cHz3u9/Fb37zG5SXl+P666/HzJkzU7bGqcAwYxgbAgzAENMOQ4yRx22LQUYNWwIMwBDTlsgQ03I8VmNkYYihjjz22GOYNWsWTj31VGRkZODcc8/F3XffHf/7hoYGfPzxx9i7dy8AIC8vD6+99hoWL16MPXv2oHfv3jjxxBPx5ptvxqswmZmZ+Mtf/oIrrrgC48ePR0FBAaZNm4Ybb7xRy2uMYZjRzJbwEsMQ04ahIableAwyrTHIyGNTeImxPcTIOLax1RjBIQZgkCH3devWrcMNMgcOHIjWW0327dsXzz33XKfHHTBgQFr3U4lhRgPbAgzAENOOwBADsBojG4OMeDYGGEBdiAHCWY0xuaUMsD/EAAwyRG0xzCjCANMxWSEGMDvIsBojH4OMOLYGGIAhJpkwtZQBDDJErmKYkcTG8BLDEJMCqzHGH7stBplgbA4vMQwxybEaE5zqTTAZZIiSY5gRyOYAAzDEpBSiEAOwGhN2DDDeMcQIYkmIAViNITIJw0xAtgcYQG2IASxaFwOwpcyCY7fFIOOdCwEGcCfEyDg2W8rEYDWGyDwMMx65EF5iGGI6EKIQA7hRjQEYZNLlSnhpzYV2MhnHNjrEAKzGpMAQQ5Q+hpk0uBRgAIaYDrGlzIpjt8UQ0zkXAwzAENORMLWUAazGEIUVw0wSroWXGFdCDBCudTEtx2NLWSoMMsm5Gl5iGGJSM7oaY1GIAViNIbIBw8x/uRpgAIaYTrGlzOjjpsIQ057rAQZwZ11M6EIMwJayDjDIEPkX6jDjcoABGGI6ZXA1xqYQI/vYyYQhyNSXZCKnsqnT+4SFKyFGxrHDFmIAd4IMQwxRcKEJM64Hl7Y4ZrkDBoeYluOxpSyVMISY1pIFmjAFGIAhpiMMMWKxGiPHQfkVqG9q0H0a5DCnw0zYAgzAakyHQhZiAAYZF4QtvMQwxHTM6CDDENOhsIQYoCXIEMnmbJgJW5BhiOkE18VYcexkwhpiwoohpmNGhxiA62I6EKYQAzDIkDpOhpkwBRmGmE4YXI1hiOkYQ0z4MMikFrYQAzDI2IxBhlRS+5ujjb/97W8488wz0bdvX0QiETz99NOBjvf5vtLQBJny2iLl62Jk7hcjpaVMcDXG5CCTXSN3SpnqPWMYZMKlrihD+ahlmVPKTA4yjYXN4lvKJFRjZO4Zo3qBf5iCzEH5FQwypJzWykxtbS1GjRqF733vezjnnHMCHSssIQZgNaZDBldiWo7HakxHwhZiuLiflZiOGF2NYSWmQ2ELMEQ6aQ0zU6ZMwZQpUwIf54v9JcguEHBChmOI6QTXxVhx7GTCFGI6GrecU9kUmkDjSpAJXYgBuC6mA2EIMQwvZBqr1szU1dWhru7AD72qqiqNZ6MOQ0wnDK7GMMR0LAwhprO9YsLGlRAj49hhCzGAvCDDECMWAwyZzKows3DhQixYsED3aSjFINMBg0NMy/G4X0wqLocYhpfUVK+LsenYotfFCGVRiAG46aUIDC9kE6vCzNy5czFnzpz4n6uqqtCvXz+NZyQXN75MIWQhBpC7uF8lV0MMA0zHXKnGmB5iWo7HdTEquBhiGGDIVlaFmdzcXOTmSmhTMowr1Ri2lAXHEGMmhpf0sRqTnNEhBuC6mBRcCjEML+QKq8JMGLAakwJDjBXHbsulEMMA4w2rMamFqaWMIcYsDDDkIq1hpqamBps2bYr/ecuWLVi3bh26deuG/v37azwz9ViNScHgENNyPK6LScaVEMMA4w+rMckZXY2xqKWMIcYbBhhyndYw89577+Eb3/hG/M+x9TDTpk3D8uXLNZ2Vei5UY0xvKWOIUcP2EMPwEpwLQYYhJhiGGL0YXvzJrN6PzEyx/1Y7E22SV7kME61h5uSTT0Y0qvY/HNMwyCRhcDXGppYyhpj0MMCIoyrIhLUaE9aWMoaYzjHAUJhxzYwmDDEpsBpj7HGTsTXEMMCIxWpMe2GqxjDE6MEAQ9SCYUYDBpkkGGKMPW4ytoUYhhd5GGTaM7YawxDTjk0hhuGFKDmGGcVUBRlrQgxgbJBhiGnPphDDACOf7W1loQkxgBVBhiGmPQYYos4xzCjEINMGQ4yRx22LAYaSYZBJZGyQsSDEAOqCjA0hhgGGyBuGGUUYZFoJ0QJ/GYGDISYRA4x6NgcZhhh/bA4xDDBEbmOYUcDmIMNqjD8MMfIwvOjFIHNAGIIMQ4w8DDBEYjDMSKYiyIStGhO2ECPzuK2ZHGIYYMygIsjY0FYWhhAD2LsuxtQQw/BCJAfDjEQMMv8VgiDDECNeWANMTmUT6ksydZ9GO7YGmVBUYxhiAJgZYhhgiORjmJHE1iBjaluZqSEGsLelzMQQE9YAYzoGGUNDDGB8S1kYQwwDDJFaDDMSMMjAyGoMQ0wL00IMA4zZGGQMDTIMMUaFGAYYIn0YZgRjkIHz1RiGmOAYYOzAICMuyISlGhOmEMMAQ2QGhhmBbAwyJoYYwMxqjI0hhgGGTCY6yJgYYlqOxWqMCAwxRJQMw4xFwhBkwlKNCUuIYYCxl+yqTBiCDENMcAwwRNQZhhlBZFdlGGS8HIchRicGGPsxyIg4jvtBxvUQwwBDZAeGGQEYZIIzLcjY1lLGEEOihDXIMMSkz+UQwwBDZB+GmYBCG2QcDTGAXdUY3SGGAYa8YJBJk6AgwxCTHgYYIrsxzBiMQSbd45hZjXE1xDDAuEtmVYZBJg0hrMYwxBBRUAwzAcisyogMMq62lZlajXExxDDAUBAuBxlWY/zRFWIYYIjcwzDjE4OMP6zG+MMQQ7LJqsowyKTBwCDDEENEtmCYMYyRQcbBagxDTGoMMOHDIOPnGOa0lTHEJMcAQxQODDM+yKrKMMh0dgzzggxDDFFyJgYZF9fHmB5kVIcYBhii8GGYMYSrQcaktjKGmPYYYEj2KGYRTAkyDDHpY4ghIlUYZjySUZVhkOnoGOGoxjDEkEtEVmUYZNoTFWRcaCljiCEihhmHMMi0xxBzAAMMtSWjKsMgk4KAIMNqTAsGGCJqjWHGA9OrMkIwyEg/FsAQQ25ikEnBoCDDEENErmGY0ci49jIDgoxpbWU2V2MYYqgjJq+VcSbIGBRiAPFBhiGGiEzAMJMmmfvKBGVCkHGtGmNriGGAIV1EVWUYZA4wtRqjIsS4FmAG5O/y9bj/7Osh+EyI3MMwo4moqgyDzAGmBhmGGG9yKht8Pa6+JFvwmbhLdFXGpSDjUluZjS1lNoYYv0ElyLEZcogOYJhJg+iqDINM22MECzIMMXbxG1b8Hpchxw4MMi3CWo2xJcTIDC5etD0PhhsKM4YZS7kQZFiN8c+GECMrtHjV+jwYbMytygSlPcgYUo0BxAaZMIcYU4JLOmLnylDjX6SiGpGMerXP2SxuTVyYMcx0wtSqTGAMMsKO0VqYQ4wp4aUjsXNkqBHDlPYyBpkWDDHB2BReUmGooTBimFHImPYyy4OMidWYMIYYG8JLKgw15gh7kAlbNcakEONCeEmFoYbChGHGMgwygR4u7BityQwyJoUYm8NLKmELNSJbzERUZXS3qLkQZBhivHE5wCQzIH8XAw05j2GmAyJbzERUZRhkAj1c2DFiwhBiXAwwyYQt1LgiSFWGQSaRrCBjQogJW4Bpi1Uach3DjCUYZAI9XNgxALdDTFjCSyo5lQ0MNGkwoSoT5iDDENOxsIeXVFilIVcxzKRgWlVGJ51BxqQQA8gLMjpDTNgDTFuuBhrRU8yCYJDxT1SQYYgJJwYachHDjGS2t5cxyLRwLcQwwHTM1UAjgv51LnYGGZPaymQEGV0hhgHGOwYacg3DjOEYZPwzOcgwxJiPgUaOIGEo6OSyQBhkktIRYhhggmOgIZcwzEikvb0spEGGIabtczLA+OVKoBHVYha0KqOzqhOoKqMxyJgaYgD1QYYhhoiSYZhJQvRGmX4FrspowiDTnuoQwwAjjiuBxnba2ssYZNpRGWIYYORhdYZcwTAjSdCqjK3tZbYHGdurMQwxcjDQ6K3KMMj4wxBDnWGgIRcwzLiIQcYXW6sxDDDUEROmmDHIeBPWIMMAowcDDdmOYaYNE1rMAlVlQhZkTGwrY4hxU5irM7onmPlhY5BhiCEi8o5hRgLtC/99CGuQsbEawxBDNrFuehmDDACGmLBhdYZsxjBjGB1VGQaZ4BhiwiGM1RldVRkt7WUMMgDkBxmGGCISiWFGMG1VmQDtZX4xyLSQHWQYYsgv3etldAQhBhn/GGLCjdUZshXDTCu618voGMXstypja5BhiKGgwlidUc1vVSZsQcaWagxDDBHJxDAjkG1VGQYZ/2QGGYYY84Ul0ASprPh9rJZ1Mj65EGQYYloMztkp7dib63tJO7ZorM6QjRhmDOG7KsMgkxZRQYYhpkVmlZjN/NpqKrZveAaZQXVVhkEmNZNDjMzQ4uU5bQo4RKZjmCElGGQ6Oq65IUZWaEn3+Rhu9LGpKsMg452MIGNiiNERXtLR+rxMCzaszpBtGGb+K+h6mSAtZq5XZWwPMmEKMarDS2dan49pwcb0VjPdi/9VYZDxxvVqjKnhpSMmBxsiGzDMhExYgozJ1RiTQoxp4aUjJgcbOsCKtTKWBRlWYzpmY4BJZXDOTgYaIo8YZjRTXZXxg0FGHBOCjE0BJpXYa9Adakyvzvila28Zr3xXZXxgkEmkO8S4FGDair02naGGrWZkE4YZAZRPMdOwOaZXNgcZ10KMC+ElFVNCDbVQWZXRMYbZD91BxqVqjMsBJhkTQg2RDRhmNFK5r4yO6WV+uBZkGGLUyKzax0BjKZfby1wLMjpCTNgCTDJsPSPqWDhWiHZC92aZnjjcXsYgE1xm1b74V9joes0mtA62FWTxv9/qisrWNBvayxhkghmcs5NBphUd74XuVkKidDHMBOS3xcz0qgyDTJBjNSi9wA1zgGmL74NdTK/KMMi0XNCquqiNBRiGmOT43pAXu3fvxtSpU1FcXIzS0lJcdtllqKlJ72IrGo1iypQpiEQiePrppxP+LhKJtPt6/PHHJbyC9LHNzCYKqzJ+2BhkbK7G8KI9NbaductXVUbhOhmdQcbWagwv0L1h2xmlY+rUqdi+fTtWrVqFhoYGTJ8+HZdffjlWrFjR6WMXL16MSCT1h9oPPvggJk+eHP9zaWmpiFP2zeyrYwpMVVUm7EGGIcY8DDTeqWwxU7ro34cg08v8MCnIMMSYT1Wg4VQzO61fvx4rV67Eu+++i6OOOgoA8Nvf/hann346brvtNvTt2zflY9etW4fbb78d7733Hvr06ZP0PqWlpSgrK5Ny7n6wzSwApS1mPqoyKtvL/GCQ8YYtVN6per9MXDdD/2VBe1nYggzbpcTge+iOqqqqhK+6umAfpqxZswalpaXxIAMAEyZMQEZGBt5+++2Uj9u7dy++853v4N577+0wrMycORM9evTAMcccg2XLliEaVdgynAQrMxRYkFDiR9iCDANMMKzQyKVq4b+q9jIGGXl48S0eW87Ead5TgeZIjtrnjNYDAPr165dw+7x58zB//nzfxy0vL0evXon/XWRlZaFbt24oLy9P+bhrrrkGxx57LM4666yU97nxxhtxyimnoEuXLnjxxRfxox/9CDU1NZg9e7bv8w0q9GFG9SQz16oyqtvLTAkyDDF2CVOgCTLJTBWlC/8VsD3IMMQQ6bV161YUFxfH/5ybm/xa8brrrsMtt9zS4bHWr1/v6xyeeeYZvPzyy/j73//e4f1++ctfxv//mDFjUFtbi1tvvZVhhvRjkPFyHLlBhiFGjjAFGj9Ujlb2yuSqDINMagwxasiuznDdjHzFxcUJYSaVH//4x7j00ks7vM/gwYNRVlaGnTsT//01NjZi9+7dKdvHXn75ZXzyySftFvOfe+65OOGEE/DKK68kfdy4ceNw0003oa6uLmUIk41hxie/62VU8LtBpgoMMqkxxNgtp7IB9SXZuk9DKVUL/z1T2F6mA4MMtcV2s3Do2bMnevbs2en9xo8fj4qKCqxduxZjx44F0BJWmpubMW7cuKSPue666/D9738/4bYjjjgCd955J84888yUz7Vu3Tp07dpVW5ABGGaUUtVi5pWqqgyDTHIMMeqwOmMflRPMvNJRlRERZBhi3MRAQzHDhg3D5MmTMWPGDCxduhQNDQ2YNWsWLrzwwvgksy+++AKnnnoqHn74YRxzzDEoKytLWrXp378/Bg0aBAB49tlnsWPHDnz9619HXl4eVq1ahZtvvhnXXnut0tfXFsOMY1RUZVQv+A9CRJBhNcYtDDT6uFSVYZBJxBBDZJbHHnsMs2bNwqmnnoqMjAyce+65uPvuu+N/39DQgI8//hh79+5N+5jZ2dm49957cc011yAajeKQQw7BHXfcgRkzZsh4CWljmDGZoVUZP3RUZUwOMgwxejHQJPLTLqZijY2KqgyDTHAMMuZgdYZiunXr1uEGmQMHDux0pHLbv588eXLCZpmmYJjxwc96GV8tZh6ZWpVhkDmAIcZtYVw3YxQfVRlVXAwyNoSYgdlfCT3epw3dhR5PBhmBhkMAyGShDjOqxzJ74lBVxg8GGT0ie4JNWGot2tXgf19wszpj8lhmry1mrlVl/GKQSZ/o4JLuc9gQcIhcFuow4xKXqjKuBRkTQ4zI0JLuc5gebih9Ro5xVlCVUd1eZmKQMSnEqAgv6Wh9HqYEG7abUZgwzCigosXMK69VGZXtZX4xyHRMRYBJ9/lNCTYuVmdMZOImmapGMTPIiGVKgEkldn6mhBqiMGCY8UjJ/jIeW8xM3FdG9ToZBpn2dIeXjsTOzZRQE2YmVlk8t5h5rMqoai8LujFmECKDjO4QY3qAScaEUMPqDIUFw0wIqajK+OFKkNEZYkwOMMmYUK0RXZ3hEAD32LZOxoUgY2OAScaEUCMKhwCQqRhmSDjV62SCciHI2BZikonsqWalxgJeKznSF/4rqMr4oau9zPYg40qIaculUENkGvP6kxzjeb2M5BYzU6syfgWtytgeZCJ7qp0IMjG6Xo/ulkAyl8r2sjAHmYHZXzkbZFpT/Rp1twgSqcDKDAmlsioT1iDjUnhJhVUaSouBVRkGGW/CEGDaGpj9FSs0RAIxzHigZPG/B6ZVZcIYZBhi5GGg8cbPHjOyF/+buLeMF6rXyvglKsgwxKijMtBwEAC5LrRhRsWGmbJbzMIqbEEmbCGmNZVTzzimuXNGTT6TXJWxpb3MtiAT9hDTGtfREInBNTMh4VJVJggGGTvxfXCfaVUZrxhkOhaWNTF+8H0hCoZhxlIm7i3jhY72MpuCjGsL+0VQ8X6I+t6K3rOIglFRlfEjTEGG9BL1fRa9ESuRCHZfEbtEYouZiVUZP8IQZBhiOsb3xh5e18t44rHFTDadm2MGITvIsBqTPr5PRP4xzJByqtvLbAoyRKp4Xf8ic72MzBYzVmWSkxlkGGL84XtG5A/DTJq8TjLzvPjfA5ktZi5WZUSQGWRYjfFG9nvFPWcM50BVxvUgQ/7JfP+45wy5imHGcV5bzGTzU5XR3V4mO8iQd3zfzCa1xcwDU6syfjHIhAPfRyJvOAvYBIaMZJZdlbGxvUxWkOHFeHDch8YNNk8xU12VCUpWkOHFNxHpxMqMZWyfYuaV36oMg0w48L0MGYktZq5XZRhk7CLrfWWrGbkoXFfGishcL+OFzBYzFVUZnetkGGTCjetmEhm1WWaavLaYeaWyKmPqOFwGGbn4/hKlx4gwc++992LgwIHIy8vDuHHj8M477+g+pdBRtZBfhaBVGQYZu/B9NYsp62W88FqVsS3IyPg0nhfaRGQK7WHmiSeewJw5czBv3jy8//77GDVqFCZNmoSdO+WVQstrDeqz97BexpQWM5OrMiZuVshpZeGk8r/FuiJvPxtMqbR4Wi/jocVMdlXGJgwydjPxvTa1Ukjhpf3q+I477sCMGTMwffp0DB8+HEuXLkWXLl2wbNky3acW53Usswm8tJi5VJUJSnRVhiFGDb7PpIpNVRnRQYb7x7iB62bINVrHaNXX12Pt2rWYO3du/LaMjAxMmDABa9asaXf/uro61NUd+MStsrISANBQW+/peRtrvX1q17TX29qT5n0e2iz2pf8taN6ffvZsqkv/nDM8vB3ZtYCXmklOTTO8LtnNqWzy/BgAyKlq8PW4mMzq/YEe31akghfYKkWbcsQecE8dmoryAh2iscFfhbGxIdPT/ZvqvX0u1eSxcNGUnf59m7PS//nXnOmhMpOZ/r/OJg8/1Eq67Edjbfqn0QBvv28AoL7JX5Vuf1Own0h7s8WuO6zJtmPy3MA0/xv8tNGsrQtS6YEv8VlDN6HH3N8Q7L+t+n3e/puur225fzRqbhtqY1R9Z4eO53SR1jCza9cuNDU1oXfv3gm39+7dGxs2bGh3/4ULF2LBggXtbn/xnIelnSMRWWKb7hMgIpLlC90nIER1dTVKSkp0n0aCnJwclJWV4W/lf9by/GVlZcjJEfxhXMiYscFJmubOnYs5c+bE/1xRUYEBAwbgs88+M+4fR9hUVVWhX79+2Lp1K4qLi3WfTqjxe2EGfh/Mwe+FOfi9MIPq70M0GkV1dTX69u0r/bm8ysvLw5YtW1Bf773qKkJOTg7y8oJ1AYSd1jDTo0cPZGZmYseOHQm379ixA2VlZe3un5ubi9zc9mOPS0pK+EPREMXFxfxeGILfCzPw+2AOfi/Mwe+FGVR+H0z+0DkvL4+BwmJaBwDk5ORg7NixWL16dfy25uZmrF69GuPHj9d4ZkREREREZDrtbWZz5szBtGnTcNRRR+GYY47B4sWLUVtbi+nTp+s+NSIiIiIiMpj2MHPBBRfgyy+/xA033IDy8nKMHj0aK1eubDcUIJnc3FzMmzcvaesZqcXvhTn4vTADvw/m4PfCHPxemIHfB3JJJGrynDwiIiIiIqIUtG+aSURERERE5AfDDBERERERWYlhhoiIiIiIrMQwQ0REREREVrI6zNx7770YOHAg8vLyMG7cOLzzzju6Tyl0/va3v+HMM89E3759EYlE8PTTT+s+pVBauHAhjj76aBQVFaFXr144++yz8fHHH+s+rVBasmQJRo4cGd+Mbvz48Xj++ed1n1boLVq0CJFIBFdffbXuUwmd+fPnIxKJJHwNHTpU92mF1hdffIGLL74Y3bt3R35+Po444gi89957uk+LyDdrw8wTTzyBOXPmYN68eXj//fcxatQoTJo0CTt37tR9aqFSW1uLUaNG4d5779V9KqH26quvYubMmXjrrbewatUqNDQ0YOLEiaitrdV9aqFz0EEHYdGiRVi7di3ee+89nHLKKTjrrLPwz3/+U/ephda7776L3/3udxg5cqTuUwmtESNGYPv27fGv119/XfcphdKePXtw3HHHITs7G88//zz+9a9/4fbbb0fXrl11nxqRb9aOZh43bhyOPvpo3HPPPQCA5uZm9OvXD1deeSWuu+46zWcXTpFIBE899RTOPvts3acSel9++SV69eqFV199FSeeeKLu0wm9bt264dZbb8Vll12m+1RCp6amBkceeSTuu+8+/OpXv8Lo0aOxePFi3acVKvPnz8fTTz+NdevW6T6V0Lvuuuvwxhtv4LXXXtN9KkTCWFmZqa+vx9q1azFhwoT4bRkZGZgwYQLWrFmj8cyIzFBZWQmg5SKa9GlqasLjjz+O2tpajB8/XvfphNLMmTNxxhlnJPy+IPU2btyIvn37YvDgwZg6dSo+++wz3acUSs888wyOOuoonHfeeejVqxfGjBmDBx54QPdpEQViZZjZtWsXmpqa0Lt374Tbe/fujfLyck1nRWSG5uZmXH311TjuuONw+OGH6z6dUPrwww9RWFiI3Nxc/PCHP8RTTz2F4cOH6z6t0Hn88cfx/vvvY+HChbpPJdTGjRuH5cuXY+XKlViyZAm2bNmCE044AdXV1bpPLXQ2b96MJUuW4NBDD8ULL7yAK664ArNnz8ZDDz2k+9SIfMvSfQJEJNbMmTPx0UcfsSddoyFDhmDdunWorKzEn/70J0ybNg2vvvoqA41CW7duxVVXXYVVq1YhLy9P9+mE2pQpU+L/f+TIkRg3bhwGDBiAJ598kq2XijU3N+Ooo47CzTffDAAYM2YMPvroIyxduhTTpk3TfHZE/lhZmenRowcyMzOxY8eOhNt37NiBsrIyTWdFpN+sWbPwl7/8BX/9619x0EEH6T6d0MrJycEhhxyCsWPHYuHChRg1ahTuuusu3acVKmvXrsXOnTtx5JFHIisrC1lZWXj11Vdx9913IysrC01NTbpPMbRKS0tx2GGHYdOmTbpPJXT69OnT7kOVYcOGse2PrGZlmMnJycHYsWOxevXq+G3Nzc1YvXo1+9IplKLRKGbNmoWnnnoKL7/8MgYNGqT7lKiV5uZm1NXV6T6NUDn11FPx4YcfYt26dfGvo446ClOnTsW6deuQmZmp+xRDq6amBp988gn69Omj+1RC57jjjms3tv/f//43BgwYoOmMiIKzts1szpw5mDZtGo466igcc8wxWLx4MWprazF9+nTdpxYqNTU1CZ+ubdmyBevWrUO3bt3Qv39/jWcWLjNnzsSKFSvwf//3fygqKoqvHSspKUF+fr7mswuXuXPnYsqUKejfvz+qq6uxYsUKvPLKK3jhhRd0n1qoFBUVtVszVlBQgO7du3MtmWLXXnstzjzzTAwYMADbtm3DvHnzkJmZiYsuukj3qYXONddcg2OPPRY333wzzj//fLzzzju4//77cf/99+s+NSLfrA0zF1xwAb788kvccMMNKC8vx+jRo7Fy5cp2QwFIrvfeew/f+MY34n+eM2cOAGDatGlYvny5prMKnyVLlgAATj755ITbH3zwQVx66aXqTyjEdu7ciUsuuQTbt29HSUkJRo4ciRdeeAGnnXaa7lMj0uLzzz/HRRddhK+++go9e/bE8ccfj7feegs9e/bUfWqhc/TRR+Opp57C3LlzceONN2LQoEFYvHgxpk6dqvvUiHyzdp8ZIiIiIiIKNyvXzBARERERETHMEBERERGRlRhmiIiIiIjISgwzRERERERkJYYZIiIiIiKyEsMMERERERFZiWGGiIiIiIisxDBDRERERERWYpghIjLIkiVL0L9/fxQUFOCcc87Bl19+qfuUiIiIjMUwQ0RkiD//+c/4yU9+gt/+9rd47733UF1djW9/+9u6T4uIiMhYkWg0GtV9EkREBIwdOxannXYaFi1aBADYtm0b+vXrh1dffRXHH3+85rMjIiIyDyszREQG2LNnD95//32cccYZ8dv69u2Lww8/HC+99JLGMyMiIjIXwwwRkQE2b94MADjkkEMSbj/00EPjf0dERESJsnSfABERAXv37gXQEl5aq6urw1lnnaXjlIiIiIzHMENEZIAuXboAAF555RWUlpbGb7/qqqvif0dERESJGGaIiAwwePBgAEBxcXFCq9n+/fvjf0dERESJuGaGiMgAXbt2xdixY/Haa6/Fb6upqcGaNWtw2mmnaTwzIiIic3E0MxGRIZ566ilcccUVePjhhzFo0CBcf/312LlzJ/7617/qPjUiIiIjsc2MiMgQ3/rWt7Bjxw5cdtll+OqrrzB58mQ8+eSTuk+LiIjIWKzMEBERERGRlbhmhoiIiIiIrMQwQ0REREREVmKYISIiIiIiKzHMEBERERGRlRhmiIiIiIjISgwzRERERERkJYYZIiIiIiKyEsMMERERERFZiWGGiIiIiIisxDBDRERERERWYpghIiIiIiIr/f/NSOsnwk7qJwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sympy import symbols, Function, latex, cos, sin\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Define variables\n", "theta, delta, beta, alpha, phi, hbar = symbols('theta delta beta alpha phi hbar')\n", "\n", "# Define the expression\n", "expr = (hbar/2) * (cos(2*delta)*cos(theta) + sin(2*delta)*sin(theta)*cos(beta - alpha - phi))\n", "\n", "# Create a 2D grid for theta and phi\n", "theta_vals = np.linspace(0, 2*np.pi, 100)\n", "phi_vals = np.linspace(0, 2*np.pi, 100)\n", "theta_grid, phi_grid = np.meshgrid(theta_vals, phi_vals)\n", "\n", "# Set constant values\n", "delta_val = np.pi/6  # example value\n", "beta_val = np.pi   # example value\n", "alpha_val = np.pi/6  # example value\n", "hbar_val = 1        # example value\n", "\n", "# Calculate the function values\n", "z = (hbar_val/2) * (np.cos(2*delta_val)*np.cos(theta_grid) + \n", "                    np.sin(2*delta_val)*np.sin(theta_grid)*np.cos(beta_val - alpha_val - phi_grid))\n", "\n", "# Create the contour plot\n", "plt.figure(figsize=(10, 8))\n", "plt.contourf(theta_grid, phi_grid, z, levels=20, cmap='viridis')\n", "plt.colorbar(label='Function Value')\n", "plt.xlabel('θ')\n", "plt.ylabel('φ')\n", "plt.title('2D Solution Space')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "mainenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}