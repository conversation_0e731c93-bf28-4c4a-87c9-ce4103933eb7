\documentclass[12pt]{article}%
\usepackage{amsfonts}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{comment}
\usepackage{pdfpages}
\usepackage[a4paper, top=2.5cm, bottom=2.5cm, left=2.2cm, right=2.2cm]{geometry}
\def\shrinkage{2.1mu}
\def\vecsign{\mathchar"017E}
\def\dvecsign{\smash{\stackon[-1.95pt]{\mkern-\shrinkage\vecsign}{\rotatebox{180}{$\mkern-\shrinkage\vecsign$}}}}
\def\dvec#1{\def\useanchorwidth{T}\stackon[-4.2pt]{#1}{\,\dvecsign}}
\usepackage{stackengine}
\stackMath
\usepackage{fontspec}
\setmainfont{Times New Roman}
\setboldmathrm{Times New Roman Bold}
\usepackage{cancel}
\usepackage{amsmath}
\usepackage{tikz}
\usepackage{mathtools}
\usepackage{changepage}
\usetikzlibrary{shapes.misc}
\usepackage{amssymb}
\usepackage{graphicx}%
\usepackage{physics}
\setcounter{MaxMatrixCols}{30}
\newtheorem{theorem}{Theorem}
\newtheorem{acknowledgement}[theorem]{Acknowledgement}
\newtheorem{algorithm}[theorem]{Algorithm}
\newtheorem{axiom}{Axiom}
\newtheorem{case}[theorem]{Case}
\newtheorem{claim}[theorem]{Claim}
\newtheorem{conclusion}[theorem]{Conclusion}
\newtheorem{condition}[theorem]{Condition}
\newtheorem{conjecture}[theorem]{Conjecture}
\newtheorem{corollary}[theorem]{Corollary}
\newtheorem{criterion}[theorem]{Criterion}
\newtheorem{definition}[theorem]{Definition}
\newtheorem{example}[theorem]{Example}
\newtheorem{exercise}[theorem]{Exercise}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{notation}[theorem]{Notation}
\newtheorem{problem}[theorem]{Problem}
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{remark}[theorem]{Remark}
\newtheorem{summary}[theorem]{Summary}
\newenvironment{proof}[1][Proof]{\textbf{#1.} }{\ \rule{0.5em}{0.5em}}
\newenvironment{solution}[1][Solution]{\textbf{#1}}{\ \rule{0.5em}{0.5em}}
\newcommand{\Q}{\mathbb{Q}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\Z}{\mathbb{Z}}
\newcommand{\N}{\mathbb{N}}
\newcommand{\Lagr}{\mathcal{L}}
\newcommand{\lagr}{\mathcal{L}_0}
\newcommand{\lb}{\left\{}
\newcommand{\rb}{\right\}}
\newcommand{\set}[1]{\left\{#1\right\}}
\newcommand{\pr}[1]{\left(\{#1\right\})}
\newcommand{\dbar}[1]{\bar{\bar{#1}}}
\newcommand{\tbar}[1]{\bar{\bar{\bar{#1}}}}
\let\oldhbar\hbar 
\title{Physics 550A Homework 4}
\author{Tyler Hadsell}
\begin{document}
\def\hbar{\oldhbar\mkern1mu} 
\maketitle
\begin{problem}
    Show that if in a representation $\hat J_x$ and $\hat J_z$ are real matrices, then $\hat J_y$ is $i$ times a real antisymmetric matrix.
\end{problem}
\begin{solution}\\ 
    We assume that $\hat J_x$ and $\hat J_z$ are real matrices, and that they are angular momentum operators. We know that the angular momentum operators satisfy the commutation relations
    $$\left[\hat J_i, \hat J_j\right] = i\hbar\epsilon_{ijk}\hat J_k$$
    We also utilize the fact that the angular momentum operators are Hermitian since their eigenvalues are observables.
    We can write out the commutation relation explicitly as the following:
    $$\hat J_i \hat J_j - \hat J_j \hat J_i = i\hbar\epsilon_{ijk}\hat J_k$$
    We can take the complex conjugate of this equation to obtain
    $$\hat J_i^*\hat J_j^* - \hat J_j^* \hat J_i^* = -i\hbar\epsilon_{ijk}\hat J_k^*$$
    Now lets write the specific case where $i=x$ and $j=z$:
    $$\hat J_x \hat J_z - \hat J_z\hat J_x = -i\hbar\hat J_y$$
    Lets now take the complex conjuate of this equation to obtain
    $$\hat J_x^*\hat J_z^* - \hat J_z^*\hat J_x^* = i\hbar\hat J_y^*$$
    Since we know that $\hat J_x$ and $\hat J_z$ are real matrices, we can state the following:
    $$\hat J_x = \hat J_x^* \quad \text{and} \quad \hat J_z = \hat J_z^*$$
    We can substitute these into the commutation relation to obtain

    $$\hat J_x \hat J_z - \hat J_z\hat J_x = i\hbar\hat J_y^*$$
    This implies:
    $$\left[J_x, J_z\right] = i\hbar\hat J_y^* = -i\hbar\hat J_y$$
    Hence we can conclude that $\hat J_y$ is an imaginary matrix:
    $$\hat J_y = -\hat J_y^*$$
    In other words $\hat J_y$ is a real matrix multiplied by $i$.
    Now utilizing the Hermitivity of the $\hat J_y$ operator, we can write the following:
    $$\hat J_y = \hat J_y^\dagger = -\hat J_y^*$$
    We can now take the complex conjugate of this equation to obtain
    $$\hat J_y^* = J_y^T = -J_y$$
    Since the transpose of $J_y$ is equal to $-J_y$, we can conclude that $J_y$ is antisymmetric. Therefore, we have shown that if $\hat J_x$ and $\hat J_z$ are real matrices, then $\hat J_y$ is $i$ times a real antisymmetric matrix.



    \end{solution}
    \begin{problem}
        Show that if an operator commutes with two of the components of the angular momentum operator, then it commutes also with the third component.
    \end{problem}
    \begin{solution}\\ 
        We can begin by stating $A$ is an arbitrary operator that commutes with $\hat J_i$ and $\hat J_j$. We can write the following:
        $$\left[A, \hat J_i\right] = 0 \quad \text{and} \quad \left[A, \hat J_j\right] = 0$$
        Now let us write out the commutation relations for $\hat J_i$ and $\hat J_j$:
        $$\left[\hat J_i, \hat J_j\right] = i\hbar\epsilon_{ijk}\hat J_k$$
        We can now take the commutator of $A$ and $\hat J_k$ to obtain
        $$\left[A, \hat J_k\right] = A \hat J_k - \hat J_k A $$
        Now we assume that $i\neq j \neq k$ so we can rearrange the original commutation relation to obtain
        $$-\epsilon_{ijk}\frac{i}{\hbar}\left[\hat J_i, \hat J_j\right] = \hat J_k$$
        We can now substitute this into the previous equation to obtain
        $$\left[A, \hat J_k\right] = A\left(-\epsilon_{ijk}\frac{i}{\hbar}\left[\hat J_i, \hat J_j\right] \right) - \left(-\epsilon_{ijk}\frac{i}{\hbar}\left[\hat J_i, \hat J_j\right]\right)A$$
        Writing this out explicitly we have
        $$\left[A, \hat J_k\right] = -\epsilon_{ijk}\frac{i}{\hbar}\left(A\hat J_i \hat J_j - A\hat J_j \hat J_i - \hat J_i \hat J_j A + \hat J_j \hat J_i A\right)$$
        Since $A$ commutes with $\hat J_i$ and $\hat J_j$ we can state the following:
        $$A \hat J_i \hat J_j = \hat J_i \hat J_j A \quad \text{and} \quad A \hat J_j \hat J_i = \hat J_j \hat J_i A$$
        We can now substitute this into the previous equation to obtain
        $$\boxed{\left[A, \hat J_k\right] = -\epsilon_{ijk}\frac{i}{\hbar}\left(\hat J_i \hat J_j A - \hat J_j \hat J_i A - \hat J_i \hat J_j A + \hat J_j \hat J_i A\right) = 0}$$
    \end{solution}
    \begin{problem}
        Determine the Clebsch-Gordan coefficients for $L = 1$ and $S = 1$.
    \end{problem}
    \begin{solution}
        
    We can first write out all of the individual states for $L = 1$ and $S = 1$
    $$\ket{2, 2}, \ket{2, 1}, \ket{2, 0}, \ket{2, -1}, \ket{2, -2},  \ket{1,1}, \ket{1,0}, \ket{1,-1}, \ket{0,0}$$
    We can start with a highest or lowest $m_J$ state and work our way down. We can start with the highest $m_J$ state $\ket{2, 2}$. We can write the following:
    $$\ket{2, 2} = \ket{1,1,1,1}$$ Hence our Clebsch-Gordan coefficient is 1.
    Now let us recall the eigenvalue equation for the lowering operator:
    $$J_- \ket{j, m} = \hbar \sqrt{j\left(j+1\right) - m\left(m-1\right)} \ket{j, m-1}$$
    Now we apply the lowering operator to $\ket{2, 2}$ to obtain $\ket{2, 1}$. We can write the following:
    $$J_- \ket{2, 2} = J_- \ket{1,1,1,1} = 2\hbar\ket{2, 1} = \hbar \sqrt{2} \ket{1,1,0,1} + \hbar \sqrt{2} \ket{1,1,1,0}$$
    $$\ket{2, 1} = \frac{1}{\sqrt{2}}\ket{1,1,0,1} + \frac{1}{\sqrt{2}}\ket{1,1,1,0}$$
    Hence our Clebsch-Gordan coefficients are $\frac{1}{\sqrt{2}}$ for both states.
    Now we can apply the lowering operator to $\ket{2, 1}$ to obtain $\ket{2, 0}$. We can write the following:
    $$J_- \ket{2, 1} = J_- \left(\frac{1}{\sqrt{2}}\ket{1,1,0,1} + \frac{1}{\sqrt{2}}\ket{1,1,1,0}\right) = $$$$\sqrt{6}\hbar\ket{2, 0} = \frac{1}{\sqrt{2}}\hbar\left(\sqrt{2}\ket{1,1,-1,1} + 2\sqrt{2}\ket{1,1,0,0} + \sqrt{2}\ket{1,1,1,-1}\right)$$
    $$\ket{2, 0} = \frac{1}{\sqrt{6}}\ket{1,1,-1,1} + \sqrt{\frac{2}{3}}\ket{1,1,0,0} + \frac{1}{\sqrt{6}}\ket{1,1,1,-1}$$
    Hence our Clebsch-Gordan coefficients are $\frac{1}{\sqrt{6}}, \sqrt{\frac{2}{3}}, \frac{1}{\sqrt{6}}$ for the states $\ket{1,1,-1,1}, \ket{1,1,0,0}, \ket{1,1,1,-1}$ respectively.
    While we might be tempted to lower this state, it will be much easier to raise from the $\ket{2,-2}$ state, since we know that coefficient is $1$. We can write the following for the raising operator:
    $$J_+ \ket{j, m} = \hbar \sqrt{j\left(j+1\right) - m\left(m+1\right)} \ket{j, m+1}$$
    $$J_+ \ket{2, -2} = J_+ \ket{1,1,-1,-1} = 2\hbar\ket{2, -1} = \hbar \sqrt{2} \ket{1,1,-1,0} + \hbar \sqrt{2} \ket{1,1,0,-1}$$
    $$\ket{2, -1} = \frac{1}{\sqrt{2}}\ket{1,1,-1,0} + \frac{1}{\sqrt{2}}\ket{1,1,0,-1}$$
    Hence our Clebsch-Gordan coefficients are $\frac{1}{\sqrt{2}}$ for both states.
    So in other words we find the following Clebsch-Gordan coefficients:
    $$\boxed{\ket{2, 2} = \ket{1,1,1,1} \implies C_{1,1,1,1}^{2,2} = 1}$$
    $$\boxed{\ket{2, 1} = \frac{1}{\sqrt{2}}\ket{1,1,0,1} + \frac{1}{\sqrt{2}}\ket{1,1,1,0} \implies C_{1,1,0,1}^{2,1} = \frac{1}{\sqrt{2}}, C_{1,1,1,0}^{2,1} = \frac{1}{\sqrt{2}}}$$
    $$\boxed{\ket{2, 0} = \frac{1}{\sqrt{6}}\ket{1,1,-1,1} + \sqrt{\frac{2}{3}}\ket{1,1,0,0} + \frac{1}{\sqrt{6}}\ket{1,1,1,-1}} $$$$\boxed{\implies C_{1,1,-1,1}^{2,0} = \frac{1}{\sqrt{6}}, C_{1,1,0,0}^{2,0} = \sqrt{\frac{2}{3}}, C_{1,1,1,-1}^{2,0} = \frac{1}{\sqrt{6}}}$$
    $$\boxed{\ket{2, -1} = \frac{1}{\sqrt{2}}\ket{1,1,-1,0} + \frac{1}{\sqrt{2}}\ket{1,1,0,-1} \implies C_{1,1,-1,0}^{2,-1} = \frac{1}{\sqrt{2}}, C_{1,1,0,-1}^{2,-1} = \frac{1}{\sqrt{2}}}$$
    $$\boxed{\ket{2, -2} = \ket{1,1,-1,-1} \implies C_{1,1,-1,-1}^{2,-2} = 1}$$
    Now we use orthogonality to jump to the $\ket{1,1}$ state. We can write the following:
    $$\ket{1,1} = a\ket{1,1,1,0} + b\ket{1,1,0,1}$$
    $$\bra{2,1}\ket{1,1} = \frac{a}{\sqrt{2}} + \frac{b}{\sqrt{2}} = 0 \implies a = -b$$
    For normalization we have the following:
    $$\left|a\right|^2 + \left|b\right|^2 = 1$$
    $$2\left|a\right|^2 = 1 \implies a = \frac{1}{\sqrt{2}}$$
    $$b = -\frac{1}{\sqrt{2}}$$
    Hence we have the following Clebsch-Gordan coefficients:
    $$\boxed{\ket{1,1} = \frac{1}{\sqrt{2}}\ket{1,1,1,0} - \frac{1}{\sqrt{2}}\ket{1,1,0,1} \implies C_{1,1,1,0}^{1,1} = \frac{1}{\sqrt{2}}, C_{1,1,0,1}^{1,1} = -\frac{1}{\sqrt{2}}}$$
    Now we can use the lowering operator on $\ket{1,1}$ to obtain $\ket{1,0}$. We can write the following:
    $$J_- \ket{1,1} = J_- \left(\frac{1}{\sqrt{2}}\ket{1,1,1,0} - \frac{1}{\sqrt{2}}\ket{1,1,0,1}\right) $$$$= \sqrt{2}\hbar\ket{1,0} = \frac{1}{\sqrt{2}}\hbar\left(\sqrt{2}\ket{1,1,1,-1} - \sqrt{2}\ket{1,1,-1,1}\right)$$
    $$\ket{1,0} = \frac{1}{\sqrt{2}}\ket{1,1,1,-1} -\frac{1}{\sqrt{2}} \ket{1,1,-1,1}$$
    Hence we have the following Clebsch-Gordan coefficients:
    $$\boxed{\ket{1,0} = \frac{1}{\sqrt{2}}\ket{1,1,1,-1} -\frac{1}{\sqrt{2}} \ket{1,1,-1,1} \implies C_{1,1,1,-1}^{1,0} = \frac{1}{\sqrt{2}}, C_{1,1,-1,1}^{1,0} = -\frac{1}{\sqrt{2}}, C^{1,0}_{1,1,0,0} = 0}$$
    We can also use orthogonality to jump to the $\ket{1,-1}$ state. We can write the following:
    $$\ket{1,-1} = c\ket{1,1,-1,0} + d\ket{1,1,0,-1}$$
    $$\bra{2,-1}\ket{1,-1} = \frac{c}{\sqrt{2}} + \frac{d}{\sqrt{2}} = 0 \implies c = -d$$
    For normalization we have the following:
    $$\left|c\right|^2 + \left|d\right|^2 = 1$$
    $$2\left|c\right|^2 = 1 \implies c = \frac{1}{\sqrt{2}}$$
    $$d = -\frac{1}{\sqrt{2}}$$
    Hence we have the following Clebsch-Gordan coefficients:
    $$\boxed{\ket{1,-1} = \frac{1}{\sqrt{2}}\ket{1,1,-1,0} - \frac{1}{\sqrt{2}}\ket{1,1,0,-1} \implies C_{1,1,-1,0}^{1,-1} = \frac{1}{\sqrt{2}}, C_{1,1,0,-1}^{1,-1} = -\frac{1}{\sqrt{2}}}$$
    Additionally, we can find the final coefficients for the $\ket{0,0}$ state. We can write the following:
    $$\ket{0,0} = e\ket{1,1,0,0} + f\ket{1,1,1,-1} + g\ket{1,1,-1,1}$$
    $$\bra{2,0}\ket{0,0} = e\sqrt{\frac{2}{3}} + \frac{f}{\sqrt{6}} + \frac{g}{\sqrt{6}} = 0$$
    $$\bra{1,0}\ket{0,0} = \frac{f}{\sqrt{2}} - \frac{g}{\sqrt{2}} = 0$$
    This implies $f = g$.
    This also implies that $e = -f$ 
    For normalization we have the following:
    $$\left|e\right|^2 + 2\left|f\right|^2 = 1$$
    $$3\left|f\right|^2 = 1 \implies f = \frac{1}{\sqrt{3}}$$
    $$e = -\frac{1}{\sqrt{3}}$$
    $$g = \frac{1}{\sqrt{3}}$$
    Hence we have the following Clebsch-Gordan coefficients:
    $$\boxed{\ket{0,0} = -\frac{1}{\sqrt{3}}\ket{1,1,0,0} + \frac{1}{\sqrt{3}}\ket{1,1,1,-1} + \frac{1}{\sqrt{3}}\ket{1,1,-1,1}} $$$$\boxed{\implies C_{1,1,0,0}^{0,0} = -\frac{1}{\sqrt{3}}, C_{1,1,1,-1}^{0,0} = \frac{1}{\sqrt{3}}, C_{1,1,-1,1}^{0,0} = \frac{1}{\sqrt{3}}}$$


\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
    We begin by writing out the angular momentum operator in cartesian coordinates:
    $$\hat L_i = \epsilon_{ijk}\hat x_j\hat p_k$$
    Now let us consider the Parity operator acting on the angular momentum operator acting on a test state:
    $$\hat P\hat L_i \psi(x_1,x_2,x_3) = \hat P\epsilon_{ijk}\hat x_j\hat p_k\psi(x_1,x_2,x_3) = -i\hbar \hat P \epsilon_{ijk}\hat x_j\frac{\partial}{\partial x_k}\psi(x_1,x_2,x_3)$$
    $$-i\hbar \epsilon_{ijk}(-x_j)\frac{\partial \psi(-x_1,-x_2,-x_3)}{\partial (-x_k)}$$
    Next we will prove an important equivalent between derivatives and functions of negative variables:
    $$\frac{\partial \psi(-x_1,-x_2,-x_3)}{\partial \left(-x_k\right)} = \frac{\partial \psi(u_1,u_2,u_3)}{\partial \left(u_k\right)} = \psi'\left(u_1,u_2,u_3\right) = \psi'\left(-x_1,-x_2,-x_3\right)$$
    This implies the following:
    $$\frac{\partial \psi(-x_1,-x_2,-x_3)}{\partial x_k} = -\frac{\partial \psi(-x_1,-x_2,-x_3)}{\partial \left(-x_k\right)} = -\psi'\left(-x_1,-x_2,-x_3\right)$$
    
    Next we do the reversed order operation:
    $$\hat L_i \hat P \psi(x_1,x_2,x_3) = \hat L_i \psi(-x_1,-x_2,-x_3) = \epsilon_{ijk}\hat x_j\hat p_k \psi(-x_1,-x_2,-x_3) $$$$= -i\hbar\epsilon_{ijk}\hat x_j\frac{\partial \psi(-x_1,-x_2,-x_3)}{\partial x_k}$$
    $$= -i\hbar\epsilon_{ijk} \left(-x_j\right)\left(-\psi'\left(-x_1,-x_2,-x_3\right)\right) = -i\hbar\epsilon_{ijk}x_j\psi'\left(-x_1,-x_2,-x_3\right)$$
    Therefore writing out the commutator we have the following:
    $$\left[\hat P, \hat L_i\right] = \hat P\hat L_i - \hat L_i \hat P = -i\hbar\epsilon_{ijk}x_j\psi'\left(-x_1,-x_2,-x_3\right) + i\hbar\epsilon_{ijk}x_j\psi'\left(-x_1,-x_2,-x_3\right) = 0$$
    The general spherical harmonics are given by the following equation:
    $$Y_{lm}(\theta,\phi) = \sqrt{\frac{2l+1}{4\pi}\frac{(l-m)!}{(l+m)!}}P_l^m(\cos\theta)e^{im\phi}$$
    If we apply the parity operator to the spherical harmonics we have the following:
    $$\hat P Y_{lm}(\theta,\phi) = \sqrt{\frac{2l+1}{4\pi}\frac{(l-m)!}{(l+m)!}}P_l^m(\cos\left(\pi - \theta\right))e^{im\left(\phi + \pi\right)} = e^{im\pi}\sqrt{\frac{2l+1}{4\pi}\frac{(l-m)!}{(l+m)!}}P_l^m(-\cos\left(\theta\right))e^{im\phi}$$
    We recognize the following:
    \begin{itemize}
        \item Any real power of an even function is even
        \item The $l$th derivative of an even function is even if $l$ is even and odd if $l$ is odd.
    \end{itemize}
    These imply that due to the form of Associated Legendre Polynomial, (an even function multiplied by an $l+m$th derivative of an even function), the Associated Legendre Polynomial is even if $l+m$ is even and odd if $l+m$ is odd.
    Due to the negative sign on the $\cos{\left(\theta\right)}$ in the Associated Legendre Polynomial we can write the following:
    
   $$\hat P Y_{lm}(\theta,\phi) = \begin{cases} Y_{lm}(\theta,\phi)e^{im\pi} & \text{if $l+m$ is even} \\ -Y_{lm}(\theta,\phi)e^{im\pi} & \text{if $l+m$ is odd} \end{cases}$$
    Now considering the phase factor we have the following:
    $$\hat P Y_{lm}(\theta,\phi) = \begin{cases} Y_{lm}(\theta,\phi) & \text{if $l$ is even} \\ -Y_{lm}(\theta,\phi) & \text{if $l$ is odd} \end{cases}$$

\end{solution}
\begin{problem}
    
\end{problem}
\begin{solution}\\
    First we will set up the Wigner D-Matrix equation:
    $$D^\ell_{m',m}(\alpha,\beta,\gamma) = e^{-im'\alpha}d^\ell_{m',m}(\beta)e^{-im\gamma}$$
    We can write out the following: $d$ matrix equation:
    $$d^\ell_{m',m}(\beta) = \bra{\ell,m'}e^{-i\beta\hat/ J_y/\hbar}\ket{\ell,m}$$
    Using computer software to diagonalize the $\ell=1$ $\hat{J}_y$ matrix we find the following:
    $$\hat{J}_y = \left[\begin{matrix}1 & -1 & -1\\0 & - \sqrt{2} i & \sqrt{2} i\\1 & 1 & 1\end{matrix}\right]\left[\begin{matrix}0 & 0 & 0\\0 & - i \beta & 0\\0 & 0 & i \beta\end{matrix}\right]\left[\begin{matrix}\frac{1}{2} & 0 & \frac{1}{2}\\- \frac{1}{4} & \frac{\sqrt{2} i}{4} & \frac{1}{4}\\- \frac{1}{4} & - \frac{\sqrt{2} i}{4} & \frac{1}{4}\end{matrix}\right]
    $$
    Therefore the exponential of the $\hat{J}_y$ matrix is given by the following:
    $$\exp\left(-i\beta\hat{J}_y/\hbar\right) = \left[\begin{matrix}\frac{\cos{\left(\beta \right)}}{2} + \frac{1}{2} & - \frac{\sqrt{2} \sin{\left(\beta \right)}}{2} & \frac{1}{2} - \frac{\cos{\left(\beta \right)}}{2}\\\frac{\sqrt{2} \sin{\left(\beta \right)}}{2} & \cos{\left(\beta \right)} & - \frac{\sqrt{2} \sin{\left(\beta \right)}}{2}\\\frac{1}{2} - \frac{\cos{\left(\beta \right)}}{2} & \frac{\sqrt{2} \sin{\left(\beta \right)}}{2} & \frac{\cos{\left(\beta \right)}}{2} + \frac{1}{2}\end{matrix}\right]$$
    We can now write out the Wigner D-Matrix:
    $$D^1_{m',m}=\left[\begin{matrix}\left(\frac{\cos{\left(\beta \right)}}{2} + \frac{1}{2}\right) e^{- i \alpha m^\prime} e^{- i \gamma m} & - \frac{\sqrt{2} e^{- i \alpha m^\prime} e^{- i \gamma m} \sin{\left(\beta \right)}}{2} & \left(\frac{1}{2} - \frac{\cos{\left(\beta \right)}}{2}\right) e^{- i \alpha m^\prime} e^{- i \gamma m}\\\frac{\sqrt{2} e^{- i \alpha m^\prime} e^{- i \gamma m} \sin{\left(\beta \right)}}{2} & e^{- i \alpha m^\prime} e^{- i \gamma m} \cos{\left(\beta \right)} & - \frac{\sqrt{2} e^{- i \alpha m^\prime} e^{- i \gamma m} \sin{\left(\beta \right)}}{2}\\\left(\frac{1}{2} - \frac{\cos{\left(\beta \right)}}{2}\right) e^{- i \alpha m^\prime} e^{- i \gamma m} & \frac{\sqrt{2} e^{- i \alpha m^\prime} e^{- i \gamma m} \sin{\left(\beta \right)}}{2} & \left(\frac{\cos{\left(\beta \right)}}{2} + \frac{1}{2}\right) e^{- i \alpha m^\prime} e^{- i \gamma m}\end{matrix}\right]$$
    Writing out the transormation equation:
    $$\begin{bmatrix}
        Y^\prime_{1,1}\\ Y^\prime_{1,0} \\Y^\prime_{1,-1}
    \end{bmatrix} = \left(D^1_{m^\prime, m}\right)^T \begin{bmatrix}
        Y_{1,1}\\ Y_{1,0} \\Y_{1,-1}
    \end{bmatrix} $$
    This gives us the following:
    $$Y^\prime_{1,1} = \frac{\left(\left(- Y_{1,-1} \cos{\left(\beta \right)} + Y_{1,-1}\right)e^{i\gamma} + \sqrt{2} Y_{1,0} \sin{\left(\beta \right)} + \left(Y_{1,1} \cos{\left(\beta \right)} + Y_{1,1}\right)e^{-i\gamma}\right) e^{- i\alpha}}{2}$$
    $$Y^\prime_{1,0} = \frac{\left(\sqrt{2} Y_{1,-1} \sin{\left(\beta \right)}e^{i\gamma} + 2 Y_{1,0} \cos{\left(\beta \right)} - \sqrt{2} Y_{1,1} \sin{\left(\beta \right)}e^{-i\gamma}\right)}{2}$$
    $$Y^\prime_{1,-1} = \frac{\left(\left(Y_{1,-1} \cos{\left(\beta \right)} + Y_{1,-1}\right)e^{i\gamma} - \sqrt{2} Y_{1,0} \sin{\left(\beta \right)} + \left(- Y_{1,1} \cos{\left(\beta \right)} + Y_{1,1}\right)e^{-i\gamma}\right) e^{ i\alpha}}{2}$$
    We see that in the case of $\alpha = \gamma = 0$ and $\beta = 0$ we have the following:
    $$Y^\prime_{1,1} = Y_{1,1}$$
    $$Y^\prime_{1,0} = Y_{1,0}$$
    $$Y^\prime_{1,-1} = Y_{1,-1}$$
    This is the expected result and we conclude our proof.
\end{solution}
\begin{problem}
\end{problem}
\begin{solution}\\
    Ran out of time so I wrote it on paper which is attached to the end of the document.
\end{solution}
\begin{problem}
    Show that for the Hamiltonian: $$\hat H = \frac{\bar {\hat{p}}^2}{2m} + V\left(r\right) + \lambda \hat {\bar{L}} \cdot \hat {\bar{S}}$$ the components of $\bar{\hat J}= \bar{\hat L} + \bar{\hat S}$ are constants of motion.

\end{problem}
\begin{solution}\\
A quantity is a constant of motion if it commutes with the Hamiltonian. We can write the commutation relation for the Hamiltonian and the components of $\bar{\hat J}$ as the following:
$$\left[\hat H, \hat J_i\right] = \left[\hat H, \hat L_i + \hat S_i\right] = \left[\hat H, \hat L_i\right] + \left[\hat H, \hat S_i\right]$$
We can write this out explicitly as the following:
$$\left[\hat H, \hat L_i\right] = \left[\frac{\bar {\hat{p}}^2}{2m} + V\left(r\right) + \lambda \hat {\bar{L}} \cdot \hat {\bar{S}}, \hat L_i\right] = \left[\frac{\bar {\hat{p}}^2}{2m} + V\left(r\right), \hat L_i\right] + \left[\lambda \hat {\bar{L}} \cdot \hat {\bar{S}}, \hat L_i\right]$$
$$\left[\hat H, \hat S_i\right] = \left[\frac{\bar {\hat{p}}^2}{2m} + V\left(r\right) + \lambda \hat {\bar{L}} \cdot \hat {\bar{S}}, \hat S_i\right] = \left[\frac{\bar {\hat{p}}^2}{2m} + V\left(r\right), \hat S_i\right] + \left[\lambda \hat {\bar{L}} \cdot \hat {\bar{S}}, \hat S_i\right]$$
$V\left(r\right)$ is a scalar and hence commutes with $\hat L_i$ and $\hat S_i$. We can write the following:
Now we can show that $\left[\bar{\hat{p}}^2, L_i\right] =0$ and $\left[\bar{\hat{p}}^2, S_i\right] =0$.
It is trivial for the spin operator since the spin operator is independent of the position operator. For the angular momentum operator we can write the following:
$$\left[\bar{\hat{p}}^2, \hat L_i\right] = \left[\bar{\hat{p}}^2, \epsilon_{ijk}\hat x_j\hat p_k\right] = \epsilon_{ijk}\left(\bar{\hat{p}}^2 \hat x_j \hat p_k - \hat x_j \hat p_k \bar{\hat{p}}^2\right)$$
We know the following:
$$\left[\hat p_i, \hat p_j\right] =0\,\, \forall i,j \in \set{1,2,3}$$ 
Since if an operator commutes with another operator it also commutes with functions of the operator we can say the following:
$$\left[\hat p_i\hat p_i, \hat p_j\right] = 0\,\, \forall i,j \in \set{1,2,3}$$
Therefore we can write the following:
$$\left[\bar{\hat{p}}^2, \hat p_j\right] = 0$$
We can utilize a relation that was prove in a previous problem set:
$$\left[\hat x_i, \hat p_j^2\right] = 2\hat p_j\left[\hat x_i,\hat p_j\right] = 2i\hbar \hat p_j \text{ when } i=j$$
Substituting this into our expression we have:
$$\left[\bar{\hat{p}}^2, \hat x_j\right] = -2i\hbar \hat p_j$$
We then can write that $$\left[\bar{\hat{p}}^2, \hat L_i\right] = \epsilon_{ijk}\left(\bar{\hat{p}}^2 \hat x_j \hat p_k - \hat x_j \hat p_k \bar{\hat{p}}^2\right) = \epsilon_{ijk}\left(\bar{\hat p}^2 \hat x_j - \hat x_k \bar{\hat{p}}^2\right)\hat p_k = -2i\epsilon_{ijk}\hbar \hat p_j\hat p_k $$$$= -i\hbar\left(\epsilon_{ijk}\hat p_j \hat p_k + \epsilon_{ijk}\hat p_j \hat p_k\right) = -i\hbar \left(\epsilon_{ijk}\hat p_j \hat p_k + \epsilon_{ikj}\hat p_k \hat p_j\right)$$
$$= -i\hbar\epsilon_{ijk}\left[\hat p_j,\hat p_k\right] = 0$$
Therefore we have the following:
$$\left[\bar{\hat{p}}^2, \hat L_i\right] = 0$$
Now for the final part of the problem we can write the following:
$$\lambda \left[\hat L_j\hat S_j, \hat L_i + S_i\right] = \lambda \left[\hat L_j\hat S_j, \hat L_i\right] + \lambda \left[\hat L_j\hat S_j, \hat S_i\right]$$
$$= \lambda \left[\hat L_j, \hat L_i\right]\hat S_j + \lambda \left[\hat S_j, \hat L_i\right]\hat L_j + \lambda \left[\hat L_j, \hat S_i\right]\hat S_j + \lambda \left[\hat S_j, \hat S_i\right]\hat L_j$$
We know that the angular momentum operators commute with the spin operators hence:
$$\lambda \left[\hat L_j\hat S_j, \hat L_i + S_i\right] = \lambda\left(\left[\hat L_j, \hat L_i\right]\hat S_j + \left[\hat S_j, \hat S_i\right]\hat L_j\right)$$
$$= -i\hbar\lambda\epsilon_{ijk}\left(\hat L_k \hat S_j + \hat S_k \hat L_j\right) = -i\hbar\lambda\epsilon_{ijk}\left(\hat L_k \hat S_j + \hat L_j \hat S_k\right)$$
We can rename then rearrange the indices of the Levi-Civita symbol to obtain:
$$\lambda \left[\hat L_j\hat S_j, \hat L_i + S_i\right] = -i\hbar \lambda \epsilon_{ijk}\left(\hat L_k \hat S_j - \hat L_k \hat S_j\right) = 0$$
So in total we have:
$$\left[\bar{\hat p}^2, \hat L_i + \hat S_i\right] = 0$$
$$\left[V\left(r\right), \hat L_i + \hat S_i\right] = 0$$
$$\lambda \left[\hat L_j\hat S_j, \hat L_i + S_i\right] = 0$$
Since our Hamiltonian is composed of a sum of those operators, we can write the following:
$$\boxed{\left[\hat H, \hat L_i + \hat S_i\right] = 0}$$
This implies that $\hat L_i + \hat S_i$ is a constant of motion.

\end{solution}
\begin{problem}
    Consider a system of three non-identical particles, each of spin $s=\frac{1}{2}$, whose Hamiltonian is given by:
    $$H =\frac{\epsilon_0}{\hbar^2}\left(\bar{\hat S}_2 + \bar{\hat S}_3\right)\cdot \bar{\hat S}_1 + \frac{\epsilon_1}{\hbar^2}\left(\hat S_{1y} + \hat S_{2y} + \hat S_{3y}\right)$$
    Where $\epsilon_0$ and $\epsilon_1$ are constants. Find the energy of the system:
\end{problem}
\begin{solution}\\
    Let us rewrite the Hamiltonian in the following way:
    $$H = \frac{\epsilon_0}{\hbar^2}\left(\hat S^2_i \hat S^1_i + \hat S^3_i \hat S^1_i\right) + \frac{\epsilon_1}{\hbar^2}\left(\hat S^1_y + \hat S^2_y + \hat S^3_y\right)$$
    Let us consider the following:
    $$\bar{\hat{S}}^2 = \left(\bar{\hat S}_1 + \left(\bar{\hat S}_2 + \bar{\hat S}_3\right)\right)^2 = \bar{\hat{S}}^2_1 + \bar{\hat{S}}^2_2 + \bar{\hat{S}}^2_3 + 2\bar{\hat S}_1 \cdot\left(\bar{\hat S}_2 + \bar{\hat S}_3\right) + 2\bar{\hat S}_2 \cdot \bar{\hat S}_3$$
    $$2\bar{\hat{S}}_1 \cdot \left(\bar{\hat S}_2 + \bar{\hat S}_3\right) = \bar{\hat{S}}^2 - \bar{\hat{S}}^2_1 - \bar{\hat{S}}^2_2 - \bar{\hat{S}}^2_3 - 2\bar{\hat S}_2 \cdot \bar{\hat S}_3$$
    $$=\bar{\hat{S}}^2 - \bar{\hat{S}}_1^2 - \bar{\hat{S}}_{23}^2 \text{ where } \bar{\hat{S}}_{23} = \bar{\hat S}_2 + \bar{\hat S}_3$$
    Now if we utilize a basis aligned with the $y$ axis we have the following:
    $$\hat S_y \ket{\psi} = m_s \ket{\psi}$$
    Hence our Hamiltonian acting on our state should give us the energy of system. We can write the following:
    $$H\ket{\psi} = \frac{\epsilon_0}{\hbar^2}\left(\bar{\hat{S}}^2 - \bar{\hat{S}}_1^2 - \bar{\hat{S}}_{23}^2\right)\ket{\psi} + \frac{\epsilon_1}{\hbar^2}\left(\hat S^1_y + \hat S^2_y + \hat S^3_y\right)\ket{\psi}$$
    $$=E\ket{\psi} = \frac{\epsilon_0}{\hbar^2}\left(s\left(s+1\right) - s_1\left(s_1+1\right) - s_{23}\left(s_{23}+1\right)\right)\ket{\psi} + \frac{\epsilon_1}{\hbar^2}\left(m_{s_1} + m_{s_2} + m_{s_3}\right)\ket{\psi}$$
    Therefore the energy of the system is given by the following:
    $$E = \frac{\epsilon_0}{\hbar^2}\left(s\left(s+1\right) - s_1\left(s_1+1\right) - s_{23}\left(s_{23}+1\right)\right) + \frac{\epsilon_1}{\hbar^2}\left(m_{s_1} + m_{s_2} + m_{s_3}\right)$$
    $$=\boxed{\frac{\epsilon_0}{\hbar^2}\left(s\left(s+1\right) - s_1\left(s_1+1\right) - s_{23}\left(s_{23}+1\right)\right) + \frac{\epsilon_1 m_{s}}{\hbar^2}}$$
    



\end{solution}
\begin{problem}
Show that $$e^{\frac{i\pi \hat{J}_y}{2\hbar}}e^{\frac{i\pi\hat{J}_x}{\hbar}}e^{-\frac{i\pi \hat{J}_y}{2\hbar}} = e^{\frac{i\pi \hat{J}_z}{\hbar}}$$

\end{problem}
\begin{solution}\\
    First let us state the following: Let $\alpha = \frac{\pi}{2\hbar}$. Then we have the following:
    $$e^{i\alpha \hat{J}_y}e^{2i\alpha \hat{J}_x}e^{-i\alpha \hat{J}_y}$$
    Now let us consider the following:
    $$e^{2i\alpha \hat{J}_x} = 1 + 2i\alpha \hat{J}_x + \frac{1}{2}\left(2i\alpha \hat{J}_x\right)^2 + \dots$$
    Let us prove that the following is true:\\
    If $$e^{i\alpha \hat{J}_y}\hat{J}_x e^{-i\alpha \hat{J}_y} = \hat{J}_x'$$
    then
    $$e^{i\alpha \hat{J}_y}\hat{J}_x^n e^{-i\alpha \hat{J}_y} = \hat{J}_x'^n$$
    We can prove this by induction. We have already shown the base case. Now let us assume that the following is true:
    $$e^{i\alpha \hat{J}_y}\hat{J}_x^n e^{-i\alpha \hat{J}_y} = \hat{J}_x'^n$$
    Then we have the following:
    $$e^{i\alpha \hat{J}_y}\hat{J}_x^{n+1} e^{-i\alpha \hat{J}_y} = e^{i\alpha \hat{J}_y}\hat{J}_x^n \hat{J}_x e^{-i\alpha \hat{J}_y} = e^{i\alpha \hat{J}_y}\hat{J}_x^n e^{-i\alpha \hat{J}_y} e^{i\alpha \hat{J}_y} \hat{J}_x e^{-i\alpha \hat{J}_y} = \hat{J}_x'^n e^{i\alpha \hat{J}_y} \hat{J}_x e^{-i\alpha \hat{J}_y}$$
    $$ = \hat{J}_x'^n \hat{J}_x' = \hat{J}_x'^{n+1}$$
    Now we can build up the full exponential transformation from the base case of $\hat{J}_x'$ for each term in the expansion of $e^{2i\alpha \hat{J}_x}$.
    Now let us find the following:
    $$e^{i\alpha \hat{J}_y}\hat{J}_x e^{-i\alpha \hat{J}_y} = \hat{J}_x' = \hat{J}_x + \left[i\alpha \hat{J}_y, \hat{J}_x\right] + \frac{1}{2}\left[i\alpha \hat{J}_y, \left[i\alpha \hat{J}_y, \hat{J}_x\right]\right] + \dots$$
    $$= \hat{J}_x + \alpha \hbar \hat{J}_z + \frac{1}{2}\alpha^2 \hbar^2 \hat{J}_x + \dots$$ Continuing the process leads us to recognize the following:
    $$\hat{J}_x' = \hat{J}_x \cos\left(\hbar \alpha\right) + \hat{J}_z \sin\left(\hbar \alpha\right) = \hat{J}_x \cos\left(\frac{\pi}{2}\right) + \hat{J}_z \sin\left(\frac{\pi}{2}\right) = \hat{J}_z$$
    Now building up the full exponential transformation from the base case of $\hat{J}_x'$ for each term in the expansion of $e^{2i\alpha \hat{J}_x}$. We have the following:
    $$e^{i\alpha \hat{J}_y}e^{2i\alpha \hat{J}_x}e^{-i\alpha \hat{J}_y} = e^{i\alpha \hat{J}_y}\left(1 + 2i\alpha \hat{J}_x + \frac{1}{2}\left(2i\alpha \hat{J}_x\right)^2 + \dots\right)e^{-i\alpha \hat{J}_y}$$
    $$= \sum_{n=0}^{\infty} \frac{1}{n!}\left(2i\alpha\right)^n e^{i\alpha \hat{J}_y}\hat{J}_x^n e^{-i\alpha \hat{J}_y} = \sum_{n=0}^{\infty} \frac{1}{n!}\left(2i\alpha\right)^n \hat{J}_z^n = e^{2i\alpha \hat{J}_z} = e^{\frac{i\pi \hat{J}_z}{\hbar}}$$
\end{solution}
\begin{problem}
    Consider the wave function of the particle $$\psi\left(\bar r\right) = \left(\sqrt{2}x + \sqrt{2}y + z\right)f\left(r\right)$$
    where $f(r)$ is a spherically symmetric function. \\ 
    a. Is $\psi\left(\bar r\right)$ an eigenfunction of $\hat L^2$ if so what is the eigenvalue? \\
    b.\\ What are the probability of finding the particle in the $m_\ell = 1$ state?
\end{problem}
\begin{solution}\\
    a.\\ 
    We begin by writing out the angular momentum operator in cartesian coordinates:
    $$L_i = i\hbar\epsilon_{ijk}x_j\frac{\partial}{\partial x_k}$$
    $$L_iL_i = L^2 = -\hbar^2 \left(\epsilon_{ijk}x_j\frac{\partial }{\partial x_k} \epsilon_{imn}x_m \frac{\partial }{\partial x_n}\right)$$
    $$=-\hbar^2\left(\epsilon_{ijk}\epsilon_{imn}x_j \left(\delta_{mk}\frac{\partial }{\partial x_n} + x_m \frac{\partial^2}{\partial x_k \partial x_n}\right)\right) = -\hbar^2\epsilon_{ijk}x_j\left(\epsilon_{ikn}\frac{\partial}{\partial x_n} + \epsilon_{imn} x_m\frac{\partial^2}{\partial x_k \partial x_n}\right)$$
    $$=\hbar^2 x_j\left(2\delta_{jn}\frac{\partial }{\partial x_n} + \left(\delta_{km}\delta_{jn} - \delta_{kn}\delta_{jm}\right)x_m\frac{\partial^2}{\partial x_k \partial x_n}\right)$$
    $$=\hbar^2\left(2x_j\frac{\partial }{\partial x_j} + x_jx_k\frac{\partial^2}{\partial x_k \partial x_j} - x_j^2 \frac{\partial^2}{\partial x_k^2}\right)$$
    Rewriting in familiar notation:
    $$L^2 = \hbar^2 \left(\left(\bar r \cdot \bar \nabla\right)^2 + \bar r \cdot \bar \nabla -r^2\bar \nabla^2 \right)$$
    We know that we can represent the angular momentum operator as the following:
    $$\bar{\hat{L}} = \bar{\hat{r}}\times \bar{\hat{p}}$$ Since the position vector is just $r \hat r$ all radial components of the angular momentum operator will vanish. Hence we can ignore our $f(r)$ term. We can then write the following:
    Now operating on the wavefunction gives us:
    $$\bar{\hat{L}}^2\psi = \hbar^{2} \cdot \left(2 \sqrt{2} x + 2 \sqrt{2} y + 2 z\right) = 2\hbar^2 \psi$$
    Hence the eigenvalue is $2\hbar^2$.\\
    b.\\ 
    We start finding the $\ell$ value of the wavefunction:
    $$2\hbar^2 = \hbar^2\ell\left(\ell+1\right)$$
    $$\ell = 1$$
    Now we can write our wavefunction into spherical coordinates and compute the probability of finding the particle in the $m_\ell = 1$ state, which is given by the inner product of the wavefunction with the spherical harmonics:
    $$\psi\left(\bar r\right) = \sqrt{2} r \sin{\left(\phi \right)} \sin{\left(\theta \right)} + \sqrt{2} r \sin{\left(\theta \right)} \cos{\left(\phi \right)} + r \cos{\left(\theta \right)}$$
    $$Y^1_1 = - \frac{\sqrt{3} e^{i \phi} \sin{\left(\theta \right)}}{ \sqrt{8\pi}}$$
    $$P(1,1) = \left|\int \psi\left(\bar r\right) Y^1_1 d\tau\right|^2 $$$$= \left|\int \left(\left( \sqrt{2} r \sin{\left(\phi \right)} \sin{\left(\theta \right)} + \sqrt{2} r \sin{\left(\theta \right)} \cos{\left(\phi \right)} + r \cos{\left(\theta \right)}\right) \cdot - \frac{\sqrt{3} e^{i \phi} \sin{\left(\theta \right)}}{ \sqrt{8\pi}}\right) d\tau\right|^2$$
    $$ = \frac{8\pi}{3}$$
    We now need to consider the normalzation constant $N$ for the wavefunction which is found by:
    $$\frac{1}{N^2} = \int \psi\left(\bar r\right) \psi\left(\bar r\right) d\tau$$
    This gives us the following:
    $$N = \frac{1}{10}\sqrt{\frac{15}{\pi}}$$
    Hence the probability of finding the particle in the $m_\ell = 1$ state is:
    $$P(1,1) = \frac{8\pi}{3}\left(\frac{3}{20\pi}\right) = \frac{2}{5}$$


\end{solution}
\begin{problem}
Determine $\langle L_z \rangle$ when the system is constructed from $L = 1$ and $S = 1/2$ and the quantum
state is defined by $J = 1/2$ and $m_j = 1/2$.
\end{problem}
\begin{solution}\\
   Let us first separate the $J,m_j$ state into $L,m_l,S,m_s$ states.
   $$\ket{\frac{1}{2},\frac{1}{2}} = a\ket{1,\frac{1}{2}, 1, -\frac{1}{2}} + b\ket{1,\frac{1}{2}, 0, \frac{1}{2}}$$
   We know $a$ and $b$ from Clebsch-Gordan coefficients calculations:
   $$\ket{\frac{1}{2},\frac{1}{2}} = \sqrt{\frac{2}{3}}\ket{1,\frac{1}{2}, 1, -\frac{1}{2}} + \frac{1}{\sqrt{3}}\ket{1,\frac{1}{2}, 0, \frac{1}{2}}$$
   Now we can write the expectation value of the $L_z$ operator:
   $$\langle L_z \rangle = \bra{\frac{1}{2},\frac{1}{2}}L_z\ket{\frac{1}{2},\frac{1}{2}} = \bra{\frac{1}{2},\frac{1}{2}}L_z\left(\sqrt{\frac{2}{3}}\ket{1,\frac{1}{2}, 1, -\frac{1}{2}} + \frac{1}{\sqrt{3}}\ket{1,\frac{1}{2}, 0, \frac{1}{2}}\right)$$
   $$= \hbar\bra{\frac{1}{2},\frac{1}{2}}\sqrt{\frac{2}{3}}\ket{1,\frac{1}{2},1,-\frac{1}{2}} = \boxed{\frac{2\hbar}{3}}$$
\end{solution}
\begin{problem}
    Determine the eigenvalues and eigenvectors of $\sigma \cdot e$. Here $\sigma$ are the Pauli matrices and $e$ is
    a unit vector in terms of polar angles $\theta$ and $\varphi$.
\end{problem}
\begin{solution}\\
    We can do this by first defining our unit vector in general spherical coordinates:
    $$\hat e = f(\theta,\varphi)\hat x + g(\theta,\varphi)\hat y + h(\theta,\varphi)\hat z \text{ where } f^2 + g^2 + h^2 = 1$$
    We can then write the following:
    $$\sigma \cdot e = \sigma_x f + \sigma_y g + \sigma_z h$$
    Writing out then summing Pauli matrices:
    $$\sigma \cdot e = \begin{pmatrix}
        h & f-ig \\
        f+ig & -h 
    \end{pmatrix}$$
    We can then finding the eigenvalues:
    $$\det\left(\sigma \cdot e - \lambda I\right) = 0$$
    $$\det\left(\begin{pmatrix}
        h-\lambda & f-ig \\
        f+ig & -h-\lambda
    \end{pmatrix}\right) = 0$$
    $$-\left(h-\lambda\right)\left(h+\lambda\right) - \left(f-ig\right)\left(f+ig\right) = 0$$
    $$\lambda^2 - h^2 - \left(f^2 + g^2\right) = 0 \implies \lambda^2 = 1$$
    $$\lambda = \pm 1$$
    We can then find the eigenvectors:
    $$\lambda = 1 \implies \begin{pmatrix}
        h-1 & f-ig \\
        f+ig & -h-1
    \end{pmatrix}\begin{pmatrix}
            x \\
            y
        \end{pmatrix} = \begin{pmatrix}
                0 \\
                0
        \end{pmatrix}$$
        This means the following:
        $$\left(h-1\right)x + \left(f-ig\right)y = 0$$
        We set $y = 1$ and solve for $x$:
        $$x = \frac{f-ig}{1-h}$$
        Now to find the normalization constant $N$:
        $$\frac{1}{N^2} = \left|\frac{f-ig}{1-h}\right|^2 + 1 = \frac{f^2 + g^2}{\left(1-h\right)^2} + 1 = \frac{f^2 + g^2 + 1 + h^2 -2h}{\left(1-h\right)^2}$$
        $$ = \frac{2-2h}{\left(1-h\right)^2} = \frac{2}{\left(1-h\right)}$$
        $$N = \sqrt{\frac{1-h}{2}}$$
        Hence the eigenvector is:
        $$\ket{1} = \sqrt{\frac{1-h}{2}}\begin{pmatrix}
            \frac{f-ig}{1-h} \\
            1
        \end{pmatrix}$$
        We can do the same for $\lambda = -1$:
        $$\lambda = -1 \implies \begin{pmatrix}
            h+1 & f-ig \\
            f+ig & -h+1
            \end{pmatrix}\begin{pmatrix}
                x \\
                y
            \end{pmatrix} = \begin{pmatrix}
                0 \\
                0
            \end{pmatrix}$$
            This means the following:
            $$\left(h+1\right)x + \left(f-ig\right)y = 0$$
            We set $y = 1$ and solve for $x$:
            $$x = \frac{ig-f}{1+h}$$
            Now to find the normalization constant $N$:
            $$\frac{1}{N^2} = \left|\frac{ig-f}{1+h}\right|^2 + 1 = \frac{f^2 + g^2}{\left(1+h\right)^2} + 1 = \frac{f^2 + g^2 + 1 + h^2 +2h}{\left(1+h\right)^2} = \frac{2+2h}{\left(1+h\right)^2} = \frac{2}{\left(1+h\right)}$$
            $$N = \sqrt{\frac{1+h}{2}}$$
            Hence the eigenvector is:
            $$\ket{-1} = \sqrt{\frac{1+h}{2}}\begin{pmatrix}
                \frac{ig-f}{1+h} \\
                1
            \end{pmatrix}$$
            If we want to use an arbitrary position vector in spherical coordinates for ease of calculation we can write the following:
            $$h = \cos\theta$$
            $$f = \sin\theta\cos\varphi$$
            $$g = \sin\theta\sin\varphi$$
                

    \end{solution}
    \begin{problem}
    \end{problem}
    \begin{solution}\\
        Ran out of time so I wrote it on paper which is attached to the end of the document.
    \end{solution}
    \begin{problem}
        \end{problem}
        \begin{solution}\\
            %Using the arbitrary spin state of a $\frac{1}{2}$ particle:
            %$$\ket{\psi} = \begin{pmatrix}
            %    e^{i\alpha} \cos\left(\delta\right)\\
             %   e^{i\beta} \sin\left(\delta\right)
            %\end{pmatrix}$$
            %We can then write the expectation value of the spin operator along an arbitrary axis:
            %Let our $\sigma_{z'}$ operator be the following:
            %$$\sigma_{z'} = \sin\left(\theta\right)\cos\left(\phi\right)\sigma_x + \sin\left(\theta\right)\sin\left(\phi\right)\sigma_y + \cos\left(\theta\right)\sigma_z$$
            %$$= \begin{pmatrix}
             %   \cos\left(\theta\right) & \sin\left(\theta\right)e^{i\phi} \\
             %   \sin\left(\theta\right)e^{i\phi} & -\cos\left(\theta\right)
            %\end{pmatrix}$$
            %$$\langle \sigma_{z'} \rangle = \bra{\psi}\sigma_n\ket{\psi} = \begin{pmatrix}
            %    e^{-i\alpha} \cos\left(\delta\right) & e^{-i\beta} \sin\left(\delta\right)
            %    \end{pmatrix}\begin{pmatrix}
             %       \cos\left(\theta\right) & \sin\left(\theta\right)e^{-i\phi} \\
              %      \sin\left(\theta\right)e^{i\phi} & -\cos\left(\theta\right)
               % \end{pmatrix}\begin{pmatrix}
                %    e^{i\alpha} \cos\left(\delta\right) \\
                 %   e^{i\beta} \sin\left(\delta\right)
                  %  \end{pmatrix}$$
                   % $$= \begin{pmatrix}
                    %    e^{-i\alpha} \cos\left(\delta\right) & e^{-i\beta} \sin\left(\delta\right)
                     %   \end{pmatrix}
                      %  \begin{pmatrix}
                       %     \cos\left(\theta\right)\cos\left(\delta\right)e^{i\alpha} + \sin\left(\theta\right)e^{-i\phi}\sin\left(\delta\right)e^{i\beta} \\
                        %    \sin\left(\theta\right)e^{i\phi}\cos\left(\delta\right)e^{i\alpha} - \cos\left(\theta\right)\sin\left(\delta\right)e^{i\beta}
%
 %                       \end{pmatrix}$$
  %                      $$\cos^2{\delta}\cos{\theta} + e^{i\left(\beta - \alpha - \phi\right)}\cos{\delta}\sin{\delta}\sin{\theta} + e^{-i\left(\beta -\alpha - \phi\right)}\sin{\delta}\cos{\delta}\sin{\theta} - \sin^2{\delta}\cos{\theta}$$
   %                     $$=\cos{2\delta}\cos{\theta} + 2\sin{\theta}\cos{\delta}\sin{\delta}\cos{\left(\beta - \alpha - \phi\right)}$$
    %                    $$=\cos{2\delta}\cos{\theta} + \sin{2\delta}\sin{\theta}\cos{\left(\beta - \alpha - \phi\right)}$$
     %                   Since the epectation value of $\sigma_{z'} = \frac{2}{\hbar}\langle S_{z'} \rangle$ we can write:
      %                  $$\langle S_{z'} \rangle = \frac{\hbar}{2}\left(\cos{2\delta}\cos{\theta} + \sin{2\delta}\sin{\theta}\cos{\left(\beta - \alpha - \phi\right)}\right)$$
       %                 We can see that for any $\delta, \beta$, and $\alpha$ we can choose $\theta$ and $\phi$ such that $\langle S_{z'} \rangle = \frac{\hbar}{2}$.
        %                Let us choose $\delta = \frac{\pi}{4}$ and $\beta = \alpha = 0$. Then we have:
         %               $$\langle S_{z'} \rangle = \frac{\hbar}{2}\left(\sin{\theta}\cos{\phi}\right)$$
          %              Since both of these are bounded by $\pm 1$ we can choose $\theta = \frac{\pi}{2}$ and $\phi = 0$ to get $\langle S_{z'} \rangle = \frac{\hbar}{2}$. Hence we have shown that for any arbitrary spin state of a $\frac{1}{2}$ particle we can choose an arbitrary axis such that the expectation value of the spin operator along that axis is $\frac{\hbar}{2}$.
           %             For the case of $\alpha =\frac{\pi}{6}, \beta = \pi, \delta = \frac{\pi}{6}$ we have: the following solution space (where $\hbar$ is set to 1):
            %            
             %           \begin{center}
              %          $\includegraphics[scale=0.5]{LarryLarry2.png}$
               %         \end{center}
                %        Now 
                 %       Using the conclusions from problem 13 we can find the condition where $$\left|\braket{\uparrow}{\psi} \right|^2 = 1$$:
                  %      $$\left|\bra{\uparrow}\ket{\psi}\right|^2 = 1= \left|\sqrt{\frac{1-\cos{\theta}}{2}}\left(\frac{\sin{\theta}\cos{\varphi} - i\sin{\theta}\sin{\varphi}}{1-\cos{\theta}}e^{i\alpha} \cos\left(\delta\right) + e^{i\beta} \sin\left(\delta\right)\right)\right|^2$$
                   %     This occurs when:
                    %    $$\frac{\sin{\theta}\cos{\varphi} - i\sin{\theta}\sin{\varphi}}{1-\cos{\theta}}e^{i\alpha}\cos{\delta} + e^{i\beta}\sin{\delta}= \frac{2}{1-\cos{\theta}}$$
                     %   $$\sin{\theta}\cos{\varphi} - i\sin{\theta}\sin{\varphi} + e^{i\left(\beta - \alpha\right)}\cos{\delta} + e^{i\beta}\sin{\delta}\left(1-\cos{\theta}\right) = 2$$
                        Ran out of time so I wrote it on paper which is attached to the end of the document.
                        \end{solution}
                        \begin{problem}
                        \end{problem}
                        \begin{solution}\\
                            Ran out of time so I wrote it on paper which is attached to the end of the document.
                        \end{solution}
                        \includepdf[pages=-]{How do I remove clock.pdf}
                        \end{document}