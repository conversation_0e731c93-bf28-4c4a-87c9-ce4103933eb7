\documentclass[aps,prb,english,notitlepage,floatfix,amsmath,amssymb,nofootinbib,
nobibnotes]{revtex4-2}

%\usepackage[dvips]{color,graphicx}
\usepackage[pdftex]{graphicx}
\usepackage[usenames,dvipsnames]{color}
\usepackage{epic,epsf}
\usepackage{dcolumn}
\usepackage{ulem}
\usepackage{physics}
\usepackage{tikz}
\usepackage{graphicx}% Include figure files
\usepackage{dcolumn}% Align table columns on decimal point
\usepackage{bm}% bold math
\usepackage{xcolor}
\usepackage{comment}
\usepackage{amsmath}
\usepackage{cancel}
\usepackage{stackrel}
%\usepackage{hyperref}% add hypertext capabilities
%\usepackage[mathlines]{lineno}% Enable numbering of text and display math
%\linenumbers\relax % Commence numbering lines
\usepackage{braket} % for using the braket notation
%\usepackage[showframe,%Uncomment any one of the following lines to test 
%%scale=0.7, marginratio={1:1, 2:3}, ignoreall,% default settings
%%text={7in,10in},centering,
%%margin=1.5in,
%%total={6.5in,8.75in}, top=1.2in, left=0.9in, includefoot,
%%height=10in,a5paper,hmargin={3cm,0.8in},
%]{geometry}
\usepackage{mathrsfs}

%\usepackage{caption}
%\usepackage{subcaption}


%% Own definitions:
\newcommand{\BEq}{\begin{eqnarray}}
\newcommand{\EEq}{\end{eqnarray}}
\newcommand{\BEqn}{\begin{eqnarray*}}
\newcommand{\EEqn}{\end{eqnarray*}}
%% When one wants Eqs(1a,1b,1c) etc. then use \BEqM ... \EEqM:
\newcommand{\BM}{\begin{subequations}}
\newcommand{\EM}{\end{subequations}}
\newcommand{\BEqM}{\begin{subequations}\begin{eqnarray}}
\newcommand{\EEqM}{\end{eqnarray}\end{subequations}}
\newcommand{\Bit}{\begin{itemize}}
\newcommand{\Eit}{\end{itemize}}
\newcommand{\Ben}{\begin{enumerate}}
\newcommand{\Een}{\end{enumerate}}
%% Greek letters:
\renewcommand{\a}{\alpha}
\renewcommand{\b}{\beta}
\newcommand{\D}{\Delta}
%% Useful dbar for Thermodynamics:
\def\dbar{{\mathchar'26\mkern-12mu d}}
%%
%% Some simplified expressions:
\newcommand{\mb}[1]{\mbox{\boldmath $#1$}}
\newcommand{\hb}[1]{\mbox{\boldmath$\hat{#1}$}}
\newcommand{\bg}{{\bf g}}
\newcommand{\br}{{\bf r}}
\newcommand{\qa}{\mbox{$q_\alpha$}}
\newcommand{\bdel}{\mbox{\boldmath $\nabla$}}
\newcommand{\non}{\nonumber\\}
\parindent0cm

%% Colors:
\newcommand{\bt}[1]{\textcolor{blue}{#1}}
\newcommand{\rt}[1]{\textcolor{red}{#1}}
\newcommand{\gt}[1]{\textcolor{green}{#1}}
\newcommand{\mt}[1]{\textcolor{magenta}{#1}}

\markright{Andreas Bill \hfill HW 2, 23F\hfill }

\begin{document}

\title{Mathematical Methods in Physics (PHYS 560A) \\ Assignment 2: \bt{Solutions}}
\author{Andreas Bill\\09/05/2023, due 09/14/2023 on Canvas}
%\affiliation{}
\maketitle


This is a group assignment so each group will turn in ONE solution (named GroupX-A2.pdf where X is the group number; the name of all group members should appear below the title). Working as a group on this should be fun and useful. Just remember what is written in the syllabus and what I told you in class; the purpose of the group work is to work together, \textit{not} to distribute the work among each other and then collect all solutions. The groups are:
\begin{description}
\item[\textnormal{Group 1}] Anthony Acebal, Alexander Goytia Fajardo, Oscar Padilla
\item[\textnormal{Group 2}] Emiliano Benitez, Ethan Hsu, Ryan Rios
\item[\textnormal{Group 3}] Sophealena Chhom, Andy Le Blanc, Timothy Van Hoomissen
\item[\textnormal{Group 4}] Eric Corona, David Morales-Zapien, Tianfei Zhang
\end{description}
\BEqn
\hline
\EEqn

\Ben %%%%%

\vspace*{0ex}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\item %%%
Consider the complex function $f$ of complex variable $z$
\BEq
f(z) = \sqrt{z^2 - 2},
\EEq

\color{blue}
We note first that our earlier analysis of $\sqrt{z}$ showed that $z=0$ and $z\to\infty$ are the two only branch points of the function. This leads to hypothesize that $z=\pm \sqrt{2}$ and perhaps $z\to\infty$ will be the branch point of $f(z)$. Let's look into that: 
\color{black}

\Ben%%%

\item%%%
Using the two sets of polar coordinates $(r_a,\theta_a)$ and $(r_b,\theta_b)$ with respect to points $a$ and $b$, express $f(z)$ in these polar coordinates.

\color{blue}
Introducing the polar coordinates around point $a=\sqrt{2}$, $z = a + r_a e^{i\theta_a}$, and around point $b=-\sqrt{2}$, $z = b + r_b e^{i\theta_b}$ we have for any point $z$ in the Argand plane (see Fig.~\ref{HW2prob1Fig1}a; see also comment at the end of the problem.)
\begin{align}\label{fzrtheta}
f(z) = \sqrt{r_a e^{i\theta_a}r_b e^{i\theta_b}} = \sqrt{ r_a r_b }\, e^{i\theta_a/2} \, e^{i\theta_b/2}.
\end{align}
\begin{figure}[h]
%\includegraphics[width=43ex,height=32ex]{FIGS/MMP_HW2_23F_FigSqrtab.jpeg} \hspace*{15ex}
%\includegraphics[width=43ex,height=32ex]{FIGS/MMP_HW2_23F_FigSqrtabReal.jpeg}
\caption{\label{HW2prob1Fig1}The arbitrary point $z$ in terms of polar coordinates centered on $a$ and $b$ in the Argand $z-$plane. Left: arbitrary points $a$, $b$ and arbitrary contour around point $a$. Right: $a$ and $b$ are on the real axis and circular contour around $a$.}
\end{figure}
\color{black}
\newpage
Show that:
\Ben %%%
\item%%%
Closed contours around $a$ \textit{or} $b$ are double-valued.

\color{blue}

To simplify matters let us first choose a \textit{circular} contour around $a$ (see Fig.~\ref{HW2prob1Fig1}b) with a radius $r_a = R$ such that point $b$ is not included in the contour: $R < |a-b|$. Let us also choose the angle $\theta_a = 0$ at the start of the contour. Both are only set for convenience.\\
At the start of the contour around $z=a$ we have $f(z) = f(r,\theta) = f(R,\varepsilon)$ with $0<\varepsilon\ll 1$ and
\BEq
f(z=r_a+i\varepsilon) \approx f(r_a,\varepsilon/r_a) = \sqrt{ R r_b }\, e^{i\varepsilon/2r_a} \, e^{i\theta^0_b/2} \stackrel{\varepsilon \to 0}{\longrightarrow}  \sqrt{ R r_b } \, e^{i\theta_b/2} = \sqrt{R} \sqrt{z-b}.
\EEq
{\small (the approximation comes from the fact that for $r_a < \infty$ and $0 < \varepsilon \ll 1$ we have $\theta = \arctan(\varepsilon/r_a)\approx \varepsilon/r_a$.)

Going around the contour once so that $\theta_a$ goes from zero to $2\pi$ we note first that $\theta_b$ will do a close loop but not span the full interval $[0,2\pi)$ because by construction the closed contour does not contain the point $b$. Hence, $\theta_{b,\text{final}}(\varepsilon)= \theta_{b,\text{initial}}(\varepsilon) = \theta_b(\varepsilon)$. On the other hand, $\theta_a$ goes from $\varepsilon$ to $2\pi - \varepsilon$:
\BEq
f\left(z = R e^{i(\varepsilon+2\pi)}\right)
= \sqrt{ R r_b }\, e^{i(\varepsilon+2\pi)/2} \, e^{i\theta_b(\varepsilon)/2}
\stackrel{\varepsilon \to 0}{\longrightarrow} 
\sqrt{ R r_b }\, e^{i\pi} \, e^{i\theta_b/2} = - \sqrt{R r_b} e^{i\theta_b/2} = - f(z=r_a).
\EEq
Because of the minus sign, $f(R,0) \neq f(R,2\pi)$ and thus $z = a$ is a branch point of $f(z)$.\\
The same reasoning can be applied to the point $z = b$ where we compare $f(z) = f(r_b,0)$ and $f(r_b,2\pi)$. As a result, both $z=a$ and $z=b$ are branch points of $f(z)$.\\
\color{black}

\item %%%
A closed contour around \textit{both} points $a,b$ is single valued.

\color{blue}
We now consider a path that encircles \textit{both} $z=a$ and $z=b$.  Unlike the previous point we now have $R>|a-b|$. As we go around the contour both angles $\theta_a$ and $\theta_b$ will go from an initial angle $\theta_{a,i}$, $\theta_{b,i}$ (in general $\theta_{a,i}\neq\theta_{b,i}$ unless we take the starting point of the contour on the line that contains both points $a$ and $b$) to the final angles $\theta_{a,f} = \theta_{a,i} + 2\pi$ and $\theta_{a,f} = \theta_{a,i} + 2\pi$. Therefore, as we go around the contour once we see that
\BEq
f\left(z = R e^{i(\theta+2\pi)}\right)
&=& \sqrt{ r_a r_b }\, e^{i(\theta_a + 2\pi)/2} \, e^{i(\theta_b+2\pi)/2}
= \sqrt{ r_a r_b }\, e^{i\theta_a/2}\, e^{i\theta_b/2} \, e^{i(2\pi+2\pi)/2}\non
&=& \sqrt{ r_a r_b }\, e^{i\theta_a/2}\, e^{i\theta_b/2}
= f(z).
\EEq
Note here that $R,\theta$ are the polar coordinates with respect to the center of the contour path defined as a circle enclosing $a$ and $b$.\\
\color{black}

\item %%%
The point at infinity $|z|\to \infty$ is not a branch point.

\color{blue}

Unlike the case of $\sqrt{z}$, the point $z\to\infty$ is not a branch point of $f(z)$. This is seen by taking $t = 1/z$:
\BEq
f(1/t) = \sqrt{(1/t-a)(1/t-b)} = \frac{1}{t} \, \sqrt{(1-at)(1-bt)}.
\EEq
This function diverges at $t\to 0$. But it remains single valued in this limit. Writing $t=(R,\theta)$ we have
\BEq
f(1/t) = \frac{e^{-i\theta}}{R} \, \sqrt{(1-aRe^{i\theta})(1-bRe^{i\theta})}.
\EEq
For $\theta = 0$ we have $f(1/t_0) = \frac{1}{R} \sqrt{(1-aR)(1-bR)}$. For $\theta \to 2\pi$ we have
\BEq
f(1/t_{2\pi}) = \frac{e^{-i2\pi}}{R} \sqrt{(1-aRe^{i2\pi})(1-bRe^{i2\pi})} = f(1/t_0).
\EEq
To make sense of this result note that in the previous point we took the closed contour around $a$ and $b$ and went counterclockwise. We can go around the contour clockwise. By definition, the interior of the contour is always on the left of the contour as one moves on it. So when going counterclockwise the interior of the contour is where $a$ and $b$ is. When going clockwise along the \textit{same} contour, $a$ and $b$ are outside the contour and $z\to \infty$ is \textit{inside} the contour. This implies that $z\to\infty$ cannot be a branch point of $f(z)$.
}
\color{black}

\Een %%%
\item
Determine two possible branch cuts of the function (a Riemann sheet where the function is single-valued) that will allow only closed paths around both points $a$ and $b$.

\color{blue}
There is always an infinity of possible branch cuts between two branch points since the line connecting the two can be arbitrary. However, there are two obvious choices. Consider the straight line containing $a$ and $b$. For the purpose of the discussion we can assume here that $a < b \in \mathbb{R}$ as in Fig.~\ref{HW2prob1Fig1}b (but we insist that is not necessary, only convenient for the discussion). One possibility is to connect $a$ and $b$ directly. The branch cut would be the interval $(a,b)$ on the real axis. Another possibility is to connect these point via $|z|\to \infty$ since the latter point is not a branch point. In this case, the branch cut is $(-\infty,a) \cup (b,\infty)$  (it is a loop that goes from $a$ to minus infinity and then comes back from plus infinity - which is the same point in the Argand plane - to b) It is also possible to take the branch cuts $(\infty, a) \cup (\infty, b)$, or $(a,\infty) \cup (b,\infty)$ since the parts where the branches overlap, the sign change across the cut will compensate each other.

\vspace*{3ex}

\textbf{Note about Eq.~\eqref{fzrtheta}:}\\
It seems like we have chosen two origins (at $a$ and $b$) for the polar coordinates and have now four independent components (variables) whereas $z$ has only two components. This is not the case but is not directly visible in this condensed notation. What we mean by Eq.~\eqref{fzrtheta} is that if you take first a loop around point $a$, then the natural polar coordinates are those that are centered on $a$, so $r_a,\theta_a$, see Fig.~\eqref{HW2prob1Fig1}. We can still write the same point $z$ in terms of $r_b$ and $\theta_b$ but these are not independent variables anymore; they depend on your actual polar coordinates $r_a$ and $\theta_a$. So for a contour around $a$ the independent variables are $r_a,\theta_a$ and the dependent variables are $r_b = r_b(r_a,\theta_a)$ and $\theta_b = \theta_b(r_a,\theta_a)$.\\
Similarly, if we consider a contour around point $b$. To describe a contour around both points you can choose the polar coordinates around the origin of axes $r,\theta$ (not drawn on the figure) and then have both $r_a,\theta_a$ and $r_b,\theta_b$ as function of $r,\theta$.

\Een
\color{black}

\vspace*{1ex}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%\textbf{Problem 2:} Analyticity and derivatives of a complex function.\\[.2cm]
\item%%%
Consider the complex function $f(z)=u(z) + iv(z)$ with $z=x+iy \in\mathbb{C}$.
Assuming that $u$ and $v$ are conjugate harmonic functions and given that $u(x,y) = \sin x\,\sinh y$ determine the conjugate of $u(x,y)$. Write the function in terms of $z$.%\\[.3cm] % M&H

\color{blue}
Since $u$ and $v$ are conjugate harmonic functions it means that they satisfies the Laplace equation ($u_{xx} + u_{yy} = 0$) and the Cauchy-Riemann (C-R) relations. In particular, from the first Cauchy-Riemann relation we have
\BEq
\frac{\partial u}{\partial x} = u_x = \cos x \,\sinh y \stackrel{\text{(C-R)}}{=} \frac{\partial v}{\partial y} = v_y \quad \Rightarrow \quad v(x,y) = \int dy\, \cos x\,\sinh y + c_1 = \cos x \,\cosh y + c_1,
\EEq
where $c_1$ is a real constant. For any value of the constant $c_1$ we have $v_x = -\sin x\,\cosh y = - u_y$ and thus the second Cauchy-Riemann relation is also satisfied. We can set $c_1 = 0$ and define the simplest analytic complex function $f(z)$ for the $u(x,y)$ given above as $f(z) = u+iv = \sin x\,\sinh y + i\,\cos x \,\cosh y$.

We have to express $f$ as a function of $z$. This may take some staring and playing around with algebra... In fact, the above expression may remind you of two trigonometric relations you have seen before, namely $\cos(\a + \b) = \cos \a \, \cos \b - \sin \a \, \sin \b$ and $\cosh (\a + \b) = \cosh\a\,\cosh\b - \sinh\a\,\sinh\b$, but notice that neither quite matches the one we have derived. The main difference is the presence of the imaginary number $i$. Let us then try to expand $\cos(z) = \cos (x + iy)$ (so $\a = x$ and $\b = iy$)
\BEq
\cos (x+iy) =  \cos x \,\cos(iy) - \sin x\,\sin (iy)  &=&  \cos x \,\cosh y - i \sin x\,\sinh y \non
&=& -i (  \sin x\,\sinh y + i  \cos x \,\cosh y ) 
\EEq 
where we used  $\cos(iy) = [e^{i(iy)} + e^{-i(iy)}]/2 = [e^{-y} + e^{y}]/2 = \cosh y$ and similarly $\sin(iy) = i \sinh y$.
As a result we see that $f(z) = i\cos(z)$. This demonstration relies on the permanence of algebraic functions.

{\small
Note: Another, more pedestrian, way to find the expression in terms of $z$ is to plug into the expression above the expressions of $\sin, \cos, \sinh, \cosh$ in terms of exponentials:
\BEq
\sin x\sinh y + i\cos x\cosh y &=&\frac{(e^{ix}-e^{-ix})}{2i}\frac{(e^y-e^{-y})}{2} + i\frac{(e^{ix}+e^{-ix})}{2}\frac{(e^y+e^{-y})}{2} \non
&=&\frac{1}{4i}\left(e^{ix}e^y-e^{ix}e^{-y}-e^{-ix}e^{y}+e^{-ix}e^{-y}\right) \non
&&+\frac{i}{4}\left(e^{ix}e^y+e^{ix}e^{-y}+e^{-ix}e^{y}+e^{-ix}e^{-y}\right)\non
&\stackrel{\frac{1}{i}=-i}{=}&\frac{i}{4}\left(-e^{ix}e^y+e^{ix}e^{-y}+e^{-ix}e^{y}-e^{-ix}e^{-y} +e^{ix}e^y+e^{ix}e^{-y}+e^{-ix}e^{y}+e^{-ix}e^{-y}\right) \non
&=&\frac{i}{4}\left(2e^{ix}e^{-y} + 2e^{-ix}e^y\right) = \frac{i}{2}\left(e^{i(x+iy)}+e^{-i(x+iy)}\right) \non
&=&i\cos(z)\;,
\EEq
}
\color{black}

\vspace*{1ex}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\item %%%
Consider now the complex functions
\BEq
(a)\; f(z) = \frac{z}{z^2+1}, \quad (b)\,f(z) = \frac{z + |z|}{2},\quad (c)\, f(z) = \Im z. %\quad (d)\, f(z) = \cos z.
\EEq

\Ben %%%

\item %%%
Determine the domain in $\mathbb{C}$ where each function is analytic. Which function is entire?
\item %%%
Calculate the first derivative $df/dz$ of each function.\\
%\textit{Make sure to justify every step since we are now working in the complex plane with complex functions.}
\Een %%%

\color{blue}
We first determine the domain of analyticity of each function. We remind first that a sum, difference, product and composition of analytic functions is an analytic function. We will make use of that result.
\Ben
\item
We first write the function as $f(z) = z (z^2+1)^{-1} = f_1(z)f_2(z)$. Since the function $f_1(z) = z$ is analytic in $\mathbb{C}$ except at $|z|\to \infty$ remains to find where $f_2(z)$ is analytic. We note that
\BEq
f_2(z) = \frac{1}{z^2+1} = \frac{1}{(z+i)(z-i)}
\EEq
Therefore, the function $f(z) = f_1(z) f_2(z)$ is analytic everywhere in $\mathbb{C}$, except at the points $z = \pm i$ where it has two singularities (poles). In conclusion $f(z)$ has three branch points (singularities, poles).
\item
We write $2f(z) = z + |z| = f_1(z) + f_2(z)$. We already saw that $f_1 = z$ is analytic everywhere except at $|z|\to \infty$. As for $f_2 = |z|$ there are two ways to show that it is not analytic. One is to write $f_2 = \sqrt{zz^\star}$. Since it explicitly depends on $z^\star$ it is non-analytic (because $df/dz^\star = z/|z| \neq 0$ for all $z\neq 0$ and $df/dz^\star$ is not defined at $z=0$).\\
The other way to show that $f_2$ is non-analytic is to write the function explicitly in $x,y$ and show that the Cauchy-Riemann relations are not satisfied. We write $f_2 (z) = |z| = \sqrt{x^2 + y^2}$ which is a real function. We see that $u(x,y) = \sqrt{x^2 + y^2}$ and $v(x,y) = 0$. Since $\partial u/\partial x = x/\sqrt{x^2 + y^2}$ we see that the function is not defined at $(x,y) = (0,0)$ and that for any $x\neq 0$ $\partial u/\partial x \neq 0 = \partial v/\partial y$. Similarly for any $y\neq 0$ we have $\partial u/\partial y \neq 0 = - \partial v/\partial x$.
\item
We write the function $f(z) = \mathrm{Im} z = (z-z^\star)/2i$. Since the function depends explicitly on $z^\star$ it is non-analytic anywhere. One can also show it by writing $f(z) = \mathrm{Im}(x+iy) = y$, and thus $u(x,y) = y$, $v(x,y) = 0$. We have $\partial u/\partial y = 1 \neq 0 = \partial v/\partial x$ for any $(x,y)$.
\Een

Now let's calculate the derivative of each function.
\Ben
\item
In this first example we could divide again $f = u+iv$ and use the expression derived in class $df/dz =\partial u/\partial x + i\partial v/\partial x = u_x + i v_x$.  Another way is prove first that if $f(z) = f_1/f_2$ then we have the usual relation (using the notation $f^\prime = df/dz$)
\BEq
\frac{df}{dz} = \frac{f_1^\prime f_2 - f_1f_2^\prime}{f_2^2}.
\EEq
This should in principle be proven:
{\tiny
\BEq
\frac{df}{dz} &=& \lim_{\Delta z \to 0} \frac{f(z+\Delta z) - f(z)}{\Delta z}
= \lim_{\Delta z \to 0} \frac{1}{\Delta z} \left[ \frac{f_1(z+\Delta z)}{f_2(z+\Delta z)} - \frac{f_1(z)}{f_2(z)} \right] \non\non
&=& \lim_{\Delta z \to 0} \frac{1}{\Delta z} \left[ \frac{f_1(z+\Delta z)f_2(z) - f_1(z)f_2(z+\Delta z) }{f_2(z+\Delta z)f_2(z)} \right]%\non\\
= \lim_{\Delta z \to 0} \frac{1}{\Delta z} \left\{ \frac{\left[f_1(z+\Delta z) - f_1(z) + f_1(z)\right]f_2(z)- f_1(z)\left[f_2(z+\Delta z) - f_2(z) + f_2(z)\right]}{f_2(z+\Delta z)f_2(z)} \right\} \non\non
&=& \lim_{\Delta z \to 0} \frac{1}{\Delta z} \left\{ \frac{\left[f_1(z+\Delta z) - f_1(z)\right] f_2(z)- f_1(z)\left[f_2(z+\Delta z) - f_2(z)\right]}{f_2(z+\Delta z)f_2(z)} \right\}\non\non
&=& \lim_{\Delta z \to 0}\frac{\frac{f_1(z+\Delta z) - f_1(z)}{\Delta z} f_2(z)- f_1(z)\frac{f_2(z+\Delta z) - f_2(z)}{\Delta z}}{f_2(z+\Delta z)f_2(z)}. QED
\EEq
}
Thus, we have
\BEq
\frac{df}{dz} = \frac{d}{dz}\left(\frac{z}{z^2+1} \right)= \frac{z^2+1 - 2z^2}{(z^2+1)^2} = \frac{1-z^2}{(z^2+1)^2}
\EEq
\item
Since it is non-analytic at any point in $\mathbb{C}$ there is no point in trying to calculate the derivative.
\item
Same argument as in (b).
\color{black}
%\item
%For example, one can choose to use the corollary of the Cauchy-Riemann theorem:
%\BEq
%\frac{df}{dz}  &=& \frac{d}{dz}\left( \cos z \right)= -\sin z\\
%&&\mathrm{or}\nonumber\\
%\frac{df}{dz} &=&\frac{\partial u}{\partial x} + i \frac{\partial v}{\partial x} \stackrel{Eq.~\eqref{CRcosz}}{=} - \sin x \cosh y + i \cos x \sinh y = -\sin z.
%\EEq
\Een

\color{black}

\vspace*{1ex}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% 
%% 
\item%%%
The so-called Wirtinger derivative operators in complex analysis are \textit{defined} as
\BEq
\label{we12}
\frac{\partial }{\partial z} &=& \frac{1}{2}\left(\frac{\partial}{\partial x}  - i\frac{\partial }{\partial y}\right), \quad\quad %\\
%\label{we2}
\frac{\partial }{\partial \bar{z}} = \frac{1}{2}\left(\frac{\partial}{\partial x}  + i\frac{\partial }{\partial y}\right)\;,
\EEq
where $\bar{z} = z^\star$ is also often used to denote the complex conjugate.
\Ben
\item Show that 
\BEq
\frac{\partial \ln z}{\partial z} = \frac{1}{z}
\EEq
and 
\BEq
\frac{\partial \ln z}{\partial \bar{z}} = 0
\EEq
using the Wirtinger derivative operators. Remember that $z$ and $\ln z$ can be written in cartesian or polar coordinates.
%$z=x+iy$ and $\ln z = \ln r + i \theta$ where $z=re^{i\theta}$ and $r=\sqrt{x^2+y^2}$ and $\theta = \arctan(y/x)$.

\color{blue}
Let's write $\ln z = \ln(re^{i\theta + 2\pi n}) = \ln r + i (\theta + 2\pi n)$ for $n\in\mathbb{Z}$ and recall that $r=\sqrt{x^2+y^2}$ and $\theta = \arctan(y/x)$. Let us calculate the derivative using Eq.~(\ref{we12}a):
\BEq
2\frac{\partial \ln z}{\partial z} &=& \frac{\partial(\ln r + i (\theta + 2\pi n))}{\partial x} -i \frac{\partial(\ln r + i (\theta + 2\pi n))}{\partial y}\non
&=&\frac{\partial \ln r}{\partial x} + i\frac{\partial \theta}{\partial x} - i\frac{\partial \ln r}{\partial y} + \frac{\partial \theta}{\partial y} \non
&=&\frac{x}{x^2+y^2} -i \frac{y}{x^2+y^2} - i\frac{y}{x^2+y^2} + \frac{x}{x^2+y^2}\non
&=&\frac{2 (x-iy)}{x^2+y^2} = \frac{2\bar{z}}{z\bar{z}} = \frac{2}{z}.
\EEq

Let's calculate the other derivative, which is easy now because using Eq.~(\ref{we12}b) it is just different combinations of the derivatives we just did
\BEq
2\frac{\partial \ln z}{\partial \bar{z}} &=& \frac{\partial(\ln r + i (\theta + 2\pi n))}{\partial x} +i \frac{\partial(\ln r + i (\theta + 2\pi n))}{\partial y}\non
&=&\frac{\partial \ln r}{\partial x} + i\frac{\partial \theta}{\partial x} + i\frac{\partial \ln r}{\partial y} - \frac{\partial \theta}{\partial y} \non
&=&\frac{x}{x^2+y^2} -i \frac{y}{x^2+y^2} + i\frac{y}{x^2+y^2} - \frac{x}{x^2+y^2}
= 0\;.
\EEq

\color{black}
\item %%%
Show that 
\BEq
\frac{\partial^2}{\partial x^2} + \frac{\partial^2}{\partial y^2} = 4\frac{\partial^2}{\partial z\partial \bar{z}}\;.
\EEq
\textit{Hint: Combining Eqs.~\eqref{we12} one can find} 
\BEq
\frac{\partial }{\partial x} = \frac{\partial }{\partial z} + \frac{\partial }{\partial \bar{z}}
\EEq
\textit{and a similar one for $\partial/\partial y$.}\\

\color{blue}
Using the hint we can look at 
\BEq
\frac{\partial^2}{\partial x^2} &=& \left(\frac{\partial }{\partial z} + \frac{\partial }{\partial \bar{z}}\right)\left(\frac{\partial }{\partial z} + \frac{\partial }{\partial \bar{z}}\right)
=\frac{\partial^2}{\partial z^2} + 2\frac{\partial^2}{\partial z\partial\bar{z}} + \frac{\partial^2}{\partial \bar{z}^2}\;.
\EEq
The expression for $\partial/\partial y$ is 
\BEq
\frac{\partial }{\partial y} = i\left(\frac{\partial }{\partial z} - \frac{\partial }{\partial \bar{z}}\right)
\EEq
so 
\BEq
\frac{\partial^2}{\partial y^2} &=& i\left(\frac{\partial }{\partial z} - \frac{\partial }{\partial \bar{z}}\right)i\left(\frac{\partial }{\partial z} - \frac{\partial }{\partial \bar{z}}\right)
=-\left(\frac{\partial^2}{\partial z^2} - 2\frac{\partial^2}{\partial z\partial\bar{z}} + \frac{\partial^2}{\partial \bar{z}^2}\right)
= -\frac{\partial^2}{\partial z^2} + 2\frac{\partial^2}{\partial z\partial\bar{z}} - \frac{\partial^2}{\partial \bar{z}^2}\;.
\EEq
Finally let's combine the two operators
\BEq\label{Laplacian}
\frac{\partial^2}{\partial x^2}+\frac{\partial^2}{\partial y^2} = 4\frac{\partial^2}{\partial z\partial \bar{z}}\;.
\EEq

\color{black}

\item %%%
This part is long-winded but gives some physics context. Consider an electron confined to a  two-dimensional plane, its position is given as $(x,y)$, and let the electron be subjected to a constant magnetic field of strength $B$ in the $z$-direction (perpendicular to the plane). The Hamiltonian is (in SI units) %(in $cgs$ units)
\BEq
% CGS: H = \frac{1}{2m}\left(\mathbf{p} + \frac{e}{c}\mathbf{A}\right)^2
H = \frac{1}{2m}\left(\mathbf{p} - q\mathbf{A}\right)^2
\EEq
where $m$ is the electron mass, $\mathbf{p}=-i\hbar (\partial/\partial x, \partial /\partial y)$ is the momentum, $q$ is the charge ($q=-e<0$ for electrons),
%$c$ is the speed of light in vacuum,
and $\mathbf{A}$ is the vector potential. One can choose a ``gauge" for the vector potential and the so-called symmetric gauge is $\mathbf{A} = \frac{B}{2}(-y,x,0)$ --we will come to this later but from Maxwell's equations $\mathbf{B} = \nabla \times \mathbf{A} = (0,0,B)$. Using this gauge, the Hamiltonian is
\BEq
%CGS:
%H &=& \frac{1}{2m}\left[\left(-i\hbar \frac{\partial}{\partial x} - \frac{e}{c}\frac{B}{2}y\right)^2 + \left(-i\hbar \frac{\partial}{\partial y} + \frac{e}{c}\frac{B}{2}x\right)^2 \right]\\
%&=& \frac{1}{2}\left[\left(-i\frac{\hbar}{\sqrt{m}} \frac{\partial}{\partial x} - \frac{e}{c}\frac{B}{2\sqrt{m}}y\right)^2 + \left(-i\frac{\hbar}{\sqrt{m}}\frac{\partial}{\partial y} + \frac{e}{c}\frac{B}{2\sqrt{m}}x\right)^2 \right]\;.
%SI:
H &=& \frac{1}{2m}\left[\left(-i\hbar \frac{\partial}{\partial x} + q\frac{B}{2}y\right)^2 + \left(-i\hbar \frac{\partial}{\partial y} - q\frac{B}{2}x\right)^2 \right]\non
&=& \frac{1}{2}\left[\left(-i\frac{\hbar}{\sqrt{m}} \frac{\partial}{\partial x} + q\frac{B}{2\sqrt{m}}y\right)^2 + \left(-i\frac{\hbar}{\sqrt{m}}\frac{\partial}{\partial y} - q\frac{B}{2\sqrt{m}}x\right)^2 \right]\;.
\EEq
It is useful to make the problem dimensionless by defining $\ell_B = \sqrt{\hbar/eB}$ which is called the magnetic length--you can double check the units.  Then,
% CGS: $\ell = \sqrt{\hbar c/eB}$
\BEq
%% CGS:
%H &=& \frac{1}{2}\left[\left(-i\sqrt{\frac{\hbar e B}{mc}} \ell \frac{\partial}{\partial x} - \frac{1}{2}\sqrt{\frac{\hbar e B}{mc}}\frac{y}{\ell}\right)^2 + \left(-i\sqrt{\frac{\hbar e B}{mc}} \ell \frac{\partial}{\partial y} + \frac{1}{2}\sqrt{\frac{\hbar e B}{mc}}\frac{x}{\ell}\right)^2\right ] \non
%&=&\frac{\hbar e B}{2mc}\left[\left(-i\ell\frac{\partial}{\partial x} - \frac{y}{2\ell}\right)^2 + \left(-i\ell\frac{\partial}{\partial y} + \frac{x}{2\ell}\right)^2\right]%\\
%=\frac{\hbar\omega}{2}\left[\left(-i\ell\frac{\partial}{\partial x} - \frac{y}{2\ell}\right)^2 + \left(-i\ell\frac{\partial}{\partial y} + \frac{x}{2\ell}\right)^2\right]
H &=& \frac{1}{2}\left[\left(-i\sqrt{\frac{\hbar e B}{m}} \ell_B \frac{\partial}{\partial x} - \frac{1}{2}\sqrt{\frac{\hbar e B}{m}}\frac{y}{\ell_B}\right)^2 + \left(-i\sqrt{\frac{\hbar e B}{m}} \ell_B \frac{\partial}{\partial y} + \frac{1}{2}\sqrt{\frac{\hbar e B}{m}}\frac{x}{\ell_B}\right)^2\right ] \non
%&=& \frac{\hbar e B}{2m}\left[\left(-i\ell_B\frac{\partial}{\partial x} - \frac{y}{2\ell_B}\right)^2 + \left(-i\ell_B\frac{\partial}{\partial y} + \frac{x}{2\ell_B}\right)^2\right] \non
&=& \frac{\hbar\omega_c}{2}\left[\left(-i\ell_B\frac{\partial}{\partial x} - \frac{y}{2\ell_B}\right)^2 + \left(-i\ell_B\frac{\partial}{\partial y} + \frac{x}{2\ell_B}\right)^2\right]
\EEq
where $\omega_c = eB/m$ is the cyclotron frequency. Now let us measure all lengths in units of magnetic length $\ell_B$.\footnote{In the literature you will often see the equivalent statement that we "set $\ell_B = 1$"; also, the subscript "B" is often omitted in the field but has the advantage that one identifies the length as depending on the magnetic field}  Then,
%Now let's set $\ell=1$ and measure all lengths in units of magnetic length.  Then,
\BEq \label{ham}
H = \frac{\hbar\omega_c}{2}\left[\left(-i\frac{\partial}{\partial x} - \frac{y}{2}\right)^2 + \left(-i\frac{\partial}{\partial y} + \frac{x}{2}\right)^2\right]\;.
\EEq
Use the Wirtinger derivatives to show that 
\BEq
H = \frac{\hbar\omega_c}{2}\left[ -4 \frac{\partial^2}{\partial z\partial\bar{z}} + \frac{1}{4}z\bar{z} - z\frac{\partial }{\partial z} + \bar{z} \frac{\partial}{\partial \bar{z}}\right]\;.
\EEq
\color{magenta}
\BEq
%% CGS:
%H &=& \frac{1}{2}\left[\left(-i\sqrt{\frac{\hbar e B}{mc}} \ell \frac{\partial}{\partial x} - \frac{1}{2}\sqrt{\frac{\hbar e B}{mc}}\frac{y}{\ell}\right)^2 + \left(-i\sqrt{\frac{\hbar e B}{mc}} \ell \frac{\partial}{\partial y} + \frac{1}{2}\sqrt{\frac{\hbar e B}{mc}}\frac{x}{\ell}\right)^2\right ] \non
%&=&\frac{\hbar e B}{2mc}\left[\left(-i\ell\frac{\partial}{\partial x} - \frac{y}{2\ell}\right)^2 + \left(-i\ell\frac{\partial}{\partial y} + \frac{x}{2\ell}\right)^2\right]%\\
%=\frac{\hbar\omega}{2}\left[\left(-i\ell\frac{\partial}{\partial x} - \frac{y}{2\ell}\right)^2 + \left(-i\ell\frac{\partial}{\partial y} + \frac{x}{2\ell}\right)^2\right]
H &=& \frac{1}{2}\left[\left(-i\sqrt{\frac{\hbar e B}{m}} \ell_B \frac{\partial}{\partial x} + \frac{1}{2}\sqrt{\frac{\hbar e B}{m}}\frac{y}{\ell_B}\right)^2 + \left(-i\sqrt{\frac{\hbar e B}{m}} \ell_B \frac{\partial}{\partial y} - \frac{1}{2}\sqrt{\frac{\hbar e B}{m}}\frac{x}{\ell_B}\right)^2\right ] \non
%&=& \frac{\hbar e B}{2m}\left[\left(-i\ell_B\frac{\partial}{\partial x} - \frac{y}{2\ell_B}\right)^2 + \left(-i\ell_B\frac{\partial}{\partial y} + \frac{x}{2\ell_B}\right)^2\right] \non
&=& \frac{\hbar\omega_c}{2}\left[\left(-i\ell_B\frac{\partial}{\partial x} + \frac{y}{2\ell_B}\right)^2 + \left(-i\ell_B\frac{\partial}{\partial y} - \frac{x}{2\ell_B}\right)^2\right]
\EEq
where $\omega_c = eB/m$ is the cyclotron frequency. Now let us measure all lengths in units of magnetic length $\ell_B$.\footnote{In the literature you will often see the equivalent statement that we "set $\ell_B = 1$"; also, the subscript "B" is often omitted in the field but has the advantage that one identifies the length as depending on the magnetic field}  Then,
%Now let's set $\ell=1$ and measure all lengths in units of magnetic length.  Then,
\BEq \label{ham}
H = \frac{\hbar\omega_c}{2}\left[\left(-i\frac{\partial}{\partial x} + \frac{y}{2}\right)^2 + \left(-i\frac{\partial}{\partial y} - \frac{x}{2}\right)^2\right]\;.
\EEq
Use the Wirtinger derivatives to show that 
\BEq
H = \frac{\hbar\omega_c}{2}\left[ -4 \frac{\partial^2}{\partial z\partial\bar{z}} + \frac{1}{4}z\bar{z} - z\frac{\partial }{\partial z} + \bar{z} \frac{\partial}{\partial \bar{z}}\right]\;.
\EEq

\color{blue}

We can use $x = (z+\bar{z})/2$, $y= (z-\bar{z})/2i$ and the expressions above for $\partial/\partial x$ and $\partial /\partial y$.  First though let's expand Eq.~\eqref{ham} and look at each term by itself.
\BEq
\left(-i\frac{\partial}{\partial x} - \frac{y}{2}\right)^2 &=& \left(-i\frac{\partial}{\partial x} - \frac{y}{2}\right)\left(-i\frac{\partial}{\partial x} - \frac{y}{2}\right)\non
&=& - \frac{\partial^2}{\partial x^2} + i\frac{\partial }{\partial x}\frac{y}{2} + i \frac{y}{2}\frac{\partial }{\partial x} + \frac{y^2}{4}
= - \frac{\partial^2}{\partial x^2} + i y\frac{\partial }{\partial x} + \frac{y^2}{4}\;.
\EEq
To be clear, to get the last equality we used the chain rule to get $\partial /\partial x(y/2) = (y/2)\partial/\partial x$. We can now do the second term in the same way
\BEq
\left(-i\frac{\partial}{\partial y} + \frac{x}{2}\right)^2 = -\frac{\partial^2}{\partial y^2} - i x\frac{\partial }{\partial y} + \frac{x^2}{4}\;.
\EEq
Now combine the two expressions
\BEq
\left(-i\frac{\partial}{\partial x} - \frac{y}{2}\right)^2 + \left(-i\frac{\partial}{\partial y} + \frac{x}{2}\right)^2 &=& 
-\frac{\partial^2}{\partial x^2} + i y\frac{\partial }{\partial x} + \frac{y^2}{4} - \frac{\partial^2}{\partial y^2} - i x\frac{\partial }{\partial y} + \frac{x^2}{4}\non
&=& -\frac{\partial^2}{\partial x^2} - \frac{\partial^2}{\partial y^2} + i y\frac{\partial }{\partial x} - i x\frac{\partial }{\partial y} + \frac{x^2 + y^2}{4}\;.
\EEq
We express the first two terms using the result of the previous problem, Eq.~\eqref{Laplacian}. We also replace $x^2+y^2=z\bar{z}$. Then, we have
\BEq\label{sumofsquares}
\left(-i\frac{\partial}{\partial x} - \frac{y}{2}\right)^2 + \left(-i\frac{\partial}{\partial y} + \frac{x}{2}\right)^2 &=& 
-4\frac{\partial^2}{\partial z\partial \bar{z}} + i y\frac{\partial }{\partial x} - i x\frac{\partial }{\partial y} + \frac{z\bar{z}}{4}\;.
\EEq
The middle terms are new but we can do them too, 
\BEq\label{middleterms}
iy \frac{\partial}{\partial x} &=& i\frac{1}{2i}(z-\bar{z}) \left(\frac{\partial }{\partial z} + \frac{\partial }{\partial\bar{z}}\right) - i\frac{1}{2}(z+\bar{z})i\left(\frac{\partial }{\partial z} - \frac{\partial }{\partial\bar{z}}\right) \non
&=&\frac{1}{2}\left[ z\frac{\partial }{\partial z} + z\frac{\partial }{\partial \bar{z}} - \bar{z} \frac{\partial }{\partial z} - \bar{z}\frac{\partial }{\partial \bar{z}}   + z\frac{\partial }{\partial z} - z\frac{\partial }{\partial \bar{z}} +\bar{z}\frac{\partial }{\partial z} - \bar{z}\frac{\partial }{\partial \bar{z}}\right] \non
&=&\frac{1}{2}\left[2z\frac{\partial }{\partial z} - 2\bar{z}\frac{\partial }{\partial \bar{z}}\right] 
= z\frac{\partial }{\partial z} - \bar{z}\frac{\partial }{\partial \bar{z}}\;.
\EEq
Eq.~\eqref{sumofsquares} with Eq.~\eqref{middleterms} finally gives
\BEq\label{prior}
H &=& \frac{\hbar\omega}{2}\left[\left(-i\frac{\partial}{\partial x} - \frac{y}{2}\right)^2 + \left(-i\frac{\partial}{\partial y} + \frac{x}{2}\right)^2\right] 
= \frac{\hbar\omega}{2}\left[-4\frac{\partial^2}{\partial z\partial \bar{z}} + z\frac{\partial }{\partial z} - \bar{z}\frac{\partial }{\partial \bar{z}} + \frac{1}{4}z\bar{z}\right]
\EEq
and we see we are off by a sign in the $z\partial/\partial z - \bar{z}\partial/\partial \bar{z}$ term.  My mistake :-)

\color{magenta}

We can use $x = (z+\bar{z})/2$, $y= (z-\bar{z})/2i$ and the expressions above for $\partial/\partial x$ and $\partial /\partial y$.  First though let's expand Eq.~\eqref{ham} and look at each term by itself.
\BEq
\left(-i\frac{\partial}{\partial x} + \frac{y}{2}\right)^2 &=& \left(-i\frac{\partial}{\partial x} + \frac{y}{2}\right)\left(-i\frac{\partial}{\partial x} + \frac{y}{2}\right)\non
&=& - \frac{\partial^2}{\partial x^2} - i\frac{\partial }{\partial x}\frac{y}{2} - i \frac{y}{2}\frac{\partial }{\partial x} + \frac{y^2}{4}
= - \frac{\partial^2}{\partial x^2} - i y\frac{\partial }{\partial x} + \frac{y^2}{4}\;.
\EEq
To be clear, to get the last equality we used the chain rule to get $\partial /\partial x(y/2) = (y/2)\partial/\partial x$. We can now do the second term in the same way
\BEq
\left(-i\frac{\partial}{\partial y} - \frac{x}{2}\right)^2 = -\frac{\partial^2}{\partial y^2} + i x\frac{\partial }{\partial y} + \frac{x^2}{4}\;.
\EEq
Now combine the two expressions
\BEq
\left(-i\frac{\partial}{\partial x} + \frac{y}{2}\right)^2 + \left(-i\frac{\partial}{\partial y} - \frac{x}{2}\right)^2 &=& 
-\frac{\partial^2}{\partial x^2} - i y\frac{\partial }{\partial x} + \frac{y^2}{4} - \frac{\partial^2}{\partial y^2} + i x\frac{\partial }{\partial y} + \frac{x^2}{4}\non
&=& -\frac{\partial^2}{\partial x^2} - \frac{\partial^2}{\partial y^2} - i y\frac{\partial }{\partial x} + i x\frac{\partial }{\partial y} + \frac{x^2 + y^2}{4}\;.
\EEq
We express the first two terms using the result of the previous problem, Eq.~\eqref{Laplacian}. We also replace $x^2+y^2=z\bar{z}$. Then, we have
\BEq\label{sumofsquares}
\left(-i\frac{\partial}{\partial x} + \frac{y}{2}\right)^2 + \left(-i\frac{\partial}{\partial y} - \frac{x}{2}\right)^2 &=& 
-4\frac{\partial^2}{\partial z\partial \bar{z}} - i y\frac{\partial }{\partial x} + i x\frac{\partial }{\partial y} + \frac{z\bar{z}}{4}\;.
\EEq
The middle terms are new but we can do them too, 
\BEq\label{middleterms}
-iy \frac{\partial}{\partial x} +ix\frac{\partial }{\partial y} &=& -i\frac{1}{2i}(z-\bar{z}) \left(\frac{\partial }{\partial z} + \frac{\partial }{\partial\bar{z}}\right) + i\frac{1}{2}(z+\bar{z})i\left(\frac{\partial }{\partial z} - \frac{\partial }{\partial\bar{z}}\right) \non
&=&\frac{1}{2}\left[ -z\frac{\partial }{\partial z} - z\frac{\partial }{\partial \bar{z}} + \bar{z} \frac{\partial }{\partial z} + \bar{z}\frac{\partial }{\partial \bar{z}}   - z\frac{\partial }{\partial z} + z\frac{\partial }{\partial \bar{z}} -\bar{z}\frac{\partial }{\partial z} + \bar{z}\frac{\partial }{\partial \bar{z}}\right] \non
&=&\frac{1}{2}\left[-2z\frac{\partial }{\partial z} + 2\bar{z}\frac{\partial }{\partial \bar{z}}\right] 
= -z\frac{\partial }{\partial z} + \bar{z}\frac{\partial }{\partial \bar{z}}\;.
\EEq
Eq.~\eqref{sumofsquares} with Eq.~\eqref{middleterms} finally gives
\BEq\label{prior}
H &=& \frac{\hbar\omega}{2}\left[\left(-i\frac{\partial}{\partial x} - \frac{y}{2}\right)^2 + \left(-i\frac{\partial}{\partial y} + \frac{x}{2}\right)^2\right] 
= \frac{\hbar\omega}{2}\left[-4\frac{\partial^2}{\partial z\partial \bar{z}} - z\frac{\partial }{\partial z} + \bar{z}\frac{\partial }{\partial \bar{z}} + \frac{1}{4}z\bar{z}\right]
\EEq


\color{black}

\item Define 
\BM
\BEq
a^\dagger &=& \frac{1}{\sqrt{2}}\left(\frac{\bar{z}}{2} - 2 \frac{\partial}{\partial z}\right)\\
a &=&\frac{1}{\sqrt{2}}\left(\frac{z}{2} + 2 \frac{\partial}{\partial \bar{z}}\right)
\EEq 
\EM
and show that $[a,a^\dagger]=1$.\\
\textit{The commutator $[.,.]$ of operators $a$ and $b$ is defined as $[a,b] = ab - ba$.}

\color{blue}
This is straightforward (though a little tedious),
\BEq\label{commutator}
[a,a^\dagger] &=& aa^\dagger - a^\dagger a\\
&=& \frac{1}{\sqrt{2}}\left(\frac{z}{2} + 2 \frac{\partial}{\partial \bar{z}}\right)\frac{1}{\sqrt{2}}\left(\frac{\bar{z}}{2} - 2 \frac{\partial}{\partial z}\right) - \frac{1}{\sqrt{2}}\left(\frac{\bar{z}}{2} - 2 \frac{\partial}{\partial z}\right)\frac{1}{\sqrt{2}}\left(\frac{z}{2} + 2 \frac{\partial}{\partial \bar{z}}\right)\non
&=&\frac{1}{2}\left[\left(\frac{z\bar{z}}{4} -z\frac{\partial}{\partial z} +\frac{\partial }{\partial\bar{z}}\bar{z} - 4\frac{\partial^2}{\partial z\partial \bar{z}}\right) - \left(\frac{\bar{z}z}{4} + \bar{z}\frac{\partial}{\partial \bar{z}} - \frac{\partial}{\partial z} z - 4\frac{\partial^2}{\partial z\partial \bar{z}}\right)\right]\non
&=&\frac{1}{2}\left[\left(\frac{z\bar{z}}{4} -z\frac{\partial}{\partial z} +1 + \bar{z}\frac{\partial }{\partial\bar{z}} - 4\frac{\partial^2}{\partial z\partial \bar{z}}\right) - \left(\frac{\bar{z}z}{4} + \bar{z}\frac{\partial}{\partial \bar{z}} - 1 - z\frac{\partial}{\partial z} - 4\frac{\partial^2}{\partial z\partial \bar{z}}\right)\right]\non\
&=&\frac{1}{2}\left[\frac{z\bar{z}}{4} -z\frac{\partial}{\partial z} +1 + \bar{z}\frac{\partial }{\partial\bar{z}} - 4\frac{\partial^2}{\partial z\partial \bar{z}} - \frac{\bar{z}z}{4} - \bar{z}\frac{\partial}{\partial \bar{z}} + 1 + z\frac{\partial}{\partial z} + 4\frac{\partial^2}{\partial z\partial \bar{z}}\right]\non
&=&1\;. \nonumber
\EEq
Important is to note that this calculation is a \textit{operator} calculation. $z$ and $\bar{z}$ are complex numbers effectively multiplied with the unit operator $\mathbb{I}$ and $\partial_z$, $\partial_{\bar{z}}$ are derivative operators. They all act on a wave function $\psi$ of the Schr\"odinger equation ($H\psi = E\psi$). This means in particular that $z\partial_z \neq \partial_z z$. To see this it is best to apply the operator to a function. For example:
\begin{align}
\partial_{\bar{z}} \bar{z} \psi(z)  = \partial_{\bar{z}} \left[ \bar{z} \psi(z) \right]
=
\left[ \partial_{\bar{z}} \bar{z} \right] \psi(z) + \bar{z} \left[ \partial_{\bar{z}} \psi(z) \right]
=
\psi(z) + \bar{z}  \partial_{\bar{z}} \psi(z) \neq \bar{z}  \partial_{\bar{z}} \psi(z).
\end{align}
The term $\psi(z)$ appears for $\partial_{\bar{z}} \bar{z}$ and $\partial_z z$. With the factor $1/2$ in front, it gives you the unit you were looking for [$1\psi(z)$].

\color{black}
\item Finally, use $a^\dagger$ and $a$ to rewrite the Hamiltonian as
\BEq \label{finalH}
H = \hbar\omega_c \left(a^\dagger a + \frac{1}{2}\right)\;.
\EEq
Where have you seen this Hamiltonian? What system does it describe? Summarize in one sentence the result you obtained.
%is the Hamiltonian of a  simple harmonic oscillator.

\color{blue}
For this one you can choose two paths: (1) you can see that Eq.~\eqref{prior} looks like the term $a^\dagger a - 1$ in the third line of Eq.~\eqref{commutator}. (2) We can start with Eq.~\eqref{finalH} and prove Eq.~\eqref{prior} using again terms in the derivation of Eq.~\eqref{commutator}:
\BEq
\hbar\omega\left(a^\dagger a + \frac{1}{2}\right)&=& \frac{\hbar\omega}{2}\left(\frac{\bar{z}z}{4} + \bar{z}\frac{\partial}{\partial \bar{z}} - 1 - z\frac{\partial}{\partial z} - 4\frac{\partial^2}{\partial z\partial \bar{z}}\right)+\frac{\hbar\omega}{2}\\
&=&\frac{\hbar\omega}{2}\left(\frac{\bar{z}z}{4} + \bar{z}\frac{\partial}{\partial \bar{z}} - z\frac{\partial}{\partial z} - 4\frac{\partial^2}{\partial z\partial \bar{z}}\right)\;.
\EEq
The $\hbar\omega/2$'s cancel.\\
This is the Hamiltonian of a harmonic oscillator of frequency $\omega_c$, written in second quantization. the $1/2$ term is called the zero-point energy of the oscillator and shows that the lowest energy state of the quantum oscillator is never zero, even at the absolute zero of temperature!

\color{black}

\Een

\Een

\end{document}
