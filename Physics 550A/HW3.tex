\documentclass[12pt]{article}%
\usepackage{amsfonts}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{comment}
\usepackage[a4paper, top=2.5cm, bottom=2.5cm, left=2.2cm, right=2.2cm]{geometry}
\def\shrinkage{2.1mu}
\def\vecsign{\mathchar"017E}
\def\dvecsign{\smash{\stackon[-1.95pt]{\mkern-\shrinkage\vecsign}{\rotatebox{180}{$\mkern-\shrinkage\vecsign$}}}}
\def\dvec#1{\def\useanchorwidth{T}\stackon[-4.2pt]{#1}{\,\dvecsign}}
\usepackage{stackengine}
\stackMath
\usepackage{times}
\usepackage{cancel}
\usepackage{amsmath}
\usepackage{tikz}
\usepackage{changepage}
\usetikzlibrary{shapes.misc}
\usepackage{amssymb}
\usepackage{graphicx}%
\usepackage{physics}
\setcounter{MaxMatrixCols}{30}
\newtheorem{theorem}{Theorem}
\newtheorem{acknowledgement}[theorem]{Acknowledgement}
\newtheorem{algorithm}[theorem]{Algorithm}
\newtheorem{axiom}{Axiom}
\newtheorem{case}[theorem]{Case}
\newtheorem{claim}[theorem]{Claim}
\newtheorem{conclusion}[theorem]{Conclusion}
\newtheorem{condition}[theorem]{Condition}
\newtheorem{conjecture}[theorem]{Conjecture}
\newtheorem{corollary}[theorem]{Corollary}
\newtheorem{criterion}[theorem]{Criterion}
\newtheorem{definition}[theorem]{Definition}
\newtheorem{example}[theorem]{Example}
\newtheorem{exercise}[theorem]{Exercise}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{notation}[theorem]{Notation}
\newtheorem{problem}[theorem]{Problem}
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{remark}[theorem]{Remark}
\newtheorem{summary}[theorem]{Summary}
\newenvironment{proof}[1][Proof]{\textbf{#1.} }{\ \rule{0.5em}{0.5em}}
\newenvironment{solution}[1][Solution]{\textbf{#1}}{\ \rule{0.5em}{0.5em}}
\newcommand{\Q}{\mathbb{Q}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\Z}{\mathbb{Z}}
\newcommand{\N}{\mathbb{N}}
\newcommand{\Lagr}{\mathcal{L}}
\newcommand{\lagr}{\mathcal{L}_0}
\newcommand{\lb}{\left\{}
\newcommand{\rb}{\right\}}
\newcommand{\set}[1]{\left\{#1\right\}}
\newcommand{\pr}[1]{\left(\{#1\right\})}
\newcommand{\dbar}[1]{\bar{\bar{#1}}}
\newcommand{\tbar}[1]{\bar{\bar{\bar{#1}}}}
\title{Physics 550A Homework 3}
\author{Tyler Hadsell}
\begin{document}
\maketitle

\begin{problem}
Determine the eigenvalues and eigenstates of a system with mass $m$ and potential
\[
V(x)=\left\{\begin{array}{lll}
\infty & \text { for } \quad x<0 \\
1 / 2 m \omega^{2} x^{2} & \text { for } \quad x>0
\end{array}\right.
\]
\end{problem}
\begin{solution}\\
First we can begin by writing out the Hamiltonian for such a system:
$$-\frac{\hbar^{2}}{2m}\frac{\partial^{2}}{\partial x^{2}}\psi(x)+V(x)\psi(x)=E\psi(x)$$
We can rewrite this in our operator form:
$$\frac{\hat p ^2}{2m}\psi + \frac{1}{2}m\omega^{2}\hat x^{2}\psi = E\psi$$
Next we want our Hamiltonian to be written as the product of two binomials in terms of our position and momentum operators:
$$\frac{\hat p ^2}{2m}\psi + \frac{1}{2}m\omega^{2}x^{2}\psi + C\psi = c\left(a\hat p + \hat x\right)\left(b\hat p + \hat x\right)$$
We can then expand this out and equate the coefficients of the position and momentum operators to find the values of $a,b,c$ and $C$.
$$\frac{\hat p ^2}{2m}\psi + \frac{1}{2}m\omega^{2}x^{2}\psi + C\psi = c\left(ab\hat p ^2 + \hat x^2 + a\hat p \hat x + b\hat x\hat p\right)$$
$$ = c\left(ab\hat p^2 + \hat x^2 + \left(a + b\right)\hat p\hat x + i\hbar b\right)$$
Equating the coefficients of the position and momentum operators we find:
$$abc = \frac{1}{2m}$$
$$a + b = 0$$
$$c = \frac{1}{2}m\omega^2$$
$$\frac{ab}{2}m\omega^2 = \frac{1}{2m}$$
$$ab = \frac{1}{m^2\omega^2}$$
$$a^2 = - \frac{1}{m^2\omega^2}$$
$$a = \pm\frac{i}{m\omega}$$
$$b =  \mp\frac{i}{m\omega}$$
$$C = \pm\frac{\hbar}{2}m\omega^2\frac{1}{m\omega}= \pm\frac{\hbar}{2}\omega$$
We can then write our Hamiltonian in terms of the position and momentum operators:
$$\frac{\hat p ^2}{2m}\psi + \frac{1}{2}m\omega^{2}x^{2}\psi + \frac{\hbar\omega}{2}\psi = \left(\frac{1}{\sqrt{2}}\left(\frac{i}{\sqrt{m}}\hat p + \sqrt{m}\omega\hat x\right)\right)\left(\frac{1}{\sqrt{2}}\left(-\frac{i}{\sqrt{m}}\hat p + \sqrt{m}\omega\hat x\right)\right)\psi$$
$$\left(E+\frac{\hbar\omega}{2}\right)\psi = \left(\frac{1}{\sqrt{2m}}\left(i\hat p + m\omega\hat x\right)\right)\left(\frac{1}{\sqrt{2m}}\left(-i\hat p + m\omega\hat x\right)\right)\psi$$
Let us define the following operators:
$$\hat a = \frac{1}{\sqrt{2m}}\left(i\hat p + m\omega\hat x\right)$$
$$\hat a^{\dagger} = \frac{1}{\sqrt{2m}}\left(-i\hat p + m\omega\hat x\right)$$
We can then write our Hamiltonian in terms of these operators:
$$\left(\hat H+\frac{\hbar\omega}{2}\right)\psi = \hat a^{\dagger}\hat a\psi$$
Additionally we can switch the order of the operators in the following way:
$$\left(\hat H-\frac{\hbar\omega}{2}\right)\psi= \hat a\hat a^{\dagger}\psi$$
Hence we find the following:
$$\hat H = a^{\dagger}a - \frac{\hbar\omega}{2}$$
$$\hat H = a\hat a^{\dagger} + \frac{\hbar\omega}{2}$$
We can then write the following:
$$\hat H \psi = \frac{1}{2}\left(aa^\dagger + a^\dagger a\right)\psi$$
Let us redefine $a$ and $a^\dagger$ as the following:
$$a\rightarrow \frac{1}{\sqrt{\hbar \omega }} a$$
$$a^\dagger \rightarrow \frac{1}{\sqrt{\hbar \omega }} a^\dagger$$
$$\hat H \psi = \frac{\hbar \omega}{2}\left(aa^\dagger + a^\dagger a\right)\psi = \hbar\omega\left(aa^\dagger + \frac{1}{2}\right)\psi = \hbar\omega\left(a^\dagger a - \frac{1}{2}\right)\psi$$
Now lets find the Hamiltonian of the following state:
$$\hat H \left(a\psi\right) =E\left(a \psi\right) = \hbar\omega\left(aa^\dagger +\frac{1}{2}\right)\left(a\psi\right) = \hbar\omega\left(aa^\dagger a + \frac{1}{2}a\right)\psi$$
$$= a\hbar\omega\left(a^\dagger a + \frac{1}{2}\right)\psi = a\hbar\omega\left(a^\dagger a - \frac{1}{2}\right)\psi + \hbar\omega \left(a\psi\right) = \left(E + \hbar\omega\right)\left(a\psi\right)$$
Hence we find that the energy of the state $a\psi$ is $E + \hbar\omega$.
Therefore we can state that
$$\hat H \left(a^n \psi\right) = \left(E + n\hbar\omega\right)\left(a^n \psi\right)$$
If we state that $\hat H a^0 \psi = E_0 = E$ then we can state that
$$\hat H \left(a^n \psi\right) = E_n = \left(E_0 + n\hbar\omega\right)\left(a^n \psi\right)$$
We recall that $a^\dagger \psi_0=0$
$$E_0 \psi_0 = \hbar\omega\left(aa^\dagger + \frac{1}{2}\right)\psi_0 = \frac{\hbar\omega}{2}\psi_0$$
$$E_0 = \frac{\hbar\omega}{2}$$
$$E_n = \left(E_0 + n\hbar\omega\right) = \left(\frac{\hbar\omega}{2} + n\hbar\omega\right)$$
Therefore we have found the energy eigenvalues of the system.
We now want to find the eigenstates of the system. We can begin by using the fact that $a^\dagger \psi_0=0$
$$a^\dagger \psi_0 = \frac{1}{\sqrt{2m}}\left(-i\hat p + m\omega\hat x\right)\psi_0\left(x\right) = 0$$
$$\hat p \psi_0 = m\omega\hat x\psi_0 = 0$$
$$\hbar \frac{\partial \psi_0}{\partial x} + m\omega x\psi_0 = 0$$
Rearranging we obtain the following:
$$\frac{\partial \psi_0}{\partial x} + \frac{m\omega}{\hbar}x\psi_0 = 0$$
Let us define the following:
$$\frac{m\omega}{\hbar} = \alpha$$
$$\frac{\partial \psi_0}{\partial x} + \alpha x\psi_0 = 0$$
Now let us assume a power series solution for $\psi_0$
$$\psi_0 = \sum_{n=0}^{\infty}a_n x^n$$
$$\frac{\partial \psi_0}{\partial x} = \sum_{n=0}^{\infty}na_n x^{n-1}$$
$$\sum_{n=0}^{\infty}na_n x^{n-1} + \alpha x\sum_{n=0}^{\infty}a_n x^n = 0$$
$$\sum_{n=0}^{\infty}na_n x^{n-1} + \sum_{n=0}^{\infty}\alpha a_n x^{n+1} = 0$$
Now let use reindex the first sum and take two initial terms:
$$n\rightarrow n+1$$
$$\sum_{n=-1} ^\infty \left(n+1\right)a_{n+1}x^n + \sum_{n=0}^{\infty}\alpha a_n x^{n+1} = 0$$
$$\sum_{n=0}^{\infty}\left(n+1\right)a_{n+1}x^n + \sum_{n=0}^{\infty}\alpha a_n x^{n+1} = 0$$
We can reindex the first sum again to obtain the following:
$$\sum_{n=0}^{\infty}\left(n+2\right)a_{n+2}x^{n+1} + \sum_{n=0}^{\infty}\alpha a_n x^{n+1} = 0$$
This implies the following:
$$\left(n+2\right)a_{n+2} + \alpha a_n = 0$$
$$a_{n+2} = -\frac{\alpha}{\left(n+2\right)}a_n$$
We can now write the following:
$$a_2 = -\frac{\alpha}{2}a_0$$
$$a_3 = -\frac{\alpha}{3}a_1$$
This is a harmonic oscillator so the wave function must be even. Therefore we can state that $a_1 = 0$.
$$a_2 = -\frac{\alpha}{2}a_0$$
$$a_3 = 0$$
$$a_4 = -\frac{\alpha}{4}a_2 = \frac{\alpha^2}{8}a_0$$
$$a_5 = 0$$
$$a_6 = -\frac{\alpha}{6}a_4 = -\frac{\alpha^3}{48}a_0$$
Hence we see that
$$a_{2n} = \frac{\alpha^n}{2^n n!}a_0$$
Let $\alpha = \beta^2$
$$a_{2n} = \frac{\beta^{2n}}{2^n n!}a_0$$
Therefore we can state that
$$\psi_0 = a_0\sum_{n=0}^{\infty}\frac{\left(-1\right)^n\beta^{2n}}{2^n n!}x^{2n} = a_0\left(1 - \frac{\beta^2}{2}x^2 + \frac{\beta^4}{8}x^4 - \frac{\beta^6}{48}x^6 + \cdots\right)$$
If we reparametrize $\frac{x^2}{2}\rightarrow u$:
$$\psi_0 = a_0\left(1 - \beta^2 u + \frac{\beta^4}{2}u^2 - \frac{\beta^6}{6}u^3 + \cdots\right)$$
We can then write the following:
$$\psi_0 = a_0\exp\left(-\beta^2 u\right)$$
$$\psi_0 = a_0\exp\left(-\frac{m\omega x^2}{2\hbar}\right)$$
Starting with the ground state we can find all of the higher states using the $a$ operator:
$$\psi_1 = a\psi_0 = \frac{1}{\sqrt{2m\omega\hbar}}\left(-i\hat p + m\omega\hat x\right)\psi_0$$
$$\psi_1 = \frac{1}{\sqrt{2m\omega\hbar}}\left(-\hbar\frac{\partial}{\partial x} + m\omega x\right)\psi_0$$
$$\psi_1 = \frac{1}{\sqrt{2m\omega\hbar}}\left(-\hbar\frac{\partial}{\partial x} + m\omega x\right)a_0\exp\left(-\frac{m\omega x^2}{2\hbar}\right)$$
$$\psi_1 = a_0 \left(\sqrt{\frac{m\omega}{2\hbar}}x - \sqrt{\frac{\hbar}{2m\omega}}\frac{\partial }{\partial x}\right)\exp \left(-\frac{m\omega x^2}{2\hbar}\right)$$
Let us define the following:
$$w^2 = \frac{m\omega}{2\hbar}x^2$$
$$w = \sqrt{\frac{m\omega}{2\hbar}}x$$
We can now reparametrize the wave function in terms of a new variable $w$.
$$\frac{\partial }{\partial w} = \sqrt{\frac{2\hbar}{m\omega}}\frac{\partial }{\partial x}$$
$$\psi_1 = a_0\left(w - \frac{1}{2}\frac{\partial }{\partial w}\right)\exp \left(-w^2\right)$$
$$\psi_1=\frac{a_0}{2}\left(2w - \frac{\partial }{\partial w}\right)\exp \left(-w^2\right)$$
Using this parametrization we see that our $a$ operator can be redefined as:
$$a = \frac{1}{2}\left(2w - \frac{\partial }{\partial w}\right)$$
We can now find the higher states using the $a$ operator:
$$\psi_n = a^n \psi_0$$
$$\psi_n = \frac{a_0}{2^n}\left(2w - \frac{\partial }{\partial w}\right)^n\exp \left(-w^2\right)$$
We can now find the normalization constant $a_0$
$$\int_{-\infty}^{\infty}\psi_0^*\psi_0 dx = 1$$
$$\frac{1}{a_0^2} = \int_{-\infty}^{\infty}\exp\left(-\frac{m\omega x^2}{\hbar}\right)dx = \sqrt{\frac{\hbar}{m\omega}}$$
$$a_0 = \left(\frac{m\omega}{\hbar}\right)^{\frac{1}{4}}$$
Therefore we can state that
$$\psi_n = \frac{a_n}{2^n }\left(\frac{m\omega}{\hbar}\right)^{\frac{1}{4}}\left(2w - \frac{\partial }{\partial w}\right)^n\exp \left(-w^2\right)$$
We recognize that each wavefunction must be normalized so we add a $a_n$ normalization constant to each wavefunction.
These wavefunctions are Gaussian wavefunctions with Hermite polynomials as coefficients.
$$\psi_n = \frac{a_n}{2^n }\left(\frac{m\omega}{\hbar}\right)^{\frac{1}{4}}H_n(w)\exp \left(-w^2\right)$$
If we reintroduce the $x$ variable we obtain the following:
$$\psi_n = \frac{a_n}{2^n }\left(\frac{m\omega}{\hbar}\right)^{\frac{1}{4}}H_n\left(\sqrt{\frac{m\omega}{2\hbar}}x\right)\exp \left(-\frac{m\omega x^2}{2\hbar}\right)$$
Further simplifying we can write the following redefinitions:
$$A_n = \frac{a_n}{2^n }\left(\frac{m\omega}{\hbar}\right)^{\frac{1}{4}}$$
$$\boxed{\psi_n = A_n H_n\left(\sqrt{\frac{m\omega}{2\hbar}}x\right)\exp \left(-\frac{m\omega x^2}{2\hbar}\right)}$$
This was a long development but we have now found the wavefunctions for the harmonic oscillator, but we were tasked with finding the wavefunctions for the following potential:

\[
V(x)=\left\{\begin{array}{lll}
\infty & \text { for } \quad x<0 \\
1 / 2 m \omega^{2} x^{2} & \text { for } \quad x>0
\end{array}\right.
\]


We are now going to solve for the simple potential well for $E<V_0$, and then apply our piecewise conditions to our wavefunctions:
$$\frac{\partial^2 \psi}{\partial x^2} - \frac{2m}{\hbar^2}\left(E - V_0\right)\psi$$
This is a second order differential equation. We can solve this using the following ansatz:
$$\psi = Ae^{kx} + Be^{-kx}$$
$$\psi = Ae^{i\sqrt{\frac{2m}{\hbar^2}\left(E - V_0\right)}x} + Be^{-i\sqrt{\frac{2m}{\hbar^2}\left(E - V_0\right)}x}$$
$$\psi = Ae^{-\sqrt{\frac{2m}{\hbar^2}\left(V_0-E\right)}x} + Be^{\sqrt{\frac{2m}{\hbar^2}\left(V_0 - E\right)}x}$$
We are going to consider now the case where $x<0$ as this is the region where the potential is infinite.
$$\psi = Be^{\sqrt{\frac{2m}{\hbar^2}\left(V_0-E\right)}x} \text{ } A \text{ must be zero for normalizability}$$
Now let us redefine $-x$ as $\chi\in [0,\infty)$:
$$\psi = Be^{-\sqrt{\frac{2m}{\hbar^2}\left(V_0-E\right)}\chi}$$
If we take the limit as $V_0 \rightarrow \infty$ we obtain the following:
$$\psi = \lim_{V_0 \to \infty}Be^{-\sqrt{\frac{2m}{\hbar^2}\left(V_0-E\right)}\chi} = Be^{-\infty} = 0$$
This is the wavefunction for the infinite square well, outside of the well.
Now we require that our wavefunction be continuous and differentiable at $x=0$.
Therefore we require that our harmonic oscillator wave function must be equal to $0$ at $x=0$ and have a derivative equal to $0$ at $x=0$.
$$\psi_n\left(x\right) = A_n H_n\left(\sqrt{\frac{m\omega}{2\hbar}}x\right)\exp \left(-\frac{m\omega x^2}{2\hbar}\right)$$
$$\frac{\partial \psi_n}{\partial x} = A_n 2n\sqrt{\frac{m\omega}{2\hbar}}H_{n-1}\left(\sqrt{\frac{m\omega}{2\hbar}}x\right)\exp \left(-\frac{m\omega x^2}{2\hbar}\right) - A_n \sqrt{\frac{m\omega}{2\hbar}}xH_n\left(\sqrt{\frac{m\omega}{2\hbar}}x\right)\exp \left(-\frac{m\omega x^2}{2\hbar}\right)$$
To ensure our derivative is $0$ at $x=0$ we write the following:
$$0 = A_n 2n\sqrt{\frac{m\omega}{2\hbar}}H_{n-1}\left(0\right)$$
This condition only holds for $n=2k+1$ where $k\in \mathbb{N}$.
This condition is also true for the original wave function.
$$0 = A_n H_n\left(0\right)$$
Therefore we see that our wavefunctions are the following:
$$\psi_{2k+1} = A_{2k+1} H_{2k+1}\left(\sqrt{\frac{m\omega}{2\hbar}}x\right)\exp \left(-\frac{m\omega x^2}{2\hbar}\right) \text{ for } x\in [0,\infty), k\in \mathbb{N}$$
Hence the eigenstates of our wavefunctions are the following:
$$\boxed{\psi_{k}(x) = \begin{cases}
A_{2k+1} H_{2k+1}\left(\sqrt{\frac{m\omega}{2\hbar}}x\right)\exp \left(-\frac{m\omega x^2}{2\hbar}\right) & \text{for } x \in (0,\infty) \\
0 & \text{for } x \in (-\infty,0]
\end{cases}, \quad k \in \mathbb{N}}$$
Additionally, the energy eigenstates are the following:
$$\boxed{E_{k} = \left(2k +\frac{3}{2}\right)\hbar\omega}$$
\end{solution}
\begin{problem}
    Using Heisenberg uncertainty relation find a lower limit for the ground state of a particle in the potential
    \[
    V(x)=\frac{m \omega^{2} x^{2}}{2}+\lambda^{\prime} x^{4}, \quad \lambda^{\prime}>0
    \]
    Consider the $\omega^{2}>0$ and $\omega^{2}<0$ cases. Draw potential curves.
    \end{problem}
    \begin{solution}\\
        We can begin this problem by writing out our Hamiltonian:
        $$\hat{H} = \frac{\hat{p}^2}{2m} + \frac{1}{2}m\omega^2\hat{x}^2 + \lambda^{\prime}\hat{x}^4$$
        Next we will find the expectation value of the Hamiltonian:
        $$\langle E\rangle = \frac{1}{2m}\langle p^2 \rangle +  \frac{1}{2}m\omega^2\langle x^2 \rangle + \lambda^{\prime}\langle x^4 \rangle$$
        We want to write this in terms of uncertainties:
        $$\left(\Delta x\right)^2 =  \langle x^2 \rangle - \langle x \rangle^2$$
        $$\left(\Delta p\right)^2 =  \langle p^2 \rangle - \langle p \rangle^2$$
        $$\left(\Delta x^2\right)^2 = \langle x^4 \rangle - \langle x^2 \rangle^2$$
        We can recognize via Ehrenfest's theorem that since our potential is time independant, the expectation value of the momentum is $0$. Hence:
        $$\left(\Delta p\right)^2 = \langle p^2 \rangle$$
        Additionally our potentials are symmetric about $x=0$ so we can write:
        $$\langle x \rangle = 0$$
        $$\left(\Delta x\right)^2 =  \langle x^2 \rangle$$
        $$\left(\Delta x^2\right)^2 = \langle x^4 \rangle - \left(\Delta x\right)^4$$
        We can now write our Hamiltonian in terms of our uncertainties:
        $$\langle E\rangle = \frac{1}{2m}\langle p^2 \rangle +  \frac{1}{2}m\omega^2\langle x^2 \rangle + \lambda^{\prime}\langle x^4 \rangle$$
        $$\langle E\rangle = \frac{1}{2m}\left(\Delta p\right)^2 +  \frac{1}{2}m\omega^2\left(\Delta x\right)^2 + \lambda^{\prime}\left(\left(\Delta x^2\right)^2 + \left(\Delta x\right)^4\right)$$
        We can now use the uncertainty relation:
        $$\left(\Delta x\right)^2\left(\Delta p\right)^2 \geq \frac{\hbar^2}{4}$$
        Minimizing our uncertainties we state:
        $$\left(\Delta p\right)^2 = \frac{\hbar^2}{4\left(\Delta x\right)^2}$$
        Now we substitute this into our Hamiltonian:
        $$\langle E\rangle = \frac{1}{2m}\left(\frac{\hbar^2}{4\left(\Delta x\right)^2}\right) +  \frac{1}{2}m\omega^2\left(\Delta x\right)^2 + \lambda^{\prime}\left(\left(\Delta x^2\right)^2 + \left(\Delta x\right)^4\right)$$
        We first notice that the energy is minimized when $\Delta x^2 = 0$ so for the remainder of the problem we assume this is the case.
        $$\langle E\rangle = \frac{1}{2m}\left(\frac{\hbar^2}{4\left(\Delta x\right)^2}\right) +  \frac{1}{2}m\omega^2\left(\Delta x\right)^2 + \lambda^{\prime}\left(\Delta x\right)^4$$
        We can now minimize this expression:
        $$\frac{\partial \langle E\rangle}{\partial \Delta x} = -\frac{1}{2m}\left(\frac{\hbar^2}{4\left(\Delta x\right)^3}\right) +  \frac{1}{2}m\omega^2\left(\Delta x\right) + 4\lambda^{\prime}\left(\Delta x\right)^3$$
        $$0 = -\frac{1}{2m}\left(\frac{\hbar^2}{4\left(\Delta x\right)^3}\right) +  \frac{1}{2}m\omega^2\left(\Delta x\right) + 4\lambda^{\prime}\left(\Delta x\right)^3$$
        We can multiply both sides by $\Delta x^3$ to get:
        $$0 = -\frac{1}{2m}\left(\frac{\hbar^2}{4}\right) +  \frac{1}{2}m\omega^2\left(\Delta x\right)^4 + 4\lambda^{\prime}\left(\Delta x\right)^6$$
        This is a cubic equation in $\left(\Delta x\right)^2$ which we can solve using the cubic formula. These will be absolutely ugly so I will not write them out here. But we will denote these minimum solutions as the following:
        $$\left(\Delta x\right)^2 = \left(\Delta x\right)_{i}^2 \text{ where } i\in\{1,2,3\}$$
        We denote the solution that truly minimizes the energy as $\left(\Delta x\right)_{\text{min}}^2$.
        We can now plug these values back into our Hamiltonian to get the following:
        $$\langle E\rangle_\text{min} = \frac{1}{2m}\left(\frac{\hbar^2}{4\left(\Delta x\right)_\text{min}^2}\right) +  \frac{1}{2}m\omega^2\left(\Delta x\right)_\text{min}^2 + \lambda^{\prime}\left(\Delta x\right)_\text{min}^4$$
        Hence this is the lower limit on the energy of the ground state.\\
        The following plots are for the scenario with $\omega^2>0$ and $\omega^2<0$ respectively.\\
        \begin{center}
            
        \includegraphics[scale=0.15]{Plot1.png}
        
        \includegraphics[scale=0.15]{Plot2.png}
    \end{center}
    \end{solution}

\begin{problem}
Consider harmonic oscillator system.
\begin{enumerate}
    \item[(a)] Calculate the commutators
    \[
    \left[\hat{a}, f\left(\hat{a}^{\dagger}\right)\right] \quad \text{and} \quad \left[\hat{a}^{\dagger}, f(\hat{a})\right]
    \]
    for $f$ being a power, a polynomial, and a convergent power series.
    \item[(b)] Prove the relations
    \[
    \hat{a} f\left(\hat{a}^{\dagger}\right)|0\rangle = f^{\prime}\left(\hat{a}^{\dagger}\right)|0\rangle \quad \text{and} \quad \hat{a}^{2} f\left(\hat{a}^{\dagger}\right)|0\rangle = f^{\prime \prime}\left(\hat{a}^{\dagger}\right)|0\rangle
    \]
    \item[(c)] Construct the even and odd states
    \[
    |\eta,+\rangle = \frac{1}{\sqrt{\cosh |\eta|^{2}}} \cosh \left(\eta \hat{a}^{\dagger}\right)|0\rangle \quad \text{and} \quad |\eta,-\rangle = \frac{1}{\sqrt{\sinh |\eta|^{2}}} \sinh \left(\eta \hat{a}^{\dagger}\right)|0\rangle
    \]
    Show that they are eigenstate of $\hat{a}^{2}$.
\end{enumerate}
\end{problem}
\begin{solution}\\
    a.\\ 
    First we note that because the commutator of $\hat{a}$ and $\hat{a}^{\dagger}$ is $1$ we can write:
    $$\left[a,\left[a,a^{\dagger}\right]\right] = 0 $$
    This implies from our proof in assignment 1 that:
    $$\left[a,a^{\dagger n}\right] = n a^{\dagger n-1}\left[a,a^{\dagger}\right] = n a^{\dagger n-1}$$
    This is is the same for the commutator of $\hat{a}^{\dagger}$ and $\hat{a}$.
    $$\left[a^{\dagger},a^{n}\right] = n a^{n-1}\left[a^\dagger,a\right] = -n a^{n-1}$$
    Next instead of building our way up from the power function to the power series we will build our way down from the power series to the power function.
    Let us first consider the power series function:
    $$f(a^\dagger) = \sum_{n=0}^{\infty}c_n a^{\dagger n}$$
    We can now take the commutator of $\hat{a}$ and $f(a^\dagger)$:
    $$\left[a,f(a^\dagger)\right] = \left[a, \sum _{n=0}^{\infty}c_n a^{\dagger n}\right] = \sum_{n=0}^{\infty}c_n \left[a, a^{\dagger n}\right] = \sum_{n=0}^{\infty}c_n n a^{\dagger n-1}$$
    We can now do the same for $\left[a^\dagger, a\right]$:
    $$\left[a^\dagger,f(a)\right] = \left[a^\dagger, \sum _{n=0}^{\infty}c_n a^{n}\right] = \sum_{n=0}^{\infty}c_n \left[a^\dagger, a^{n}\right] = \sum_{n=0}^{\infty}-c_n n a^{n-1}$$
    Now to find a polynomial function we assume the same form but with the sum terminating at finite $N$:
    $$f(a^\dagger) = \sum_{n=0}^{N}c_n a^{\dagger n}$$
    $$f(a) = \sum_{n=0}^{N}c_n a^{n}$$
    We can now take the commutator of $\hat{a}$ and $f(a^\dagger)$:
    $$\left[a,f(a^\dagger)\right] = \left[a, \sum _{n=0}^{N}c_n a^{\dagger n}\right] = \sum_{n=0}^{N}c_n \left[a, a^{\dagger n}\right] = \sum_{n=0}^{N}c_n n a^{\dagger n-1}$$
    We can now do the same for $\left[a^\dagger, a\right]$:
    $$\left[a^\dagger,f(a)\right] = \left[a^\dagger, \sum _{n=0}^{N}c_n a^{n}\right] = \sum_{n=0}^{N}c_n \left[a^\dagger, a^{n}\right] = \sum_{n=0}^{N}-c_n n a^{n-1}$$
    We obtain the same result as before, although with a finite sum.
    Finally we can consider the power function:
    $$f(a^\dagger) = a^{\dagger n}$$
    $$f(a) = a^{n}$$
    We can now take the commutator of $\hat{a}$ and $f(a^\dagger)$:
    $$\left[a,f(a^\dagger)\right] = \left[a, a^{\dagger n}\right] = n a^{\dagger n-1}$$
    We can now do the same for $\left[a^\dagger, a\right]$:
    $$\left[a^\dagger,f(a)\right] = \left[a^\dagger, a^{n}\right] = -n a^{n-1}$$
    This apparently implies that these commutator operators act as some set of derivative operators.
    b.\\
    We can now use the commutator of $\hat{a}$ and $\hat{a}^{\dagger}$ to show that:
    $$\left[a,a^{\dagger n}\right] = n a^{\dagger n-1}\left[a,a^{\dagger}\right] = n a^{\dagger n-1}$$
    We can therefore write the following:
    $$\left[a, f(a^\dagger)\right] = a f(a^\dagger) - f(a^\dagger) a $$
    Solving for the operator given in the problem statement:
    $$a f(a^\dagger) = \left[a, f(a^\dagger)\right] + f(a^\dagger) a $$
    Now we will act on the $|0\rangle$ state:
    $$a f(a^\dagger) |0\rangle = \left[a, f(a^\dagger)\right] |0\rangle + f(a^\dagger) a |0\rangle $$
    Since $a$ is the lowering operator we know that $a|0\rangle = 0$ and therefore:
    $$a f(a^\dagger) |0\rangle = \left[a, f(a^\dagger)\right] |0\rangle  = f'(a^\dagger) |0\rangle $$
    Now to operator $a$ on this resultant state to find the second derivative:
    $$a^2 f(a^\dagger) |0\rangle = a \left[a, f(a^\dagger)\right] |0\rangle  = a f'(a^\dagger) |0\rangle $$
    We will rename $f'(a^\dagger)$ as $g(a^\dagger)$:
    $$a^2 f(a^\dagger) |0\rangle = a g(a^\dagger) |0\rangle = g'(a^\dagger) |0\rangle $$
    Now reintroducing $g(a^\dagger)$ as $f'(a^\dagger)$ we have:
    $$a^2 f(a^\dagger) |0\rangle = f''(a^\dagger) |0\rangle $$
    c.\\
    We have hence shown that the operator $a$ acts as a derivative operator on the state $f(a^\dagger) |0\rangle$. We can therefore use this property on the constructed states:
    $$a^2 |\eta,+\rangle = a^2 \frac{1}{\sqrt{\cosh |\eta|^{2}}} \cosh \left(\eta \hat{a}^{\dagger}\right)|0\rangle = a a \frac{1}{\sqrt{\cosh |\eta|^{2}}} \cosh \left(\eta \hat{a}^{\dagger}\right) |0\rangle $$
    $$= a \frac{\eta}{\sqrt{\cosh |\eta|^{2}}} \sinh \left(\eta \hat{a}^{\dagger}\right)|0\rangle = \frac{\eta^2}{\sqrt{\cosh |\eta|^{2}}} \cosh \left(\eta \hat{a}^{\dagger}\right)|0\rangle  = \eta^2 |\eta,+\rangle $$
    Therefore the eigenvalue of $a^2$ for the even state is $\eta^2$. We can do the same for the odd state:
    $$a^2 |\eta,-\rangle = a^2 \frac{1}{\sqrt{\sinh |\eta|^{2}}} \sinh \left(\eta \hat{a}^{\dagger}\right)|0\rangle = a a \frac{1}{\sqrt{\sinh |\eta|^{2}}} \sinh \left(\eta \hat{a}^{\dagger}\right) |0\rangle $$
    $$= a \frac{\eta}{\sqrt{\sinh |\eta|^{2}}} \cosh \left(\eta \hat{a}^{\dagger}\right)|0\rangle = \frac{\eta^2}{\sqrt{\sinh |\eta|^{2}}} \sinh \left(\eta \hat{a}^{\dagger}\right)|0\rangle  = \eta^2 |\eta,-\rangle $$
    Therefore the eigenvalue of $a^2$ for the odd state is $\eta^2$.
    We have shown that the eigenvalues of $a^2$ for the even and odd states are $\eta^2$, and therefore the even and odd states are eigenstates of $a^2$.
\end{solution}

\begin{problem}
Consider the operators
\[
\hat{J}_{+}=\hat{J}_{1}+i \hat{J}_{2}=\sqrt{2 j-\hat{N}} \hat{a} \quad \hat{J}_{-}=\hat{J}_{1}-i \hat{J}_{2}=\hat{a}^{\dagger} \sqrt{2 j-\hat{N}}
\]
with $2 j \in\{1,2,3, \ldots\}, \hat{a}$ and $\hat{a}^{\dagger}$ satisfy $\left[\hat{a}, \hat{a}^{\dagger}\right]=1$, and $\hat{N}=\hat{a}^{\dagger} \hat{a}$.
\begin{enumerate}
    \item[(a)] Show that both operators, $\hat{J}_{+}$ and $\hat{J}_{-}$, act on the subspace spanned by the eigenstates of $\hat{H}$
    \[
    |0\rangle,|1\rangle,|2\rangle, \ldots,|2 j\rangle
    \]
    and their action leaves the subspace invariant.
    \item[(b)] Find the operator with the properties
    \[
    \left[\hat{J}_{+}, \hat{J}_{-}\right]=2 \hat{J}_{3} \quad \text{and} \quad\left[\hat{J}_{3}, \hat{J}_{\pm}\right]= \pm \hat{J}_{\pm}
    \]
    \item[(c)] Show that in that subspace
    \[
    \hat{\boldsymbol{J}}^{2}=\hat{J}_{1}^{2}+\hat{J}_{2}^{2}+\hat{J}_{3}^{2}
    \]
    is $j(j+1)$ times of the unit operator.
\end{enumerate}
\end{problem}
We can first consider the action of the operators $\hat{J}_{+}$ and $\hat{J}_{-}$ on the state $|n\rangle$:
$$\hat{J}_{+}|n\rangle = \sqrt{2j-\hat N}\hat{a}|n\rangle = \sqrt{2j-\hat N} \sqrt{n}|n-1\rangle = \sqrt{n\left(2j - \left(n-1\right)\right)}|n-1\rangle$$
$$\hat{J}_{-}|n\rangle = \hat{a}^{\dagger} \sqrt{2 j-\hat{N}}|n\rangle = \hat{a}^{\dagger} \sqrt{2 j-n} |n\rangle = \sqrt{\left(n+1\right)\left(2j - n\right)}|n+1\rangle$$
Since we see that the square root part of the operator is a diagonal matrix we can see it will not alter the state and will merely give an eigenstate. The $a$ and $a^\dagger$ operators will change the states however. They will raise or lower the states by 1, therefore we want to see the effects of each of these operators on our boundary states $n=0$ and $n=2j$\\ 
First we will apply $\hat J_{+}$ and $\hat J_{-}$ to our $n=0$ state:
$$\hat J_{+} |0\rangle = \sqrt{0\left(2j - \left(0-1\right)\right)}|0-1\rangle = 0$$
$$\hat J_{-} |0\rangle = \sqrt{\left(0+1\right)\left(2j - 0\right)}|0+1\rangle = \sqrt{2j}|1\rangle$$
Next we will apply $\hat J_{+}$ and $\hat J_{-}$ to our $n=2j$ state:
$$\hat J_{+} |2j\rangle = \sqrt{2j\left(2j - \left(2j-1\right)\right)}|2j-1\rangle = \sqrt{2j}|2j-1\rangle$$
$$\hat J_{-} |2j\rangle = \sqrt{\left(2j+1\right)\left(2j - 2j\right)}|2j+1\rangle = 0$$
Therefore we see that the action of the operators $\hat J_{+}$ and $\hat J_{-}$ on the boundary states $n=0$ and $n=2j$ will not take us out of the subspace spanned by the eigenstates of $\hat H$.\\
b.\\
We can now find the operator that satisfies the commutation relations:
$$\left[\hat J_{+}, \hat J_{-}\right] = 2\hat J_{3}$$
$$\left[\hat J_{3}, \hat J_{\pm}\right] = \pm \hat J_{\pm}$$
We can first find the commutator of $\hat J_{+}$ and $\hat J_{-}$:
$$\left[\hat J_{+}, \hat J_{-}\right] |n\rangle = \left[\sqrt{2 j-\hat{N}} \hat{a}, \hat{a}^{\dagger} \sqrt{2 j-\hat{N}}\right]|n\rangle $$
$$= \sqrt{2 j-\hat{N}} \hat{a} \hat{a}^{\dagger} \sqrt{2 j-\hat{N}}|n\rangle  - \hat{a}^{\dagger} \sqrt{2 j-\hat{N}} \sqrt{2 j-\hat{N}} \hat{a}|n\rangle $$
$$= \sqrt{2j - \hat N} \left(\hat N+1\right) \sqrt{2j - \hat N}|n\rangle  - \hat a^\dagger \left(2j - \hat N\right) \sqrt{n}|n-1\rangle$$
$$= \left(\left(n+1\right)\left(2j-n\right)\right)|n\rangle - n\left(2j-\left(n-1\right)\right)|n\rangle = \left(2jn + 2j -n^2 - n - 2jn + n^2 -n \right)|n\rangle $$$$= 2\left(j-n\right)|n\rangle$$
$$\boxed{\hat J_3 |n\rangle = \left(j-n\right)|n\rangle = \left(j - \hat N\right)|n\rangle}$$
Now we can see if this operator satisfies the second commutation relation:
$$\left[\hat J_3, \hat J_{\pm}\right] = \pm \hat J_{\pm}$$
$$\left[\left(j - \hat N\right), \sqrt{2j - \hat N}\hat a\right]|n\rangle = \left(j-\hat N\right)\sqrt{2j-\hat N}\hat a |n\rangle - \sqrt{2j-\hat N}\hat a \left(j-\hat N\right)|n\rangle$$
$$= \left(j-\hat N\right)\sqrt{2j-\hat N}\sqrt{n}|n-1\rangle - \sqrt{2j-\hat N}\sqrt{n}\left(j-n\right)|n-1\rangle$$
$$= \left(j-\left(n-1\right)\right)\sqrt{2j-\left(n-1\right)}\sqrt{n}|n-1\rangle - \sqrt{2j-\left(n-1\right)}\sqrt{n}\left(j-n\right)|n-1\rangle$$
$$= \boxed{\sqrt{2j-\left(n-1\right)}\sqrt{n}\left(j-n+1 - j + n\right)|n-1\rangle = \sqrt{n\left(2j-\left(n-1\right)\right)}|n-1\rangle = \hat J_{+}|n\rangle}$$
Now for the other case:
$$\left[\left(j - \hat N\right), \hat a^\dagger \sqrt{2j - \hat N}\right]|n\rangle = \left(j-\hat N\right)\hat a^\dagger \sqrt{2j-\hat N} |n\rangle - \hat a^\dagger \sqrt{2j-\hat N} \left(j-\hat N\right)|n\rangle$$
$$= \left(j - \left(n+1\right)\right)\sqrt{\left(n+1\right)\left(2j-n\right)}|n+1\rangle - \sqrt{\left(n+1\right)\left(2j-n\right)}\left(j-n\right)|n+1\rangle$$
$$= \boxed{\sqrt{\left(n+1\right)\left(2j-n\right)}\left(j-n-1 - j + n\right) = -\sqrt{\left(n+1\right)\left(2j-n\right)}|n+1\rangle = -\hat J_{-}|n\rangle}$$
Therefore we have found the operator that satisfies the commutation relations.
c.\\
We can now show that the operator $\hat{\boldsymbol{J}}^{2}$ is $j(j+1)$ times the unit operator:
We will first find $J_1$ and $J_2$:
$$J_1 = \frac{1}{2}\left(J_+ + J_-\right) = \frac{1}{2}\left(\sqrt{2j-\hat N}\hat a + \hat a^\dagger \sqrt{2j-\hat N}\right)$$
$$J_2 = \frac{1}{2i}\left(J_+ - J_-\right) = \frac{1}{2i}\left(\sqrt{2j-\hat N}\hat a - \hat a^\dagger \sqrt{2j-\hat N}\right)$$
These imply that:
$$\frac{1}{2}\left(J_+ J_- + J_- J_+\right)|n\rangle= \left(J_1^2 + J_2^2 \right)|n\rangle= \frac{1}{2}\left(\left(n+1\right)\left(2j-n\right) + \left(n\left(2j-\left(n-1\right)\right)\right)\right)|n\rangle$$
$$ = \frac{1}{2}\left(2jn + 2j -n^2 - n + 2jn -n^2 + n\right)|n\rangle = \frac{1}{2}\left(4jn + 2j - 2n^2\right)|n\rangle = \left(2jn + j - n^2\right)|n\rangle$$
$$J_3 = j - \hat N$$
$$J_3^2 = j^2 + \hat N^2 - 2j\hat N$$
$$J_3^2 |n\rangle = \left(j^2 + n^2 - 2jn\right)$$
$$\boxed{\left(J_1^2 + J_2^2 + J_3^2\right)|n\rangle = \left(2jn + j - n^2 + j^2 + n^2 - 2jn\right)|n\rangle = \left(j^2 + j\right)|n\rangle = j(j+1)|n\rangle}$$
Therefore we have shown that the operator $\hat{\boldsymbol{J}}^{2}$ is $j(j+1)$ times the unit operator.

\begin{problem}
Find the eigenvalues of the Hamiltonian
\[
\hat{H}=E \hat{a} \hat{a}^{\dagger}+V\left(\hat{a}+\hat{a}^{\dagger}\right)
\]
where $\left[\hat{a}, \hat{a}^{\dagger}\right]=\beta^{2}, E$ and $V$ and $\beta^{2}$ are positive constant.
\end{problem}
\begin{solution}\\
We can first write the form of our ladder operators given the commutator bracket:
$$\left[\hat a, \hat a^\dagger\right]|n\rangle = \beta^2|n\rangle = \hat a \hat a^\dagger |n\rangle - \hat a^\dagger \hat a |n\rangle$$
$$=\hat a a_n \sqrt{n+1}|n+1\rangle - \hat a^\dagger b_n\sqrt{n}|n-1\rangle = a_n b_{n+1}\left(n+1\right)|n\rangle - b_n a_{n-1}n|n\rangle = \beta^2|n\rangle$$
$$a_n b_{n+1} = b_n a_{n-1} = \beta^2$$
$$\frac{a_n}{a_{n-1}} = \frac{b_n}{b_{n+1}}$$
We see that the ladder operators when used together grant a $\beta^2$ factor alongside the $n$ or $n+1$ factor. We can now write the Hamiltonian in terms of the ladder operators:
$$\hat H = E \hat a \hat a^\dagger + V\left(\hat a + \hat a^\dagger\right)$$
Let us now define new operators:
$$\hat a = \hat b + \alpha$$ $$\hat a^\dagger = \hat b^\dagger + \alpha^*$$
We can rewrite our Hamiltonian in terms of these new operators:
$$\hat H = E\left(\hat b + \alpha\right)\left(\hat b^\dagger + \alpha^*\right) + V\left(\hat b + \alpha + \hat b^\dagger + \alpha^*\right)$$
$$= E\left(\hat b \hat b^\dagger + \hat b \alpha^* + \hat b^\dagger \alpha + \alpha \alpha^*\right) + V\left(\hat b + \hat b^\dagger\right) + V\left(\alpha + \alpha^*\right)$$
We want to find the value of $\alpha$ such that first order terms involving the raising and lowering operators disappear. We can do this by setting the sum of the first order terms to $0$:
$$E\hat b \alpha^* + V\hat b = 0$$
$$E\hat b^\dagger \alpha + V\hat b^\dagger = 0$$
$$\alpha = -\frac{V}{E}$$
$$\alpha^* = -\frac{V}{E}$$
We can now rewrite our Hamiltonian in terms of the new operators:
$$\hat H = E\hat b \hat b^\dagger + \frac{V^2}{E} - \frac{2V^2}{E} = E\hat b \hat b^\dagger - \frac{V^2}{E}$$
We can now find the eigenvalues of this Hamiltonian:
$$\boxed{\hat H |n\rangle = E\hat b \hat b^\dagger |n\rangle - \frac{V^2}{E}|n\rangle = E\beta^2\left(n + 1\right)|n\rangle - \frac{V^2}{E}|n\rangle = \left(E\beta^2\left(n+1\right) - \frac{V^2}{E}\right)|n\rangle}$$
\end{solution}
\begin{problem}
    
    
    (a) Calculate the transmission probability for a particle of mass $m$ incident on a potential
    
    $
    V(x)=V_{0}[\delta(x+a)+\delta(x-a)]
    $
    
    (b) The reflected $\delta_{-}$ and transmitted $\delta_{+}$ phase shifts are phase shifts with respect to plane waves. Calculate $\delta_{-}$ and $\delta_{+}$.
    
    (c) Discuss the possibility of bound states.
    
    
\end{problem}
\begin{solution}\\
    a.\\ 
    We can first assume the forms of the wavefunctions in each region:
    $$\begin{cases}
        \psi_I(x) = Ae^{-ikx} + Be^{ikx} \text{ for } x < -a\\
        \psi_{II}(x) = Ce^{-ikx} + De^{ikx} \text{ for } -a < x < a\\
        \psi_{III}(x) = Fe^{ikx} \text{ for } x > a
    \end{cases}
    $$
    We then case find two equations from continuous boundary conditions.
    $$A e^{i a k} + B e^{- i a k} = C e^{i a k} + D e^{- i a k}$$
    $$C e^{- i a k} + D e^{i a k} = F e^{i a k}$$
    Next we can find a relationship between the derivatives of our wavefunctions and the potential by integrating the Schrödinger equation:
    $$\int_{-\left(\epsilon - a\right)}^{\epsilon - a} \left(-\frac{\hbar^2}{2m}\frac{d^2\psi}{dx^2} + V_0\delta(x+a)\psi\right)dx = \int_{-\left(\epsilon - a\right)}^{\epsilon - a} E\psi dx$$
    We take the limit of this expression as $\epsilon \rightarrow 0$:
    $$\lim_{\epsilon \rightarrow 0} \int_{-\left(\epsilon - a\right)}^{\epsilon - a} \left(-\frac{\hbar^2}{2m}\frac{d^2\psi}{dx^2} + V_0\delta(x+a)\psi\right)dx = \lim_{\epsilon \rightarrow 0} \int_{-\left(\epsilon - a\right)}^{\epsilon - a} E\psi dx$$
    The right side is $0$ since the wavefunction is continuous at $x = - a$. We can then integrate the left side:
    $$\lim_{\epsilon \rightarrow 0} \int_{-\left(\epsilon - a\right)}^{\epsilon - a} \left(-\frac{\hbar^2}{2m}\frac{d^2\psi}{dx^2} + V_0\delta(x+a)\psi\right)dx = -\frac{\hbar^2}{2m}\left(\Delta \psi'\left(a\right)\right) + V_0\psi\left(a\right) = 0$$
    Where $\Delta \psi'(a)$ is the difference of the derivative  of the wavefunction from the right side of $-a$ to the left side of $-a$ We can then find the derivative of the wavefunction at $x = -a$ to find the relationship between the derivatives of the wavefunction and the potential:
    $$i F k e^{i a k} + i k \left(C e^{- i a k} - D e^{i a k}\right) = \frac{2 F V_{0} m e^{i a k}}{\hbar^{2}}$$
    We can repeat this process for $x = a$:
    $$i A k e^{i a k} - i B k e^{- i a k} - i C k e^{i a k} + i D k e^{- i a k} = \frac{2 V_{0} m \left(A e^{i a k} + B e^{- i a k}\right)}{\hbar^{2}}$$
    We can then solve each of these in terms of $B$ as we have $4$ equations and $5$ unknowns, and $B$ is the coefficient of the rightward incident wave.
    $$A=\frac{B V_{0} m \left(- V_{0} m e^{4 i a k} + V_{0} m - i \hbar^{2} k e^{4 i a k} - i \hbar^{2} k\right) e^{- 2 i a k}}{V_{0}^{2} m^{2} e^{4 i a k} - V_{0}^{2} m^{2} + 2 i V_{0} \hbar^{2} k m + \hbar^{4} k^{2}}$$
    $$C=- \frac{i B V_{0} \hbar^{2} k m e^{2 i a k}}{V_{0}^{2} m^{2} e^{4 i a k} - V_{0}^{2} m^{2} + 2 i V_{0} \hbar^{2} k m + \hbar^{4} k^{2}}$$
    $$D = \frac{B \hbar^{2} k \left(i V_{0} m + \hbar^{2} k\right)}{V_{0}^{2} m^{2} e^{4 i a k} - V_{0}^{2} m^{2} + 2 i V_{0} \hbar^{2} k m + \hbar^{4} k^{2}}$$
    $$F = \frac{B \hbar^{4} k^{2}}{V_{0}^{2} m^{2} e^{4 i a k} - V_{0}^{2} m^{2} + 2 i V_{0} \hbar^{2} k m + \hbar^{4} k^{2}}$$
    We can then find the transmission coefficient by finding the ratio of the transmitted wave to the incident wave:
    $$\mathcal{T} = \frac{F}{B} = \frac{\hbar^{4} k^{2}}{V_{0}^{2} m^{2} e^{4 i a k} - V_{0}^{2} m^{2} + 2 i V_{0} \hbar^{2} k m + \hbar^{4} k^{2}}$$ 
    In the limit where $a \to 0$:
    $$\mathcal{T} = \frac{1}{1+\frac{2 i V_{0} m}{\hbar^{2} k}}$$
    This is what we expect for a converging central delta function potential.
    Now to find the transmission probability we take the magnitude squared of the coefficient:
    $$T = $$$$\frac{\hbar^{8} k^{4}}{- 2 V_{0}^{4} m^{4} \cos{\left(4 a k \right)} + 2 V_{0}^{4} m^{4} + 4 V_{0}^{3} \hbar^{2} k m^{3} \sin{\left(4 a k \right)} + 2 V_{0}^{2} \hbar^{4} k^{2} m^{2} \cos{\left(4 a k \right)} + 2 V_{0}^{2} \hbar^{4} k^{2} m^{2} + \hbar^{8} k^{4}}$$ 
    This expression is disgusting although asking for the imaginary part of this expression in SymPy yields $0$ meaning this is a real quantity that represents the transmission probability.
    $$R = 1 - T = 1 -$$$$\frac{\hbar^{8} k^{4}}{- 2 V_{0}^{4} m^{4} \cos{\left(4 a k \right)} + 2 V_{0}^{4} m^{4} + 4 V_{0}^{3} \hbar^{2} k m^{3} \sin{\left(4 a k \right)} + 2 V_{0}^{2} \hbar^{4} k^{2} m^{2} \cos{\left(4 a k \right)} + 2 V_{0}^{2} \hbar^{4} k^{2} m^{2} + \hbar^{8} k^{4}}$$
    b.\\ 
    Now to find the phase shifts we can first find the reflection and transmission coefficients:
    This can be done by finding the real and imaginary components $\mathcal{T}$ and $\mathcal{R}$ and take the inverse tangent of their ratios:
    $$\Im{\mathcal{T}} = \frac{\hbar^{4} k^{2} \left(- V_{0}^{2} m^{2} \sin{\left(4 a k \right)} - 2 V_{0} \hbar^{2} k m\right)}{\left(V_{0}^{2} m^{2} \sin{\left(4 a k \right)} + 2 V_{0} \hbar^{2} k m\right)^{2} + \left(V_{0}^{2} m^{2} \cos{\left(4 a k \right)} - V_{0}^{2} m^{2} + \hbar^{4} k^{2}\right)^{2}}$$
    $$\Re{\mathcal{T}} = \frac{\hbar^{4} k^{2} \left(V_{0}^{2} m^{2} \cos{\left(4 a k \right)} - V_{0}^{2} m^{2} + \hbar^{4} k^{2}\right)}{\left(V_{0}^{2} m^{2} \sin{\left(4 a k \right)} + 2 V_{0} \hbar^{2} k m\right)^{2} + \left(V_{0}^{2} m^{2} \cos{\left(4 a k \right)} - V_{0}^{2} m^{2} + \hbar^{4} k^{2}\right)^{2}}$$
    $$\frac{\Im{\mathcal{T}}}{\Re{\mathcal{T}}} = \frac{- V_{0}^{2} m^{2} \sin{\left(4 a k \right)} - 2 V_{0} \hbar^{2} k m}{V_{0}^{2} m^{2} \cos{\left(4 a k \right)} - V_{0}^{2} m^{2} + \hbar^{4} k^{2}}$$
    $$\delta_{+} = \arctan{\left(\frac{\Im{\mathcal{T}}}{\Re{\mathcal{T}}}\right)} = \arctan\left(\frac{- V_{0}^{2} m^{2} \sin{\left(4 a k \right)} - 2 V_{0} \hbar^{2} k m}{V_{0}^{2} m^{2} \cos{\left(4 a k \right)} - V_{0}^{2} m^{2} + \hbar^{4} k^{2}}\right)$$
    Solving for the reflection coefficient:
    $$\mathcal{R} = \frac{A}{B} = \frac{V_{0} m \left(- V_{0} m e^{4 i a k} + V_{0} m - i \hbar^{2} k e^{4 i a k} - i \hbar^{2} k\right) e^{- 2 i a k}}{V_{0}^{2} m^{2} e^{4 i a k} - V_{0}^{2} m^{2} + 2 i V_{0} \hbar^{2} k m + \hbar^{4} k^{2}}$$
    \begin{center}$ \delta_- = \arctan\left(\frac{\Im{R}}{\Re{R}}\right) =
    \small \arctan\left(\frac{\left(V_{0} m \sin{\left(2 a k \right)} + \hbar^{2} k \cos{\left(2 a k \right)}\right) \left(8 V_{0}^{2} m^{2} \sin^{4}{\left(a k \right)} - 8 V_{0}^{2} m^{2} \sin^{2}{\left(a k \right)} + \hbar^{4} k^{2}\right)}{- 2 V_{0}^{3} m^{3} \cos^{3}{\left(2 a k \right)} + 2 V_{0}^{3} m^{3} \cos{\left(2 a k \right)} + 2 V_{0}^{2} \hbar^{2} k m^{2} \sin{\left(2 a k \right)} \cos^{2}{\left(2 a k \right)} + 2 V_{0}^{2} \hbar^{2} k m^{2} \sin{\left(2 a k \right)} + 2 V_{0} \hbar^{4} k^{2} m \cos{\left(2 a k \right)}}\right)$
    $$\lim_{a\to 0} \delta_- = \frac{\hbar^{2} k}{2 V_{0} m}$$
    $$\lim_{a\to 0} \delta_+ = -\frac{2 V_{0} m}{\hbar^{2} k}$$
    \end{center}
    Both of these break down to the correct phases at $a=0$.\\ 
    c.\\ 
    If we ensure that the wavefunction is equal to zero at $x=\pm a$ we can construct a bounded state:
    This implies the following:
    $$F=0$$
    $$-\frac{B}{A} =-\frac{D}{C} = e^{-2 i a k}$$
    $$\frac{D}{C} = e^{2 i a k} = e^{-2 i a k}$$
    This implies that $$2ka=n\pi$$
    $$k=\frac{n\pi}{2a}$$
    We can then substitute this into our energy equation:
    $$k = \sqrt{\frac{2mE}{\hbar^2}}$$
    $$E = \frac{n^2\pi^2\hbar^2}{8ma^2}$$
    This is exactly what we would expect for a bounded state as it is the energy spectrum for a particle in an infinite square well of size $2a$.


\end{solution}\begin{problem}
Consider a particle of mass $m$ moving in one-dimensional potential

$
V(x)=-\frac{\hbar^{2}}{m} \frac{1}{\cosh ^{2} x}
$

(a) Show that

$
\psi(x)=(\tanh x+C) \exp (i k x)
$

solves the problem for for particular value of $C$. Determine that $C$ and corresponding energy values. From the asymptotic behavior find the reflection and transmission coefficients.

(b) Show that

$
\phi(x)=\frac{1}{\cosh x}
$

satisfies the Schrödinger equation. Find the energy and determine if this state is the ground state or an excited state.
\end{problem}
\begin{solution}\\
    a.\\
    We can first show that the given wavefunction solves the Schrödinger equation:
    $$\psi(x) = (\tanh \alpha x + C)\exp(ikx)$$
    $$\frac{-\hbar^2}{2m}\frac{d^2\psi(x)}{dx^2} - \frac{\hbar^2}{m}\frac{\alpha^2 }{\cosh^2 {\alpha x}}\psi(x) = E\psi(x)$$
    $$\frac{d^2 \psi\left(x\right)}{d x^2} + \frac{2\alpha^2 }{\cosh^2 {\alpha x}} \psi\left(x\right) = -\frac{2mE}{\hbar^2}\psi\left(x\right)$$
    $$\frac{d^2}{dx^2}\left((\tanh {\alpha x} + C)\exp(ikx)\right) + \frac{2\alpha^2 }{\cosh^2 {\alpha x}}(\tanh {\alpha x} + C)\exp(ikx) $$$$= -\frac{2mE}{\hbar^2}(\tanh {\alpha x} + C)\exp(ikx)$$
    $$\frac{d}{dx}\left(\left(\frac{\alpha}{\cosh^2 {\alpha x}} + ik\left(\tanh {\alpha x} + C\right)\right)\exp\left(ikx\right)\right) + \frac{2\alpha }{\cosh^2 {\alpha x}}(\tanh {\alpha x} + C)\exp(ikx) $$$$= -\frac{2mE}{\hbar^2}(\tanh {\alpha x} + C)\exp(ikx)$$
    $$ik\left(\frac{\alpha}{\cosh^2 {\alpha x}} + ik\left(\tanh {\alpha x} + C\right)\right)\exp\left(ikx\right)  + \frac{2\alpha^2}{\cosh^2 {\alpha x}}(\tanh {\alpha x} + C)\exp(ikx) $$$$+ \left(-\frac{2\alpha^2\sinh {\alpha x}}{\cosh^3 {\alpha x}} +\frac{ik\alpha }{\cosh^2 {\alpha x}}\right)\exp\left(ikx\right)= -\frac{2mE}{\hbar^2}(\tanh {\alpha x} + C)\exp(ikx)$$
    Simplifying:
    $$-k^2\left(\tanh x + C\right)\exp\left(ikx\right) + \frac{2ik\alpha}{\cosh^2 {\alpha x}}\exp\left(ikx\right)  + \frac{2C\alpha^2}{\cosh^2 {\alpha x}}\exp(ikx) $$$$= -\frac{2mE}{\hbar^2}(\tanh {\alpha x} + C)\exp(ikx)$$
    With $C=-\frac{ik}{\alpha}$ we have:
    $$-k^2\left(\tanh {\alpha x} - \frac{ik}{\alpha}\right)\exp\left(ikx\right) = -\frac{2mE}{\hbar^2}\left(\tanh {\alpha x} - \frac{ik}{\alpha}\right)\exp(ikx)$$
    $$-k^2 \psi\left(x\right) = -\frac{2mE}{\hbar^2}\psi\left(x\right)$$
    This solves our eigenvalue problem with an eigenvalue $E = \frac{\hbar^2 k^2}{2m}$. Which is exactly what we would expect.
    We can now find our reflection and transmission coefficients by asymptotically analyzing the wavefunction:
    $$\psi(x) = (\tanh \alpha x - \frac{ik}{\alpha})\exp(ikx)$$
    $$\left|\psi\left(-\infty\right)\right| = \left|-1 - \frac{ik}{\alpha}\right|  = \left|1+ \frac{ik}{\alpha}\right|$$
    $$\left|\psi\left(\infty\right)\right| = \left|1 - \frac{ik}{\alpha}\right|$$
    $$\boxed{T = \frac{\left|1 - \frac{ik}{\alpha}\right|^2}{\left|1+ \frac{ik}{\alpha}\right|^2} = \frac{1 + \frac{k^2}{\alpha^2}}{1  + \frac{k^2}{\alpha^2}} = 1}$$
    $$\boxed{R = 1 - T = 0}$$
    b.\\
    We can now show that the given wavefunction solves the Schrödinger equation:
    $$\phi(x) = \frac{1}{\cosh {\alpha x}}$$
    $$\frac{d \phi\left(x\right)}{d x} = -\frac{\alpha\sinh {\alpha x}}{\cosh^2 {\alpha x}} = -\frac{\alpha\tanh {\alpha x}}{\cosh {\alpha x}}$$
    $$\frac{d^2 \phi\left(x\right)}{d x^2} = -\frac{\alpha^2}{\cosh^3{\alpha x}} + \frac{\alpha^2 \tanh{\alpha x}\sinh{\alpha x}}{\cosh^2 {\alpha x}} = -\frac{\alpha^2}{\cosh^3{\alpha x}} + \frac{\alpha^2 \tanh^2 {\alpha x}}{\cosh {\alpha x}}$$
    $$\frac{-\hbar^2}{2m}\frac{d^2\phi(x)}{dx^2} - \frac{\hbar^2}{m}\frac{\alpha^2}{\cosh^2 {\alpha x}}\phi(x) = E\phi(x)$$
    Once we compute the derivative and expand terms:
    $$- \frac{2 \alpha^{2} \sinh^{2}{\left(\alpha x \right)}}{\cosh^{3}{\left(\alpha x \right)}} + \frac{\alpha^{2}}{\cosh{\left(\alpha x \right)}} - \frac{2 \alpha^{2}}{\cosh^{3}{\left(\alpha x \right)}} = \frac{2 E m}{\hbar^{2} \cosh{\left(\alpha x \right)}}$$
    $$- \frac{\alpha^{2}}{\cosh{\left(\alpha x \right)}} =  \frac{2 E m}{\hbar^{2} \cosh{\left(\alpha x \right)}}$$
    We see our Schrödinger equation is satisfied with an eigenvalue: $$\boxed{E = -\frac{\hbar^2 \alpha^2}{2m}}$$
    This is a negative energy state so it is a bound state since $\frac{1}{\cosh^2(\alpha x)}$ is always positive $\forall x \in \mathbb{R}$. Additionally, we see that $\psi\left(x\right) = \frac{1}{\cosh\left(\alpha x\right)}$ has no nodes and is symmetric about the origin. This means that this is the ground state.
\end{solution}
\begin{problem}
    
    
    Show that $\hat{a}^{\dagger}$ do not have square integrable eigenstates.
    
    
\end{problem}
\begin{solution}\\
    We can proceed through this problem via contradiction. We will assume that $\hat a^\dagger$ has square integrable eigenstates. We can write the eigenvalue equation for $\hat a^\dagger$:
    $$\hat a^\dagger \psi\left(x\right) = \lambda \psi\left(x\right)$$
    We can now write $\hat a^\dagger$ in terms of the momentum and position operators:
    $$\hat a^\dagger = \frac{1}{\sqrt{2\hbar m \omega}}\left(m\omega x - i\hat p\right)$$
    We can now write the eigenvalue equation in terms of the momentum and position operators:
    $$\frac{1}{\sqrt{2\hbar m \omega}}\left(m\omega x - i\hat p\right) \psi\left(x\right) = \lambda \psi\left(x\right)$$
    Now writing out the momentum operator in the position basis:
    $$\frac{1}{\sqrt{2\hbar m \omega}}\left(m\omega x - i\left(-i\hbar\frac{d}{dx}\right)\right) \psi\left(x\right) = \lambda \psi\left(x\right)$$
    $$\frac{1}{\sqrt{2\hbar m \omega}}\left(m\omega x - \hbar\frac{d}{dx}\right) \psi\left(x\right) = \lambda \psi\left(x\right)$$
    We can now write the differential equation:
    $$\frac{1}{\sqrt{2\hbar m \omega}}\left(m\omega x\psi\left(x\right) - \hbar\frac{d\psi\left(x\right)}{dx}\right) = \lambda \psi\left(x\right)$$
    $$\lambda \sqrt{2\hbar m \omega}\psi\left(x\right) = m\omega x\psi\left(x\right) - \hbar\frac{d\psi\left(x\right)}{dx}$$
    $$-\frac{1}{\hbar}\left(\lambda\sqrt{2\hbar m\omega} - m\omega x\right)\psi\left(x\right) = \frac{d\psi\left(x\right)}{dx}$$
    The function that solves this differential equation takes the following form:
    $$\psi{\left(x \right)} = Ae^{\frac{x \left(- \sqrt{2} \lambda \sqrt{\hbar m \omega} + \frac{m \omega x}{2}\right)}{\hbar}}$$
    $$= \boxed{Ae^{\frac{m\omega x^2}{2\hbar} - \frac{\lambda\sqrt{2}\sqrt{\hbar m \omega}x}{\hbar}}}$$
    We can see that the argument of our exponential is a quadratic function with a positive coefficient on $x^2$, and therefore the function will not be square integrable. Therefore we have shown that $\hat a^\dagger$ does not have square integrable eigenstates.

\end{solution}
\end{document}