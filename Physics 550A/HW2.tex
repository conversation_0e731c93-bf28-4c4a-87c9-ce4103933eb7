\documentclass[12pt]{article}%
\usepackage{amsfonts}
\usepackage{fancyhdr}
\usepackage{listings}
\usepackage{comment}
\usepackage[a4paper, top=2.5cm, bottom=2.5cm, left=2.2cm, right=2.2cm]{geometry}
\def\shrinkage{2.1mu}
\def\vecsign{\mathchar"017E}
\def\dvecsign{\smash{\stackon[-1.95pt]{\mkern-\shrinkage\vecsign}{\rotatebox{180}{$\mkern-\shrinkage\vecsign$}}}}
\def\dvec#1{\def\useanchorwidth{T}\stackon[-4.2pt]{#1}{\,\dvecsign}}
\usepackage{stackengine}
\stackMath
\usepackage{times}
\usepackage{cancel}
\usepackage{physics}
\usepackage{amsmath}
\usepackage{tikz}
\usepackage{changepage}
\usetikzlibrary{shapes.misc}
\usepackage{amssymb}
\usepackage{graphicx}%
\setcounter{MaxMatrixCols}{30}
\newtheorem{theorem}{Theorem}
\newtheorem{acknowledgement}[theorem]{Acknowledgement}
\newtheorem{algorithm}[theorem]{Algorithm}
\newtheorem{axiom}{Axiom}
\newtheorem{case}[theorem]{Case}
\newtheorem{claim}[theorem]{Claim}
\newtheorem{conclusion}[theorem]{Conclusion}
\newtheorem{condition}[theorem]{Condition}
\newtheorem{conjecture}[theorem]{Conjecture}
\newtheorem{corollary}[theorem]{Corollary}
\newtheorem{criterion}[theorem]{Criterion}
\newtheorem{definition}[theorem]{Definition}
\newtheorem{example}[theorem]{Example}
\newtheorem{exercise}[theorem]{Exercise}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{notation}[theorem]{Notation}
\newtheorem{problem}[theorem]{Problem}
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{remark}[theorem]{Remark}
\newtheorem{summary}[theorem]{Summary}
\newenvironment{proof}[1][Proof]{\textbf{#1.} }{\ \rule{0.5em}{0.5em}}
\newenvironment{solution}[1][Solution]{\textbf{#1}}{\ \rule{0.5em}{0.5em}}
\newcommand{\Q}{\mathbb{Q}}
\newcommand{\R}{\mathbb{R}}
\newcommand{\C}{\mathbb{C}}
\newcommand{\Z}{\mathbb{Z}}
\newcommand{\N}{\mathbb{N}}
\newcommand{\Lagr}{\mathcal{L}}
\newcommand{\lagr}{\mathcal{L}_0}
\newcommand{\lb}{\left\{}
\newcommand{\rb}{\right\}}
\newcommand{\set}[1]{\left\{#1\right\}}
\newcommand{\pr}[1]{\left(\{#1\right\})}
\newcommand{\dbar}[1]{\bar{\bar{#1}}}
\newcommand{\tbar}[1]{\bar{\bar{\bar{#1}}}}
\title{Homework 2 Physics 550A}
\author{Tyler Hadsell}
\begin{document}
\maketitle


\begin{problem}
Consider the operator
\[
\hat{A}=\left(\begin{array}{cc}
a_{3} & a_{1}+i a_{2} \\
a_{1}-i a_{2} & -a_{3}
\end{array}\right)
\]
with $a_{i}$ real numbers. Find the spectral resolution of $\hat{A}$ (express $\hat{A}$ in terms of eigenprojections).
\end{problem}
\begin{solution}\\
We can first determine the eigenvalues of $\hat A$ and their corresponding eigenvectors:
$$\det\left(\hat A - \lambda I \right)= 0$$
$$(\lambda-a_3)(\lambda+a_3)-(a_1+ia_2)(a_1-ia_2)=0$$
$$\lambda^2-a_3^2-(a_1^2+a_2^2)=0$$
The eigenvalues are $\lambda_\pm=\pm\sqrt{a_3^2+a_1^2+a_2^2}$.
Now to find the corresponding eigenvectors we solve the following matrix-vector equation:
$$\hat A\bar v_\pm=\lambda_\pm \bar v_\pm$$
First we'll start with our first eigenvalue:
$$\hat A\bar v_+ = \bar v_+\sqrt{a_3^2+a_1^2+a_2^2}$$
This gives us the following equations:
$$v_{1} \left(a_{3} - \sqrt{a_{1}^{2} + a_{2}^{2} + a_{3}^{2}}\right) + v_{2} \left(a_{1} + i a_{2}\right) = 0$$
$$v_{1} \left(a_{1} - i a_{2}\right) + v_{2} \left(- a_{3} - \sqrt{a_{1}^{2} + a_{2}^{2} + a_{3}^{2}}\right) = 0$$
These are not linearly independent so we will solve in terms of $v_2$:
$$\bar v_+ = \left[\begin{matrix}- a_{1} - i a_{2}\\a_{3} - \sqrt{a_{1}^{2} + a_{2}^{2} + a_{3}^{2}}\end{matrix}\right] $$
Similarly we find the other eigenvector:
$$\bar v_- = \left[\begin{matrix}a_{3} - \sqrt{a_{1}^{2} + a_{2}^{2} + a_{3}^{2}}\\a_{1} - i a_{2}\end{matrix}\right]$$
We can solve for the normalization constants $v_2$ using the orthonormality condition of the eigenvectors of Hermitian matrices:
$$\bar v_+^\dagger \bar v_+=\bar v_-^\dagger \bar v_-=1$$
$$\bar v_+^\dagger \bar v_-= \bar v_-^\dagger\bar v_+=0$$
We find our normalized vectors as the following:
$$\bar v_+ = \frac{1}{\sqrt{a_{1}^{2} + a_{2}^{2} + \left(a_{3} - \sqrt{a_{1}^{2} + a_{2}^{2} + a_{3}^{2}}\right)^{2}}} \left[\begin{matrix}- a_{1} - i a_{2}\\a_{3} - \sqrt{a_{1}^{2} + a_{2}^{2} + a_{3}^{2}}\end{matrix}\right] $$
$$\bar v_- = \frac{1}{\sqrt{a_{1}^{2} + a_{2}^{2} + \left(a_{3} + \sqrt{a_{1}^{2} + a_{2}^{2} + a_{3}^{2}}\right)^{2}}} \left[\begin{matrix}a_{3} + \sqrt{a_{1}^{2} + a_{2}^{2} + a_{3}^{2}}\\a_{1} - i a_{2}\end{matrix}\right]$$
The spectral resolution of $\hat{A}$ is then given by:
$$\hat A = \lambda_+ \ket{v_+}\bra{v_+} + \lambda_- \ket{v_-}\bra{v_-}$$
$$=\frac{\lambda_+}{a_{1}^{2} + a_{2}^{2} + \left(a_{3} - \lambda_{+}\right)^{2}
}\left[\begin{matrix}\left(- a_{1} - i a_{2}\right) \left(- a_{1} + i a_{2}\right) & \left(- a_{1} - i a_{2}\right) \left(a_{3} - \lambda_{+}\right)\\\left(- a_{1} + i a_{2}\right) \left(a_{3} - \lambda_{+}\right) & \left(a_{3} - \lambda_{+}\right)^{2}\end{matrix}\right]
$$$$+\frac{\lambda_-}{a_{1}^{2} + a_{2}^{2} + \left(a_{3} - \lambda_{-}\right)^{2}}\left[\begin{matrix}\left(a_{3} - \lambda_{-}\right)^{2} & \left(a_{1} + i a_{2}\right) \left(a_{3} - \lambda_{-}\right)\\\left(a_{1} - i a_{2}\right) \left(a_{3} - \lambda_{-}\right) & \left(a_{1} - i a_{2}\right) \left(a_{1} + i a_{2}\right)\end{matrix}\right]
$$ Where $\lambda_\pm = \pm\sqrt{a_1^2 + a_2^2 + a_3^2}$\\Next we change the basis to the eigenbasis of $\hat{A}$:
$$\hat A = \sum_i \lambda_i \ket{v_i}\bra{v_i} = \sqrt{a_{1}^{2} + a_{2}^{2} + a_{3}^{2}} \begin{pmatrix} 1 & 0\\ 0 & 0 \end{pmatrix} - \sqrt{a_{1}^{2} + a_{2}^{2} + a_{3}^{2}} \begin{pmatrix} 0 & 0\\ 0 & 1 \end{pmatrix}$$
$$= \begin{pmatrix} \sqrt{a_{1}^{2} + a_{2}^{2} + a_{3}^{2}} & 0\\ 0 & -\sqrt{a_{1}^{2} + a_{2}^{2} + a_{3}^{2}} \end{pmatrix}$$
This gives us the spectral resoluton of $\hat{A}$ in the eigenbasis.



\end{solution}

\begin{problem}
Consider a basis $\left\{u_{n} ; n=0,1, \ldots\right\}$ and an operator $\hat{a}$ with the following properties

$
\hat{a}\left|u_{n}\right\rangle=\sqrt{n}\left|u_{n-1}\right\rangle
$

Determine $\hat{a}^{\dagger}$ and calculate $\left[\hat{a}, \hat{a}^{\dagger}\right]$.
\end{problem}
\begin{solution}\\ 
    First we will attempt to write out the operator in index notation:
    $$\hat a \ket {u_n} = \sqrt{n} \ket {u_{n-1}}$$
    $$\bra {u_m} \hat a \ket {u_n} = a_{mn}=\sqrt{n} \delta_{m,n-1}$$
    Where $\delta_{m,n}$ is the Kronecker delta function.
    The adjoint operator $\hat a^{\dagger}$ is then defined such that:
    $$\bra {u_n} \hat a^\dagger \ket{u_m} = a^\dagger_{nm}=\sqrt{n}\delta_{n-1,m}= \sqrt{n}\delta_{n,m+1}$$
    We also enforce a condition on the original operator that $\bra{u_m}\hat a\ket{u_n}=0$ if $n\leq 1$.
    We can write $\hat a$ as a matrix in the basis $\ket{u_n}$:
    $$\hat a=\begin{pmatrix} 0 & \sqrt{2} & 0 & 0 & \dots \\ 0 & 0 & \sqrt{3} & 0 & \dots \\ 0 & 0 & 0 & \sqrt{4} & \dots \\ 0 & 0 & 0 & 0 & \dots \\ \vdots & \vdots & \vdots & \vdots & \ddots \end{pmatrix}$$
    Similarly, we can write $\hat a^\dagger$ as:
    $$\hat a^\dagger=\begin{pmatrix} 0 & 0 & 0 & 0 & \dots \\ \sqrt{2} & 0 & 0 & 0 & \dots \\ 0 & \sqrt{3} & 0 & 0 & \dots \\ 0 & 0 & \sqrt{4} & 0 & \dots \\ \vdots & \vdots & \vdots & \vdots & \ddots \end{pmatrix}$$
    To calculate $[\hat a, \hat a^\dagger]$, we take the commutator with a test ket:
    $$[\hat a, \hat a^\dagger]\ket{u_n} = \hat a \hat a^\dagger \ket{u_n} - \hat a^\dagger \hat a \ket{u_n}$$
    Using the index notation expressions for $\hat a$ and $\hat a^\dagger$, we can obtain the following:
    $$\hat a^\dagger \ket {u_n} = \sum_{m}\ket {u_m}\bra{u_m} \hat a^\dagger \ket {u_n} = \sum_{m} \sqrt{m} \delta_{m,n+1} \ket{u_m}$$
    $$\hat a \hat a^\dagger \ket {u_n} = \sum_{m,\ell} \sqrt{m}\delta_{m,n+1}\ket {u_\ell} \bra{u_\ell} \hat a \ket {u_m} = \sum_{m,\ell} m\delta_{m,n+1}\delta_{\ell, m-1}\ket{u_\ell} = n\ket{u_n}$$
    Similarly,
    $$\hat a^\dagger \hat a \ket {u_n} = \sum_{m,\ell}   m\delta_{\ell,m+1}\delta_{m,n-1}\ket{u_\ell} = (n-1)\ket{u_n}$$
    Therefore,
    $$[\hat a, \hat a^\dagger]\ket{u_n} = (n-(n-1))\ket{u_n} = \ket{u_n}$$
    So the commutator is simply the identity operator.
    $$[\hat a, \hat a^\dagger]=\boldmath{1}$$

\end{solution}

\begin{problem}
Show that if

$
\left[\hat{x}, \hat{p}_{x}\right]=i \hbar
$

then there exists an unitary operator $S$ such that for a given real function $f(x)$ we have

$
\hat{S} \hat{p}_{x} \hat{S}^{-1}=\hat{p}_{x}+f(x)
$
\end{problem}
\begin{solution}
We begin with the following operator:
$$S = e^{\frac{-i}{\hbar}g(x)}$$
$$S^\dagger = e^{\frac{i}{\hbar}g(x)} = S^{-1}$$
Then we calculate:
$$\hat{S}\hat{p}_x\hat{S}^{-1} = e^{\frac{-i}{\hbar}f(x)}\hat{p}_xe^{\frac{i}{\hbar}f(x)}$$
We then use the operator expansion:
$$e^{\hat A} \hat B e^ {-\hat A} = \hat B + [\hat A, \hat B] + \frac{1}{2!}[\hat A, [\hat A, \hat B]] + \frac{1}{3!}[\hat A, [\hat A, [\hat A, \hat B]]]+ \dots$$
Setting $\hat A = \frac{-i}{\hbar}g(x)$ and $\hat B = \hat p_x$, we have:
$$[\hat A, \hat B] = \frac{-i}{\hbar}[g(x), \hat p_x] = \frac{dg(x)}{dx}$$
We can then see that all higher order terms will vanish since:
$$[g(x), [g(x), \hat p_x]] = g(x)\frac{d g}{d x} - \frac{d g}{d x}g(x) = 0$$
Therefore, the operator expansion reduces to:
$$\hat{S}\hat{p}_x\hat{S}^{-1} = \hat{p}_x + \frac{-i}{\hbar}[g(x), \hat p_x] = \hat{p}_x + \frac{dg(x)}{dx}$$
If we then state that $f(x) = \frac{dg(x)}{dx}$, we satisfy the condition that $\hat{S} \hat{p}_{x} \hat{S}^{-1}=\hat{p}_{x}+f(x)$.
Additionally we see our operator $S$ takes the form:
$$S = e^{-\frac{i}{\hbar}\int f(x)\,dx}$$ Which is precisely the exponential of the action integral.
\end{solution}

\begin{problem}
Show that in stationary states corresponding to the discrete spectrum of the Hamiltonian $\hat{H}=\boldsymbol{p}^{2} / 2 m+V(\boldsymbol{r})$, the mean value of the momentum vanishes, i.e. $\langle\boldsymbol{p}\rangle=0$.
\end{problem}
\begin{solution}\\
We can first note what a stationary state implies: it is an eigenstate of the Hamiltonian $\hat{H}$, meaning $\hat{H}\ket{\psi}=E\ket{\psi}$. This also implies that the state is not an explicit function of time outside of the usual exponential time dependence $e^{-iEt/\hbar}$.
Next we can utilize Ehrenfest's theorem, which states for any operator $\hat{A}$:
$$ \frac{d}{dt}\langle\hat{A}\rangle = \frac{1}{i\hbar}\langle[\hat{H},\hat{A}]\rangle + \frac{\partial}{\partial t}\langle\hat{A}\rangle$$
For the position operator $\hat{x}$ this gives the classical equation of motion:
$$\frac{d \left<\hat x\right>}{d t} = \frac{i}{\hbar} \left<\left[\hat H, \hat x\right]\right> = \frac{i}{\hbar}\left<\left(\frac{\hat p^2}{2m} \hat x - \hat x \frac{\hat p^2}{2m}\right)\right>$$
$$=\frac{i}{\hbar}\left<\left(-\frac{\hbar^2}{2m} \frac{\partial^2 \left(x\psi\right)}{\partial x^2} + x\frac{\hbar^2}{2m} \frac{\partial^2 \psi}{\partial x^2}\right)\right>= -\frac{i\hbar}{2m}\left<\left(2\frac{\partial \psi}{\partial x} + x\frac{\partial^2 \psi}{\partial x^2} - x\frac{\partial^2 \psi}{\partial x^2}\right)\right>$$
$$=\frac{\left<\hat p\right>}{m}$$
We can rearrange this equation and find:
$$\left<\hat p\right>=m\frac{d\left<\hat x\right>}{dt}$$
Since the state is stationary, $\frac{d\left<\hat x\right>}{dt}=0$. Therefore, the mean value of the momentum operator $\hat p$ vanishes for stationary states of the Hamiltonian.
\end{solution}

\begin{problem}
Show that the mean value of $x$ in a state described by $\psi$,

$
\langle x\rangle=\int_{-\infty}^{\infty} d x \psi^{*}(x) x \psi(x)
$

is equal to the value of $a$ for which the expression

$
V(a)=\int_{-\infty}^{\infty} d x \psi^{*}(x+a) x^{2} \psi(x+a)
$

is a minimum, and that this minimum has the value $V_{\text {min }}=(\Delta x)^{2}=\left\langle x^{2}\right\rangle-\langle x\rangle^{2}$.
\end{problem}
\begin{solution}\\
We can begin this problem by utilizing the translation operator $\hat{T}(a)=e^{-ia\hat{p}/\hbar}$ which translates a wavefunction by $a$:
$$\hat{T}(a)\psi(x)=\psi(x-a)$$
Therefore we use $$\hat T(-a)\psi(x)=\psi(x+a)$$
We can then rewrite the expression for $V(a)$ using this translation operator:
$$V(a)=\int_{-\infty}^{\infty} d x \psi^{*}(x)\hat{T}^\dagger(a) x^2 \hat{T}(a) \psi(x)$$
We can use the following unitary operator expansion:
$$e^{\alpha\hat{p}} \hat x^2 e^{-\alpha\hat{p}} = \hat x^2 + \alpha[\hat{p},\hat{x}^2] + \frac{\alpha^2}{2!}[\hat{p},[\hat{p},\hat{x}^2]] + \dots$$
We notice that:
$$\left[\hat p, x\right] = -i\hbar$$
Therefore:
$$\left[p, x^2\right] = -2i\hbar x$$
$$\left[\hat p, \left[\hat p, x^2\right]\right] = \left[\hat p, -2i\hbar x\right] = -2\hbar^2$$
The higher order terms go to zero due to the last term being a constant. Therefore:
$$e^{\alpha\hat{p}} \hat x^2 e^{-\alpha\hat{p}} = \hat x^2 - 2i\alpha\hbar x - \alpha^2\hbar^2$$
Plugging in $\alpha = -ia/\hbar$, we get:
$$e^{-ia\hat{p}/\hbar} \hat x^2 e^{ia\hat{p}/\hbar} = \hat x^2 - 2a\hat{x} + a^2$$
Substituting back into the expression for $V(a)$, we find:
$$V(a)=\int_{-\infty}^{\infty} dx \ \psi^*(x)(\hat x^2 - 2a\hat{x} + a^2)\psi(x)$$
$$= \langle x^2 \rangle - 2a\langle x \rangle + a^2$$
We now want to minimize this function with respect to $a$. Taking the derivative and setting it equal to zero gives:
$$\frac{dV}{da} = -2\langle x \rangle + 2a = 0 \Rightarrow \left<x\right>=a$$
Plugging back into the expression for $V(a)$ gives the minimum value:
$$V_\text{min} = \langle x^2 \rangle - 2a\langle x \rangle + a^2 = \langle x^2 \rangle - \langle x \rangle^2 = (\Delta x)^2$$
\end{solution}

\begin{problem}
In a spherical polar coordinates $l_{z}$ is represented by operator $-i \hbar \partial / \partial \phi$. One would expect the commutation rule and the uncertainty relation

$
\left[\phi, l_{z}\right]=i \hbar \quad \text { and } \quad \Delta l_{z} \Delta \phi \geq \hbar / 2
$

Show that the first relation is not in general valid and the second one would contradict the Heisenberg uncertainty principle.
\end{problem}
\begin{solution}\\
    We can first notice that our $\phi$ has the following properties:
    $$\phi \in [0,2\pi)$$
    $$\psi\left(\phi\right) = \psi\left(\phi + 2\pi\right)$$
    Therefore $\phi$ is an angular coordinate, not a regular position coordinate. The commutation relation for the angular momentum operator $L_z$ and the angular coordinate $\phi$ is:
    $$\left[\hat \phi, \hat \ell_z\right]=-i\hbar\left(\hat \phi \frac{\partial }{\partial \phi} - \frac{\partial}{\partial \phi} \hat \phi\right)\psi\left(\phi\right)$$
    $$=-i\hbar\left(\hat \phi \frac{\partial \psi}{\partial \phi} - \frac{\partial }{\partial \phi} \hat \phi \psi\left(\phi\right)\right)$$
    If we follow our computations without any alterations we will obtain:
    $$\left[\hat \phi, \hat \ell_z\right]\psi\left(\phi\right) = i\hbar \psi\left(\phi\right)$$
    However, let us use our $2\pi$ periodicity condition:
    $$\psi\left(\phi+2\pi\right)=\psi\left(\phi\right)$$
    $$\left[\hat \phi, \hat \ell_z\right]\psi\left(\phi\right) = -i\hbar \left(\hat \phi \frac{\partial \psi}{\partial \phi} - \frac{\partial }{\partial \phi} \left(\phi + 2\pi\right)\psi\left(\phi + 2\pi\right)\right)$$
    $$= i\hbar \left(\psi\left(\phi\right) + 2\pi \frac{\partial \psi}{\partial \phi}\right)$$
    Hence:
    $$\left[\hat \phi, \hat \ell_z\right] = i\hbar \left(1 + 2\pi \frac{\partial}{\partial \phi}\right) \neq i\hbar$$
    For the next part we utilize the generalized uncertainty principle:
    $$\Delta A \Delta B \geq \frac{1}{2}\left|\left<[\hat A, \hat B]\right>\right|$$
    $$\Delta \phi \Delta \ell_z \geq \frac{1}{2}\left|\left<[\hat \phi, \hat \ell_z]\right>\right| = \frac{1}{2}\left|\left<\hat \phi \hat \ell_z - \hat \ell_z \hat \phi\right>\right|= \frac{1}{2}\left|\bra{\phi \psi}\ket{\ell_z \psi} -  \bra{\ell_z \psi}\ket{\phi \psi}\right|$$
    We can now write out the integrals:
    $$= \frac{\hbar}{2}\left|\int_0^{2\pi} -\phi \psi^* \frac{\partial \psi}{\partial \phi} - \frac{\partial \psi^*}{\partial \phi} \phi \psi\, d\phi\right| = \frac{\hbar}{2}\left|-\int_0^{2\pi} \phi\left( \psi^* \frac{\partial \psi}{\partial \phi} + \frac{\partial \psi^*}{\partial \phi} \psi \right)d\phi\right|$$
    $$=\frac{\hbar}{2}\left|-\int_0^{2\pi} \phi \frac{\partial}{\partial \phi}\left(\psi^* \psi\right)d\phi\right| = \frac{\hbar}{2}\left|-\phi\left|\psi\right|^2\Big|^{2\pi}_0 + \int_0^{2\pi} \left|\psi\right|^2 d\phi\right| = \frac{\hbar}{2}\left|1 - 2\pi \left|\psi\left(2\pi\right)\right|^2\right| \neq \frac{\hbar}{2}$$
    For wave functions that vanish on the boundaries $0$ and $2\pi$, we recover the Heisenberg uncertainty relation. However, in general for periodic wave functions this does not hold.
    Therefore the Heisenberg uncertainty relation does not hold for angular coordinates.
    Additionally, the commutator computed at the beginning of the problem being utilized in this problem would yield the same result with an additional imaginary term that comes from the multivaluedness of the wavefunction $\psi\left(\phi\right)$ with integer $2\pi$ additions to the angular coordinate $\phi$. This term can only increase the uncertainty rather than decrease it however due to it being an imaginary term under the magnitude bars.
\end{solution}
\end{document}