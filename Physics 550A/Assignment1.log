This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024/Arch Linux) (preloaded format=pdflatex 2024.8.31)  13 SEP 2024 20:44
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/home/<USER>/WorkSpace/Physics 550A/Assignment1.tex"
(/home/<USER>/WorkSpace/Physics 550A/Assignment1.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count188
\c@section=\count189
\c@subsection=\count190
\c@subsubsection=\count191
\c@paragraph=\count192
\c@subparagraph=\count193
\c@figure=\count194
\c@table=\count195
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (/usr/share/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
) (/usr/share/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen141
)) (/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen142
) (/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count196
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count197
\leftroot@=\count198
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count199
\DOTSCASE@=\count266
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box51
\strutbox@=\box52
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen143
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count267
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count268
\dotsspace@=\muskip16
\c@parentequation=\count269
\dspbrk@lvl=\count270
\tag@help=\toks20
\row@=\count271
\column@=\count272
\maxfields@=\count273
\andhelp@=\toks21
\eqnshift@=\dimen144
\alignsep@=\dimen145
\tagshift@=\dimen146
\tagwidth@=\dimen147
\totwidth@=\dimen148
\lineht@=\dimen149
\@envbody=\toks22
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/share/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (/usr/share/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (/usr/share/texmf-dist/tex/latex/mhchem/mhchem.sty
Package: mhchem 2024/01/29 v4.10 for typesetting chemical formulae
 (/usr/share/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (/usr/share/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count274
\l__pdf_internal_box=\box53
)) (/usr/share/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2024-02-18 LaTeX2e option processing using LaTeX3 keys
) (/usr/share/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count275
\calc@Bcount=\count276
\calc@Adimen=\dimen150
\calc@Bdimen=\dimen151
\calc@Askip=\skip53
\calc@Bskip=\skip54
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count277
\calc@Cskip=\skip55
) (/usr/share/texmf-dist/tex/latex/chemgreek/chemgreek.sty (/usr/share/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: chemgreek 2020/01/16 v1.1a interface for upright Greek letters for use in chemistry (CN)
\l__chemgreek_tmpa_int=\count278
\g__chemgreek_tmpa_int=\count279
) (/usr/share/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (/usr/share/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (/usr/share/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\l__mhchem_ce_distanceFromLastComma_int=\count280
\__mhchem_arrow_extPgfLength_skip=\skip56
\mhchem@minispace@tmp=\skip57
\mhchem@minibackspace@tmp=\skip58
\mhchem@bondwidth=\skip59
\mhchem@bondheight=\skip60
\mhchem@smallbondwidth@tmpA=\skip61
\mhchem@smallbondwidth@tmpB=\skip62
\mhchem@smallbondwidth=\skip63
\mhchem@bondtmp@minussidebearingleft=\skip64
\mhchem@bondtmp@minussidebearingright=\skip65
\l__mhchem_option_version_int=\count281
\mhchem@option@minusmathsidebearingleft=\dimen152
\mhchem@option@minusmathsidebearingright=\dimen153
\mhchem@option@minustextsidebearingleft=\dimen154
\mhchem@option@minustextsidebearingright=\dimen155
) (/usr/share/texmf-dist/tex/latex/stmaryrd/stmaryrd.sty
Package: stmaryrd 1994/03/03 St Mary's Road symbol package
\symstmry=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `stmry' in version `bold'
(Font)                  U/stmry/m/n --> U/stmry/b/n on input line 89.
) (./Assignment1.aux)
\openout1 = `Assignment1.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.

Package chemgreek Info: Activating mapping `default' on line 17.

 (/usr/share/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count282
\scratchdimen=\dimen156
\scratchbox=\box54
\nofMPsegments=\count283
\nofMParguments=\count284
\everyMPshowfont=\toks24
\MPscratchCnt=\count285
\MPscratchDim=\dimen157
\MPnumerator=\count286
\makeMPintoPDFobject=\count287
\everyMPtoPDFconversion=\toks25
) (/usr/share/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/share/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
LaTeX Font Info:    Trying to load font information for U+msa on input line 18.
 (/usr/share/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 18.
 (/usr/share/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for U+stmry on input line 18.
 (/usr/share/texmf-dist/tex/latex/stmaryrd/Ustmry.fd) [1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}{/usr/share/texmf-dist/fonts/enc/dvips/cm-super/cm-super-t1.enc}] [2] [3] (./Assignment1.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********
 ) 
Here is how much of TeX's memory you used:
 4036 strings out of 476076
 64443 string characters out of 5793775
 1935187 words of memory out of 5000000
 26105 multiletter control sequences out of 15000+600000
 569466 words of font info for 73 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 65i,9n,79p,400b,223s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb></usr/share/texmf-dist/fonts/type1/public/amsfonts/symbols/msbm10.pfb></usr/share/texmf-dist/fonts/type1/public/cm-super/sfrm1000.pfb></usr/share/texmf-dist/fonts/type1/public/cm-super/sfrm1200.pfb></usr/share/texmf-dist/fonts/type1/public/cm-super/sfrm1728.pfb>
Output written on Assignment1.pdf (3 pages, 133978 bytes).
PDF statistics:
 70 PDF objects out of 1000 (max. 8388607)
 42 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

