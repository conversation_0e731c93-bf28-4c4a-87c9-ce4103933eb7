# Master's Thesis La<PERSON>eX Template

## Compilation Instructions

### Using VSCode

1. Install the LaTeX Workshop extension in VSCode if you haven't already:
   - Open VSCode
   - Go to Extensions (Ctrl+Shift+X or Cmd+Shift+X on Mac)
   - Search for "LaTeX Workshop"
   - Click Install

2. Open this project folder in VSCode

3. To compile the document:
   - Open `main.tex`
   - Either:
     - Save the file (Ctrl+S or Cmd+S on Mac) to trigger automatic build
     - Use the LaTeX Workshop sidebar (click the TeX icon on the left sidebar)
     - Use the keyboard shortcut Ctrl+Alt+B or Cmd+Alt+B on Mac

4. View the PDF:
   - Click the "View PDF" button in the LaTeX Workshop sidebar
   - Or use the keyboard shortcut Ctrl+Alt+V or Cmd+Alt+V on Mac

### Manual Compilation

If you prefer to compile manually from the terminal, use these commands:

```bash
xelatex -shell-escape main.tex
biber main
xelatex -shell-escape main.tex
xelatex -shell-escape main.tex
```

## Important Notes

- The `-shell-escape` flag is required for the minted package to work properly
- You need to have pygmentize installed for syntax highlighting
- The document uses XeLaTeX for compilation
- Bibliography is processed with <PERSON>iber
