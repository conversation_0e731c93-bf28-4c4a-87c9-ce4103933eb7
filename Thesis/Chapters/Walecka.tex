\section{Angle-resolved Photoemission Spectroscopy (ARPES)}
\indent In our study we utilize angle-resolved photoemission spectroscopy (ARPES) to probe the electronic band structure of PtTe$_2$ and its Cr alloys. Analysis is then peformed using custom developed Python code. In this chapter, the physics of ARPES are reviewed, and the experimental setup at Lawrence Berkeley National Laboratory's (LBNL) Advanced Light Source (ALS) Beamline 4.0.3 MERLIN is outlined and discussed. The overarching analysis routines are additionally explored with specialized analysis routines being outlined in later chapters.
\subsection{The Physics of ARPES}
\indent ARPES relies on the photoelectric effect, which was the subject of <PERSON>'s 1921 Nobel Prize in Physics. Additionally, quantum mechanical results in the form of <PERSON><PERSON><PERSON>'s Golden Rule are necessary to understand the intensity profile of the emitted electrons that manifests in the form of our experimental data. 
\subsubsection{The Photoelectric Effect}
The photoelectric effect can be defined as the emission of electrons from a material when it is exposed to electromagnetic radiation. ARPES makes use of the photoelectric effect by utilizing electromagnetic radiation in the form of high-energy photons incident on a material. The photon transfers its energy to an electron in the material, which is then emitted as the nominal photoelectron. The energy of the emitted electron is directly measured while its momentum is indirectly measured via the emission angle. 
The photoelectric effect can be outlined by the following equation
\begin{equation}
    h\nu = E_k + \phi + E_B
    \label{eq:PhotoelectricEffect}
\end{equation}
where $h\nu$ is the energy of the incident photon, $E_k$ is the kinetic energy of the emitted electron, $\phi$ is the work function of the material, and $E_B$ is the binding energy of the electron in the material. The work function is the minimum energy required to remove an electron from the material, while the binding energy is the energy required to remove the electron from its band. 
The ARPES technique is primarily concerned with obtaining the energy and momentum of electrons inside the material by measuring the energy and angle of emission of the photoelectrons that are emitted.  Eq \ref{eq:PhotoelectricEffect} can be inverted to yield the binding energy of the electron:
\begin{equation}
E_B = h\nu - E_k - \phi
\end{equation}
\indent The binding energy is related to the Fermi level by the following equation:
\begin{equation}
    E_B = E_F - E_k
\end{equation}
where $E_F$ is the Fermi level. Energy measurement only accounts for half of the ARPES technique, the other half being the measurement of the momentum of the emitted electron. The momentum of the emitted electron is measured via the emission angle. First we may convert energy to the magnitude of the momentum of the emitted electron using the following equation:
\begin{equation}
    \left|\vec{p}_f\right| = \hslash \left|\vec{k}_f\right| = \sqrt{2m_eE_k} 
\end{equation}
where $m_e$ is the mass of the electron and $\left|\vec{k}_f\right|$ is the magnitude of the final wavevector of the emitted electron. Our incident photon transfers momentum only in the direction normal to the surface of the material, hence any parallel momentum the emitted electron has must come from its state in the crystal lattice. The conservation of momentum in the plane of the surface can be written as:
\begin{equation}
    \vec{p}_{i\parallel} = \vec{p}_{f\parallel} \quad \Rightarrow \quad \left|\vec{p}_{i\parallel}\right| = \hslash \left|\vec{k}_{i\parallel}\right| = \sqrt{2m_e E_k}\sin\vartheta
\end{equation}
where $\vartheta$ is the photoelectron emission angle with respect to the surface normal to the sample. 
We can solve for the initial perpendicular component in terms of the kinetic energy $E_k$, the emission angle $\vartheta$ and the inner potential $V_0$ to arrive at the following expression:
\begin{equation}
    \left|\vec{p}_{i\perp}\right| = \hslash \left|\vec{k}_{i\perp}\right| = \sqrt{2m_e E_k\left(\cos^2{\vartheta} +\frac{V_0}{E_k}\right)}
\end{equation}
\indent For low-energy photon experiments, the momentum of the incoming photon is neglected, and the inner potential $V_0$ is determined by comparing photon-dependent scans.
Our study is interested in dispersion relations of the form $E(\vec{k})$, hence our necessary set of ARPES equations are as follows:
\begin{equation}
    E_B = h\nu - E_k - \phi
\end{equation}
\begin{equation}
    \left|\vec{k}_{i\parallel}\right| = \frac{1}{\hslash}\sqrt{2m_e E_k}\sin\vartheta
\end{equation}
\begin{equation}
    \left|\vec{k}_{i\perp}\right| = \frac{1}{\hslash}\sqrt{2m_e E_k\left(\cos^2{\vartheta} +\frac{V_0}{E_k}\right)}
\end{equation}
By use of the photoelectric effect and momentum conservation laws, the ARPES technique is able to extract the binding energy and momentum of the emitted electron via measurements of its emission angle $\vartheta$ and kinetic energy $E_k$. Interpretation of ARPES data in the context of quantum materials requires further inquiry. 
\subsubsection{Intensity Profile and Fermi's Golden Rule}
A full ARPES experiment involves the detection of high numbers of photoelectrons which builds out an intensity profile $I(\vartheta, E_k)$ from which the binding energy and momentum of the emitted electron can be extracted. Useful physical information is specifically extracted through transformations of the measured intensity profiles. The first of which is the energy-momentum profile $I(E, \left|\vec{k}_\parallel\right|)$ which can be acquired through the previously listed conservation laws. The second is the constant energy cut profile $I(k_x, k_y)\Big|_{E=const}$ which are acquired by transforming the intensity profile $I(\vartheta, E_k)$ via the geometric expressions relating to the orientation of the measured sample. The measured intensity profile is related to the quantum mechanical electron states via the following relation:
\begin{equation}
    I \propto \left|\left<\psi_f\left|\vec{A}\left(\omega\right)\cdot \vec{j}\right|\psi_i\right>\right|^2
\end{equation}
where $\psi_f$ and $\psi_i$ are the final and initial states of the emitted electron, $\vec{A}\left(\omega\right)$ is the vector potential of the incoming photon, and $\vec{j}$ is the electric current density. More rigorously, one can utilize Fermi's Golden Rule to arrive at the exact expression.
High resolution data extracted via the ARPES technique requires a consistent source of high-energy photons, which is provided by the ALS at LBNL.
\begin{figure}
    \begin{center}
        \includegraphics[width=0.60\textwidth]{Chapters/ARPESDiagram.png}
    \end{center}
    \caption[Diagram of ARPES experimental setup]{Diagram of a laboratory ARPES experiment setup (from Wikipedia page on ARPES). The incoming photon beam is incident on the sample, exciting electrons to the vacuum level. The emitted electrons are then detected by the electron analyzer at their emission angle $\vartheta$. The analyzer measures the kinetic energy and emission angle of the emitted electrons, from which the binding energy and momentum of the emitted electron can be extracted. The measured intensity profile is related to the quantum mechanical electron states via Fermi's Golden Rule.}
\end{figure}
\subsection{The Advanced Light Source (ALS) Beamline 4.0.3 (MERLIN)}
\begin{figure}
    \begin{center}
        \includegraphics[width=0.25\textwidth]{Chapters/BeamlineImage.jpg}
    \end{center}
    \caption[Beamline 4.0.3 (MERLIN) at the ALS]{Beamline 4.0.3 (MERLIN) at the ALS.}
\end{figure}
\indent Beamline 4.0.3 (MERLIN) is the experimental setup utilized for our ARPES measurements and is located at the ALS at LBNL. The beamline is a state-of-the-art ARPES setup that is capable of measuring photoemission with high resolution and precision. The setup permits an energy resolution of $\Delta E \approx 20 \text{ meV}$ and an angular resolution of $\Delta \vartheta \approx 0.20^\circ$. Momentum is proportional to the sine of the emission angle, hence the momentum resolution is given by $\Delta k \sim \sin{0.20^\circ}$. The synchrotron light source produces monochromatic light with photon energies in the range of $h\nu \in \left[20, 150\right] \text{ eV}$. Beamline 4.0.3 is outfitted with a variety of experimental chambers for sample preparation and measurement. The experimental chambers are connected to a vacuum system that maintains a minimum vaccum pressure of $10^{-11} \text{ Torr}$ in the measurement chamber. The sample is mounted on a manipulator that allows for precise control of the sample's orientation and position. Additionally, the polarization of the incoming photon beam can be controlled to allow for measurements of linear and circular dichroism of the material. This can be especially relevant to magnetic materials such as the Cr alloys of PtTe$_2$ that we study.
\subsection{Analysis Suite}
The measurements taken at the ALS are analyzed using a custom analysis suite programmed in Python. Due to Python's extensive package ecosystem, the analysis suite is able to take advantage of a wide range of tools for data manipulation, visualization, and analysis. It is designed to be flexible and easily modified to accomodate specialized analysis needs that may be required for novel information extraction. 
\subsubsection{PyARPES}
The ALS packages data files in a format usually opened in Igor Pro, a proprietary data analysis software. This packaging is helpful as it includes not only the raw data, but the metadata relating to the beamline and the ARPES setup's machine state parameters. The most relevant of this metadata is the photon energy of the incoming photon beam, the sample's polar angle, the sample's temperature, and the light polarization. 
We utilize the PyARPES package developed by the Lanzara Group at UC Berkeley to read in the data files and extract the physical measurements and the relevant metadata. Specifically we utilize the import function \textbf{read\_single\_pxt} which loads a single scan but can be iterated over to load multiple scans for full experiments. The remainder of the analysis suite is custom developed and contains a variety of features for either $2$-dimensional or $3$-dimensional analysis.
\subsubsection{2D Analysis}
\begin{figure}
    \begin{center}
        \includegraphics[width=0.55\textwidth]{Chapters/ARPESSuite.png}
    \end{center}
    \caption[Screenshot of the 2D analysis interface]{Screenshot of the 2D analysis interface. Display controls can be used to correct data offsets or denoise. Analysis options include constant intensity contours, intensity peak detection, moving average filtering, and more.}
    \label{fig:ARPESSuite}
\end{figure}
The functionality of the $2$-dimensional analysis toolbox is centered around the extraction of the material's band structure from $E$ vs $k_\parallel$ plots or constant energy $k_y$ vs $k_x$ maps constructed from iterative measurements of the sample at varied polar angles. 
For an $E$ vs $k_\parallel$ plot, we utilize our measured intensity profile $I(\vartheta, E_k)$, and apply a series of transformations to convert it to a physically meaningful representation of the band structure. The first transformation converts our intensity profile as a function of measured emission angle to a function of the momentum of the emitted electron via the following equation:
\begin{equation}
    f: I\left(\vartheta, E_k\right) \mapsto I(\left|\vec{k}_\parallel\right|, E_k) \quad \text{ where } \left|\vec{k}_\parallel\right| = \frac{1}{\hslash}\sqrt{2m_e E_k}\sin\vartheta
\end{equation}
where $\vartheta$ is the measured emission angle, $E_k$ is the measured kinetic energy, and $\left|\vec{k}_\parallel\right|$ is the magnitude of the parallel component of the emitted electron's wavevector.
The next transformation simply translates the intensity profile by zeroing the binding energy to the Fermi level:
\begin{equation}
    g: I(\left|\vec{k}_\parallel\right|, E_k) \mapsto I(\left|\vec{k}_\parallel\right|, E_B) \quad \text{ where } E_B = E_k - E_F
\end{equation}
The resultant intensity profile $I(\left|\vec{k}_\parallel\right|, E_B)$ is then ready for analysis.
For a constant energy $k_y$ vs $k_x$ map, we again work with measured intensity profile $I(\vartheta, E_k)$, although we utilize an entire set of scans with the sample oriented at varied polar angles. We can then separate our parallel momentum into its $x$ and $y$ components via the following equations:
\begin{equation}
    k_x = \frac{1}{\hslash}\sqrt{2m_e E_k}\sin\vartheta
\end{equation}
\begin{equation}
    k_y = \frac{1}{\hslash}\sqrt{2m_e E_k}\sin\phi
\end{equation}
where $\phi$ is the polar angle of the sample.\\ 
\indent After translating the intensity profile to zero the binding energy to the Fermi level, we arrive at an intensity profile of the form $I(k_x, k_y, E_B)$. Taking cross sections of this intensity profile at constant energy, we arrive at the desired $k_y$ vs $k_x$ map $I(k_x, k_y)\Big|_{E_B=const}$. At $E_B = 0$ we arrive at the Fermi surface which is one of the objects of focus in this study.
Now after converting our intensity profiles to physically meaningful representations, we must extract the relevant features of the image. This is done through a series of image processing algorithms. 
The first step is the application of a filter to remove noise. For noise reduction, the primary algorithm is the moving average which scrolls across the image and replaces the value of each pixel with the average of the values of the pixels in a surrounding window. This is more colloquially known as blurring. The moving average transformation $M$ can be defined formally:
\begin{equation}
    M_A: \mathbb{R}^{m \times n} \to \mathbb{R}^{m \times n}, \quad I \mapsto M\left[I(x, y)\right] = \frac{1}{\left(2k+1\right)^2}\sum_{i=-k}^{k}\sum_{j=-k}^{k}I(x+i, y+j)
    \label{eq:MovingAverage}
\end{equation}
where $m$ and $n$ are the dimensions of the image, $k$ is the number of pixels to average over, and $I(x, y)$ is the intensity at pixel $(x, y)$. The resultant image is smoothed and noise is reduced while the essential features of the image are preserved. 
\begin{figure}
    \begin{center}
        \includegraphics[width=0.55\textwidth]{Chapters/MovingAverageExample.png}
    \end{center}
    \caption[Moving Average Filtering Example]{Example of moving average filtering applied to a energy-momentum ARPES map. The intensity profile is denoised while maintaining structural features of the image.}
\end{figure}
Another useful image processing algorithm is Canny edge detection. This algorithm is used to identify the edges of features in the image. The Canny edge detection algorithm $C$ can be defined formally using a combination of Gaussian blurring and Sobel filtering.
Each of these tranformations are defined in terms of a convolution with a kernel. The convolution operation of a function $f(x,y)$ with a kernel $k(x,y)$ is defined as:
\begin{equation}
    (f * k)(x, y) = \int_{-\infty}^{\infty}\int_{-\infty}^{\infty} f(u, v)k(x-u, y-v) \, du \, dv \approx \sum_{i,j} f(x,y)k(x-i, y-j)
\end{equation}
We define Gaussian blurring $G$ as
\begin{equation}
    G: \mathbb{R}^{m \times n} \to \mathbb{R}^{m \times n}, \quad I \mapsto G\left[I(x, y)\right] = I(x,y)*G(x,y) 
\end{equation}
where $G(x,y)$ is the Gaussian Kernel which can be defined in the discrete case, for a kernel of size $n$ in terms of the values in the $n$'th row of Pascal's triangle. We first define an operation $\odot$ as the following:
\begin{equation}
\bar{v} \odot \bar{\bar{M}} = \begin{bmatrix}
    v_1 \\ v_2 \\ \vdots \\ v_n \end{bmatrix} \odot 
        \begin{bmatrix}
            m_{11} & m_{12} & \dots & m_{1n} \\
            m_{21} & m_{22} & \dots & m_{2n} \\
            \vdots & \vdots & \ddots & \vdots \\
            m_{n1} & m_{n2} & \dots & m_{nn}
    \end{bmatrix} = \begin{bmatrix}
    v_1m_{11} & v_1m_{12} & \dots & v_1m_{1n} \\
    v_2m_{21} & v_2m_{22} & \dots & v_2m_{2n} \\
    \vdots & \vdots & \ddots & \vdots \\
    v_nm_{n1} & v_nm_{n2} & \dots & v_nm_{nn}
\end{bmatrix}
\end{equation}
The Gaussian Kernel is then defined as:
\begin{equation}
    G = \frac{\bar{p} \odot \bar{\bar{T}}}{\max\left(\bar{p} \odot \bar{\bar{T}}\right)}
\end{equation}
with $\bar{p}$'s components being the values in the $n$'th row of Pascal's triangle and $\bar{\bar{T}}$ being a matrix constructed by repeating the $n$'th row of Pascal's triangle $n$ times.
Next we may approximate the gradient of the intensity profile $I(x, y)$ using the Sobel filters $S_x$ and $S_y$ defined as:
\begin{equation}
    S_x: \mathbb{R}^{m \times n} \to \mathbb{R}^{m \times n}, \quad I \mapsto S_x\left[I(x, y)\right] = \begin{bmatrix}
        -1 & 0 & 1 \\
        -2 & 0 & 2 \\
        -1 & 0 & 1
    \end{bmatrix} * I(x, y)
\end{equation}
\begin{equation}
    S_y: \mathbb{R}^{m \times n} \to \mathbb{R}^{m \times n}, \quad I \mapsto S_y\left[I(x, y)\right] = \begin{bmatrix}
        -1 & -2 & -1 \\
        0 & 0 & 0 \\
        1 & 2 & 1
    \end{bmatrix} * I(x, y)
\end{equation}
The gradient vector of the intensity profile is then approximated as:
\begin{equation}
    \nabla I(x, y) = \left[S_x\left[I(x, y)\right], S_y\left[I(x, y)\right]\right]
\end{equation}
and the gradient magnitude is approximated as:
\begin{equation}
    M: \nabla{I(x, y)} \mapsto \left|\nabla I(x, y)\right| = \sqrt{S_x\left[I(x, y)\right]^2 + S_y\left[I(x, y)\right]^2}
\end{equation}
Next we define the non-maximum suppression transformation $N(I, \nabla I)$ as:
\begin{equation}
    N(I, \nabla I): \mathbb{R}^{m \times n} \to \mathbb{R}^{m \times n} \end{equation} \begin{equation*} I \mapsto N\left[I(x, y), \nabla I(x, y)\right] = \begin{cases}
        I(x, y) & \text{if } I(x, y) \text{ is a local maximum in the direction of } \nabla I(x, y) \\
        0 & \text{otherwise}
    \end{cases}
\end{equation*}
Finally we define the thresholding transformation $T$ as:
\begin{equation}
    T: \mathbb{R}^{m \times n} \to \mathbb{R}^{m \times n}, \quad I \mapsto T\left[I(x, y)\right] = \begin{cases}
        1 & \text{if } I(x, y) > T \\
        0 & \text{otherwise}
    \end{cases}
\end{equation}
where $T$ is the threshold value.
\begin{figure}
    \begin{center}
        \includegraphics[width=0.55\textwidth]{Chapters/CannyEdge.png}
    \end{center}
    \caption[Canny Edge Detection Example]{Example of Canny edge detection applied to a constant energy ARPES map. The edges of the bands are highlighted in cyan.}
\end{figure}
The Canny edge detection algorithm is then defined as a composition of these transformations.
\begin{equation}
    C: \mathbb{R}^{m \times n} \to \mathbb{R}^{m \times n}, \quad I \mapsto T \circ N \circ \left(M \circ \nabla \circ G \right)\left[I(x, y)\right]
\end{equation}
The resultant image is a binary image with the edges of the features in the image highlighted.
This algorithm allows for extraction of the edges of bands and important features of the ARPES data that may otherwise be obscured by the complexity of our material's band structure. The extensiveness of the $2$-dimensional analysis suite allows for a wide range of computations to be performed on the ARPES data constituting vast potential for physical information extraction. For the purposes of studying the Fermi surface we are interested in $3$-dimensional analysis which will be outlined in the final chapter of this work.