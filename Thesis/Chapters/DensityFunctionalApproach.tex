\newcommand{\Mod}[1]{\ (\mathrm{mod}\ #1)}

\section{Topology, Manifolds, and Morse Theory}
In our study of the Fermi surface and topological transitions, we necessarily make use of topological concepts. In this chapter, we will first review the necessary mathematical background to understand the topological properties of the Fermi surface. Then we will utilize these tools to introduce the concept of topological invariants, most notably the Euler characteristic, which we will later use to study the topological transition (Lifshitz transition) in PtTe$_2$ and its Cr alloys. To solidify our analysis, we will additionally justify the use of Morse Theory in our computation of the Euler characteristic via ARPES data.
\subsection{Topology}
We can begin with definitions of concepts in topology that are most relevant to our understanding of the Fermi surface as a topological object.
\subsubsection{Topological Spaces}
Formally, a topological space $S= (X, T)$ consists of a set $X$ together with a collection $T$ of subsets of $X$, called open sets, that satisfy the following axioms :
\begin{equation}
\emptyset \in T \quad \text{and} \quad X \in T
\end{equation}
\begin{equation}
\bigcup_{i \in I} U_i \in T \quad \text{for any collection} \quad \set{U_i}_{i \in I} \subseteq T
\end{equation}
\begin{equation}
U_1 \cap U_2 \cap \cdots \cap U_n \in T \quad \text{for any finite collection} \quad \set{U_i}_{i=1}^n \subseteq T
\end{equation}
$T$ is called the topology on the set $X$ and $I$ is an arbitrary index set. These axioms ensure that the empty set and the entire set are open, arbitrary unions of open sets remain open, and finite intersections of open sets remain open. The collection $T$ formalizes the concept of "openness" and provides the foundation for defining continuity and other topological properties.
The complement of an open set is called a closed set, and sets may be both open and closed (clopen sets) or neither open nor closed. While not easily visualized, these axioms provide a sense of "nearness" and "closeness" in a topological space without relying on any form of metric \cite{Nakahara:2003}. \\ 
\indent In the context of our ARPES study, the set $X$ is the $2$-dimensional $k$-space spanned by the coordinates $k_x$ and $k_y$. The collection $T$ encodes the connectivity and structure of the electron states. In the context of analysis, we begin with the discrete dataset collected via our photoemission experiment. This comes (after converting angles to momenta) in the form of a set of points in $E-k$ space of the form $(I, E, k_x, k_y)$. Our dataset is of high enough resolution that we can approximate a continuous momentum space. The open sets that form the topology $T$ correspond to connected regions in momentum space where the energy varies smoothly. As this is a physical system, it is usually a valid assumption that small perturbations in momentum values result in only small variations in energy. This ensures our dataset satisfies the conditions required for topological analysis.
We can explicitly define this topological space $S_{BZ} = (X_{BZ}, T_{BZ})$ and its properties
\begin{equation}
    X_{BZ} = \set{\vec{k} \in \mathbb{R}^2 \mid \vec{k} \text{ is a valid momentum in the Brillouin Zone}}
\end{equation}
This statement encodes that our set on which we are defining a topology is the Brillouin Zone (BZ) of our material. 
Our topology $T_{BZ}$ can additionally be defined in the following way using the open ball $B(\vec{k}, \epsilon)$ centered at $\vec{k}$ with radius $\epsilon$
\begin{equation}
    T_{BZ} = \set{U \subseteq X_{BZ} \mid \forall \vec{k} \in U, \exists \epsilon > 0 \text{ such that } B(\vec{k}, \epsilon) \cap X_{BZ} \subseteq U}
\end{equation}
This statement simply encodes that the connectedness of the BZ defines our topology. Specifically, that the topology defined by the open sets of $X_{BZ}$ requires that for any $\vec{k} \in U \subseteq X_{BZ}$, there exists an open ball $B(\vec{k}, \epsilon)$ centered at $\vec{k}$ with radius $\epsilon$ such that the overlap of the ball $B(\vec{k}, \epsilon)$ with the BZ is contained within $U$. In other words, our topology is defined such that we preserve the continuity of the band structure within the BZ.
Next we can formally describe the form of our ARPES dataset. We denote the set of our data as $\mathcal{D}$ and describe its form as such
\begin{equation}
\mathcal{D} = \set{\left[\vec{k}, E_B, I\right] \in \mathbb{R}^4\mid \vec{k} \in X_{BZ}, E_B \in [E_{B_{min}}, 0], I \in [0, I_{max}]}
\label{eq:ARPESData}
\end{equation}
This statement encodes that our dataset is a subset of $\mathbb{R}^4$ that contains the momentum values $\vec{k}$ within the BZ, the binding energy values $E_B$ within the range of the minimum energy of our dataset $E_{B_{min}}$ and the Fermi level $E_B = 0$, and the intensity values $I$ within the range of $0$ and the maximum intensity of our dataset $I_{max}$.
This dataset captures a measured portion of the Fermi sea $F_S$ as it contains (approximately) all points $\left[\vec{k}, E(\vec{k})\right]$ for which the binding energy is below the Fermi level. 
Now we will describe the importance of equivalence and homemorphism in topological spaces.
\subsubsection{Equivalence and Homeomorphism}
\begin{figure}
    \begin{center}
        \includegraphics[width=0.55\textwidth]{Chapters/DoughnutMug.png}
    \end{center}
    \caption[Example of a Homeomorphism]{Example of a homeomorphism between two topological spaces. The continuous deformation of a torus into a coffee cup is shown. Figure by \cite{Dana2022}.}
    \label{fig:Homeomorphism}
\end{figure}
Let us consider two topological spaces $S_1 = (X_1, T_1)$ and $S_2 = (X_2, T_2)$. We say that $S_1$ and $S_2$ are homeomorphic if there exists a bijective function $f: X_1 \to X_2$ such that both $f$ and its inverse $f^{-1}$ are continuous. Two topological spaces are considered homeomorphic if one can be continuously deformed into the other. This mapping that undertakes this deformation is known as a homeomorphism \cite{Nakahara:2003}. Homeomorphic spaces are considered equivalent, even if their component sets are not identical. The definition of equivalence is in the sense that they share the same topological properties, most notable to our conversation are topological invariants.\\ 
\indent We can contexualize this in the case of distinct Fermi seas. In our study of PtTe$_2$ and its Cr alloy compounds of the form Cr$_x$Pt$_{1-x}$Te$_2$, we are interested in the topology of the Fermi sea and consequently the Fermi surface as a function of the Cr concentration $x$. The Fermi sea can be used to define a topological space as we have shown previously and the Cr ratio $x$ can be thought of as a parameter that labels the Fermi sea, and as we vary this parameter, the Fermi sea undergoes a mapping. Inspection of our ARPES data has shown that the mapping's main effect is changing the value of the Fermi energy $E_F = E_F(x)$, which significantly alters the Fermi sea by covering bands. First we can define the Fermi sea of PtTe$_2$ as
\begin{equation}
    F_{S_0} = \set{\vec{k} \in \mathbb{R}^2 \mid E(\vec{k}) \leq E_{F_{\text{PtTe$_2$}}}}
\end{equation}
Next we may define the Fermi sea of the Cr alloy compounds as
\begin{equation}
    F_{S_x} = \set{\vec{k} \in \mathbb{R}^2 \mid E(\vec{k}) \leq E_F(x)}
\end{equation}
Then we may denote the mapping of PtTe$_2$ to its Cr alloy compounds as
\begin{equation}
    C: F_{S_0} \mapsto F_{S_x}, \quad E_{F_{\text{PtTe$_2$}}} \mapsto E_F(x)
\end{equation}
\indent We hypothesize that this mapping is not bijective for at least certain values of $x$, predicated on the idea that the altering of the Fermi level $E_F(x)$ causes the Fermi sea undergoes a topological transition at a critical Cr concentration $x_c$. This transition is known as a Lifshitz transition, and it is characterized by a change in the topology of the Fermi surface. 
\begin{equation}
    x_c = \set{x \in [0, 1] \mid S_{BZ}(x^-) \not \cong S_{BZ}(x^+)}
\end{equation}
We aim to identify this transition by studying the topological invariants of the Fermi sea and measuring how they are altered or unaltered as we vary the Cr concentration.
\subsubsection{Topological Invariants}
Now that we have introduced the concept of topological spaces and homeomorphisms, we now study a method of quantifying the topological properties of a space. We state that certain properties of topological space are invariant under homeomorphism. These properties are known as topological invariants. We can therefore state that if two spaces are homeomorphic, then they share the same topological invariants. More importantly, we can consider the contrapositive of this statement, that if two spaces do not share the same topological invariants, then they are not homeomorphic. This provides a method of distinguishing between topological spaces. This is what is meant when we say a donut and a coffee cup are topologically equivalent, as they can both be continuously deformed into one another as shown in Figure \ref{fig:Homeomorphism}. In that case, the most notable topological invariant is the number of holes in the space, which is $1$ for both a donut and a coffee cup. In our study of the Fermi sea, the topological invariant of choice is the Euler characteristic.
\subsubsection{The Euler Characteristic}
The Euler characteristic is one of the most useful topological invariants. It is defined for a polyhedron as the number of vertices minus the number of edges plus the number of faces, or
\begin{equation}
    \chi = V - E + F
\end{equation}
We can adapt the Euler characteristic to continuous spaces by considering smooth manifolds. A smooth manifold is a topological space that is locally Euclidean, meaning that every point has a neighborhood that is homeomorphic to an open subset of Euclidean space. Smooth manifolds are the natural setting for differential geometry and topology, and they provide a rich framework for analyzing such spaces in the context of invariants.
\subsection{Smooth Manifolds}
\subsubsection{Differentiable Manifold}
We can state that an object $M$ is a smooth, $n$-differentiable manifold if it obeys the following axioms:
\begin{enumerate}
    \item $M$ is a topological space
    \item $M$ is provided with a collection of pairs $\set{(U_\alpha, \varphi_\alpha)}_{\alpha \in A}$ called an atlas, where each pair $(U_\alpha, \varphi_\alpha)$ is called a chart. ($U_\alpha \subseteq M$ and $\varphi_\alpha: U_\alpha \to \mathbb{R}^n$)
    \item $\set{U_\alpha}_{\alpha \in A}$ is a family of open sets that covers $M$. $\left(\bigcup_{\alpha \in A} U_\alpha = M\right)$, and $\varphi_\alpha$ are homeomorphisms from $U_\alpha$ to an open subset of $n$-dimensional Euclidean space.
    \item For two open sets $U_\alpha$ and $U_\beta$ that don't overlap, the transition map $\varphi_\beta \circ \varphi_\alpha^{-1}: \varphi_\alpha(U_\alpha \cap U_\beta) \to \varphi_\beta(U_\alpha \cap U_\beta)$ is a diffeomorphism (infinitely differentiable).
    
\end{enumerate}
This definition of a manifold allows us to perform calculus on the manifold, and it provides a solid framework for studying the topology of the manifold \cite{Nakahara:2003}. This is useful for our study of the Fermi surface ($S_F$) and the Fermi sea ($F_S$), as these can be shown to be smooth manifolds on which we can perform our analysis based on our previous definitions. We measure the Fermi sea and therefore ultimately the Fermi surface, via ARPES. Due to the complexity of the band structure of our material PtTe$_2$ it is imperative we have access to the tools of differential calculus to characterize the Fermi surface topology. Our study of topological invariants relies on the concepts of functions and maps between manifolds, which we will explore in the following section.\\
\indent We can use our previous definitions of our Fermi sea topological space $S_{BZ}$ and its charts to define the Fermi sea as a smooth manifold. It is clear to see, based on the definition of the Fermi sea, that we may chart the topological space with standard Cartesian coordinates and our requirements for a smooth manifold are satisfied.
\subsubsection{Differentiable Maps}
Let us consider a map $f: M \to N$ between two differentiable manifolds $M$ of dimension $m$ and $N$ of dimension $n$. The map behaves such that a point $x$ in $M$ is mapped to a point $f(x)$ in $N$. $\left(f: x \to f(x)\right)$
Now if we consider a chart $(U, \varphi)$ on $M$ and a chart $(V, \psi)$ on $N$, we can consider the map $f$ in the context of these charts. ($f: x\in U \to f(x)\in V$) We can write the composition of the charts and the map as
\begin{equation}
    \psi \circ f \circ \varphi^{-1}: \mathbb{R}^m \to \mathbb{R}^n
\end{equation}
We can obtain coordinate representations of the points by using our charts $\varphi(x) = \set{a^\mu} = \set{a^1, a^2, \dots, a^m}$ and $\psi(f(x)) = \set{b^\nu} = \set{b^1, b^2, \dots, b^n}$. It then becomes clear that the composition map is a familiar vector function:
\begin{equation}
    \psi \circ f \circ \varphi^{-1}(a) = b
\end{equation}
We say that $f$ is differentiable (smooth) at the point $x$ if the composition map is differentiable at the coordinate $\varphi(x)$ for each component $\mu$. An additional requirement is that this property must hold for all charts $(U, \varphi)$ on $M$ and $(V, \psi)$ on $N$. What we have shown is that the differentiability of a map between manifolds is independent of the choice of charts, yet the charts allow us to study the map in the familiar context of vector functions and differential calculus in Euclidean space \cite{Nakahara:2003}. This is a powerful conclusion that allows us to study the topology of manifolds of (nearly) arbitrary complexity.
\subsection{Critical Points and Morse Functions}
We have now outlined the use of differentiable manifolds and maps between them. This framework allows us to utilize the familar methods of differential calculus to study the topology of manifolds. In this section, we will introduce the concept of critical points and Morse functions, which will allow us to compute the topological invariants of interest of manifolds.
\subsubsection{Critical Points}
While a critical point is a common concept in single-variable and multivariable calculus, we must be cautious in its definition in the context of manifolds.\\ 
Let $f: M \to \mathbb{R}$ be a smooth function on a smooth $m$-dimensional manifold $M$. We state that a point $c \in M$ is a critical point of $f$ if the differential of $f$, $df_c: T_cM \to \mathbb{R}$, maps every tangent vector to zero \cite{Nakahara:2003}. In this context, $T_cM$ is the tangent space at the point $c$ in the manifold $M$, and $df_c$ is a linaer map that sends tangent vectors $v \in T_cM$ to directional derivatives of $f$ in the direction of $v$. This definition can be shown to be equivalent to the familiar definition of a critical point in multivariable calculus, where the gradient of the function is zero. We can write this (semi-trivially) as
\begin{equation}
    c \in \set{x \in M \mid df_x = 0}
    \iff df_c = 0
\end{equation}
We define the index of a critical point $c$ as the number of negative eigenvalues of the Hessian matrix of second derivatives of $f$ at $c$
\begin{equation}
    \text{index}(c) = \left|\set{\alpha \mid \lambda_\alpha < 0}\right| 
\end{equation}
where $\lambda_\alpha$ are the eigenvalues of the Hessian matrix $H_{ij}$ at the critical point $c$.
The Hessian matrix is a square matrix of second-order partial derivatives of a scalar-valued function. It describes the local curvature of a function of multiple variables
\begin{equation}
    H_{ij} = \frac{\partial^2 f}{\partial x_i \partial x_j}
\end{equation}
For a critical point to not be degenerate, the Hessian matrix must be invertible. This is equivalent to the statement that the Hessian matrix must not have a zero eigenvalue.
\subsubsection{Morse Functions}
We are now equipped to define one of the the cruxes of our study, the Morse function.\\ 
If $M$ is a smooth manifold and $f: M \to \mathbb{R}$ is a smooth mapping, then $f$ is a Morse function if all of its critical points are non-degenerate \cite{nash1988}. This condition ensures that the critical point is only a local minimum, maximum, or a saddle point.\\
\indent Our study of the Fermi surface and Fermi sea is well served by the use of Morse functions. As we have previously stated, a manifold can be defined that encodes the Fermi sea, which we have denoted $S_{BZ}$. Our photoemission experiment measures the energy-momentum dispersions that make up the electronic band structure of our material. This data can be used to define a Morse function on the Fermi sea manifold $S_{BZ}$ as

\begin{equation}
    f: S_{BZ} \to \mathbb{R}, \quad f(\vec{k}) = E(\vec{k})
\end{equation}
This allows us to interacting with our ARPES data using calculus. The importance of this will be made clear in the following section on Morse Theory.
\subsection{Morse Theory}
We have reached the peak of our theoretical approach to studying the topology of the Fermi surface. We have introduced the concept of smooth manifolds and maps between them, and we have defined critical points and Morse functions. We are now ready to introduce Morse theory, which highlights the main analytical method we will use to compute and quantify the topological invariants of the Fermi sea.
Morse theory can be well summarized in a single statement from Shintaro Fushida-Hardy at Stanford University \cite{fushidahardymorse}:
\begin{quote}
   "A well chosen map $f: M \to \mathbb{R}$ encodes a lot of information about $M$."
\end{quote}
The quote is vague yet astonishingly accurate. An example in the form of Reeb's Theorem may aid in illuminating this point. If we consider a manifold $M$, let us state that there exists a Morse function $f: M \to \mathbb{R}$ such that the number of critical points of $f$ is exactly $2$. We will claim without proof of this theorem, that in this case, the manifold $M$ must be homeomorphic to a sphere. This is a powerful result, as it allows us to study the topology of a manifold by studying the critical points of a Morse function on that manifold. While this result seems ad hoc and even specific to the case of a sphere, it is in fact a general result that can be applied to any manifold. The most important take away from this result is that the choice of a Morse function allows for the extraction of topological information from the critical points of the function.
\subsubsection{Morse's Theorem}
The theorem that we will make most notable use of is known as Morse's Theorem. It relates the Euler characteristic of a manifold to the number of critical points of a Morse function on that manifold. Morse's Theorem is as follows: If $f: M \to \mathbb{R}$ is a $C^\infty$ Morse function on a compact manifold $M$, then the Euler characteristic $\chi(M)$ is given by
\begin{equation}
    \chi(M) = \sum_{c \in \text{Crit}(f)} (-1)^{\text{index}(c)}
    \label{eq:MorseTheorem}
\end{equation}
where $\text{Crit}(f)$ is the set of critical points of $f$ and $\text{index}(c)$ is the index of the critical point $c$ as defined previously \cite{fushidahardymorse}.
The Euler characteristic is a topological invariant, meaning that it is preserved under homeomorphisms. Therefore, if we can compute the Euler characteristic of a manifold, we can use it to classify the topology of the manifold.\\ 
\indent We reach the final theoretical tool we need in our exploration of the Fermi sea topology and its possible topological transitions. We have shown that the Euler characteristic is a topological invariant that can be computed from the critical points of a Morse function on a manifold. We have additionally shown that the Fermi sea can be defined as a manifold and that the energy-momentum dispersions measured via ARPES can be used to define a Morse function on that manifold. Therefore, we have all the tools necessary to compute the Euler characteristic of the Fermi sea and use it to classify the topology of the Fermi surface. We can first determine the critical points on the Fermi sea by finding the points at which the gradient of the energy-momentum dispersion is zero:
\begin{equation}
    \text{Crit}(f) = \set{\vec{k} \in S_{BZ} \mid \nabla E(\vec{k}) = 0}
\end{equation}
\begin{equation}
    \text{index}(c) = \left|\set{\alpha \mid \lambda_\alpha < 0}\right|, \quad H_{ij} = \frac{\partial^2 E}{\partial k_i \partial k_j}\Bigg|_{\vec{k} = c}, \quad \det{\left(H_{ij} - \lambda_\alpha \delta_{ij}\right)} = 0
\end{equation}
\indent This conclusion enforces our previous prediction that a topological transition will occur due to the alteration of the Fermi level $E_F(x)$ as we vary the Cr concentration $x$. The critical points of the Fermi sea will change as we vary the Fermi level, and therefore the Euler characteristic will change. This change in the Euler characteristic will allow us to classify the topology of the Fermi surface and ultimately identify if a topological Lifshitz transition has occurred. The next chapter will outline the transformation of our ARPES data into a form that allows for the computation of the Euler characteristic and the determination of the Euler characteristic based on the Fermi level.
\section{The Lifshitz Transition}
In our study we have combined a variety of theoretical and experimental tools to outline a method for the identification of a topological Lifshitz transition at the Fermi surface of PtTe$_2$ and its Cr alloys. Via angle-resolved photoemission spectroscopy (ARPES), we have measured the electronic band structure of PtTe$_2$ and its Cr alloys. We have then used the mathematical framework of topology to define a manifold that encodes the Fermi sea, which we have shown can be used to compute the Euler characteristic via Morse Theory. The Euler characteristic, as a topological invariant, can then be used to classify the topology of the Fermi surface and ultimately identify if a topological Lifshitz transition has occurred. In this chapter, we will outline the transformation of our ARPES data into a form that allows for the computation of the Euler characteristic and the determination of the Euler characteristic based on the Fermi level. Additionally we will outline an important property of this Euler characteristic computation method that bolsters the validity of our method.
\subsection{Euler Characteristic on an Infinite Fermi Sea}
If we recall our definition of the Fermi-sea, and the energy dispersion map that we aim to consider our Morse function on the manifold, we must reconcile with the fact that our experimental data may not be a function. In other words, it is possible a single momentum value $\vec{k}$ can correspond to multiple energy values $E(\vec{k})$. This is a direct consequence of the fact that our ARPES measurements are sensitive to all electronic states and their dispersions within a certain energy range. While bijectivity is not a requirement for a Morse function, it complicates the computation of the Euler characteristic. 
\subsubsection{Local Changes in the Euler Characteristic}
The method by which we avoid these issues is to limit our calculations to small energy ranges within the band structure where we can treat the energy dispersion as a function. This is a valid assumption as the energy dispersions are well-behaved and do not change drastically within small energy ranges. In this case we may redefine our Fermi sea as
\begin{equation}
    F_S = \set{\left[\vec{k}, E(\vec{k})\right] \in \mathbb{R}^3 \mid E(\vec{k}) \leq E_F, E(\vec{k}) \in [E_1, E_2]}
\end{equation}
Then our topological definition of the Fermi sea set on which we define a topology is given by:
\begin{equation}
    S_{BZ} = \set{\vec{k} \in \mathbb{R}^2 \mid E(\vec{k}) \leq E_F, E(\vec{k}) \in [E_1, E_2]}
\end{equation}
While we avoid any difficulties relating to bijectivity of our energy dispersion map, we must now justify how our determination of the presence of a topological Lifshitz transition based on the Euler characteristic is still valid. Fortunately, this is very simple to show. If we consider the Euler characteristic of the Fermi sea as a function of the Fermi level $E_F$, we find that it is a piecewise constant function. In other words, the Euler characteristic will be constant for a range of Fermi levels, and then suddenly jump to a new value at a certain (or multiple) critical Fermi level(s). Therefore, a Lifshitz transition can be determined by the difference in the Euler characteristic between two sides of a critical Fermi level. We can write this formally as:
\begin{equation}
    \chi(E_{F_c}^+) - \chi(E_{F_c}^-) \neq 0 \quad \Rightarrow \quad \text{Lifshitz Transition}
\end{equation}
where $E_{F_c}^+$ and $E_{F_c}^-$ are the Fermi levels on either side of the critical Fermi level. This is a valid method for determining the presence of a Lifshitz transition as the Euler characteristic is a topological invariant and therefore cannot change suddenly without a topological transition. We may further exploit this due to the effect that Cr substitution has on our compound's band structure. Previous ARPES experiments show that the main effect of Cr substitution is the raising of band structure closer to the Fermi level $E=E_F$. Therefore we can focus on a notable band structure feature of PtTe$_2$, such as the Dirac point and track it through the Cr substitution series. The change in the energy level of such a feature will directly correspond to a change in the Fermi level relative to that feature. Therefore we can restrict our computations to an energy range of this size, below the Fermi level of PtTe$_2$. Additionally, we can perform these computations on PtTe$_2$ itself to determine the Euler characteristic of the Fermi sea in the absence of any Cr substitution keeping our calculation consistent.
\subsubsection{Tracking the Dirac Point}
\begin{figure}
    \begin{center}
        \includegraphics[width=0.55\textwidth]{Chapters/CrSubSeries.png}
    \end{center}
    \caption[Band structure of PtTe$_2$ and its Cr alloys]{Band structure of PtTe$_2$ and its Cr alloys $x \in \set{0, 0.15, 0.25, 0.35, 0.45}$. Left: Measured band structure of PtTe$_2$ and Cr$_x$Pt$_{1-x}$Te$_2$. Center: Second derivative of the measured band structure. Right: Density functional theory (DFT) calculated band structure of PtTe$_2$ and Cr$_{x}$Pt$_{1-x}$Te$_2$. The Dirac point is shown by a horizontal line overlaying the band structure.}
    \label{fig:CrSubSeries}
\end{figure} 
\begin{figure}
    \begin{center}
        \includegraphics[width=0.65\textwidth]{Chapters/EDPlot.png}
    \end{center}
    \caption{Energy of the Dirac point as a function of the Cr concentration $x$. The energy of the Dirac point is shown to be a monotonically increasing function of the Cr concentration.}
    \label{fig:DiracPointE}
\end{figure}
The Dirac point is a notable feature of the band structure of PtTe$_2$ and its Cr alloys. Therefore it is of prime use in our study of the topological Lifshitz transition. Figure \ref{fig:CrSubSeries} shows the progression of the Dirac point as we increase the Cr concentration $x$. We can see that the Dirac point shifts to higher energy levels as we increase the Cr concentration. If we plot the energy of the Dirac point as a function of the Cr concentration, we find that it is a nearly quadratic function of the Cr concentration (see Figure \ref{fig:DiracPointE}).
Our function takes the form $E_D(x) = ax^b + c$ with fit parameters $a,b$ and $c$ given in Table \ref{tab:EDFitParams}. While the values of this function are not of primary importance, it is notable that the exponent $b$ is very close to 2, possibly indicating a quadratic relationship between the Dirac point energy and the Cr concentration. What is relevant is that, with the assumption that Cr substitution has no other effect on the band structure of PtTe$_2$ besides translating it in energy, we can use this function to determine the Fermi level of PtTe$_2$ Cr alloys with respect to the baseline Fermi level of PtTe$_2$. With this mapping it is then possible to determine the critical Cr concentration $x_c$ at which a Lifshitz transition may occur.
\begin{table}
    \centering
    \begin{tabular}{|c|c|}
        \hline
        Parameter & Value \\
        \hline
        $a$ & $5.844 \pm 0.76 \text{ eV}$ \\
        $b$ & $2.203 \pm 0.17$ \\
        $c$ & $-0.99 \pm 0.02 \text{ eV}$ \\
        \hline
    \end{tabular}
    \caption{Fit parameters for the power law function $E_D(x) = ax^b + c$}
    \label{tab:EDFitParams}
\end{table}
\subsection{Data Transformation}
The main hurdle of the analysis outlined in the previous section is the transformation of our ARPES data into a form that allows for the computation of the Euler characteristic. Our ARPES data is in the form of a 4-dimensional dataset $\mathcal{D}$ (see \ref{eq:ARPESData}) that contains the momentum values $\vec{k}$ within the BZ, the binding energy values $E_B$ within the range of the minimum energy of our dataset $E_{B_{min}}$ and the Fermi level $E_B = 0$, and the intensity values $I$ within the range of $0$ and the maximum intensity of our dataset $I_{max}$. This dataset captures a measured portion of the Fermi sea $F_S$ as it contains (approximately) all points $\left[\vec{k}, E(\vec{k})\right]$ for which the binding energy is below the Fermi level. 
This data is a set of points in 4-dimensional space, and does not lend itself to the structure necessary for the computation of the Euler characteristic. Therefore, we must transform this data into a form that resembles the connected manifold needed for our theoretically motivated analysis. 
\subsubsection{Denoising and Thresholding}
We aim to transform our data of points into a smooth surface that represents the Fermi sea. The first step of our smooth surface computation is to remove experimental noise. We can do this by applying a moving average filter on $3$-dimensional slices of our dataset across all energies $E_B$. This transforms our intensity profile $I(\vec{k})\Big|_{\forall E_B}$ to a smoothed intensity profile $\tilde{I}(\vec{k})\Big|_{\forall E_B}$. The transformation can be defined:
\begin{equation}
    M_A: I(\vec{k})\Big|_{\forall E_B} \mapsto \tilde{I}(\vec{k})\Big|_{\forall E_B}
\end{equation}
where $M_A$ is the moving average filter defined in \ref{eq:MovingAverage}.
Next, to isolate the Fermi sea from our point-set $\mathcal{D}$, we make the assumption that the points below a certain intensity threshold $T$ are not part of the band structure and therefore not part of the Fermi sea. This allows us to define a set of points $\mathcal{D}' \subset \mathcal{D}$ that contains only the points of the Fermi sea. We can define the transformation from $\mathcal{D}$ to $\mathcal{D}'$ as
\begin{equation}
     T: \mathcal{D} \mapsto \mathcal{D}^\prime
\end{equation}
\begin{equation*}
    \left\{\vec{D}\in \mathbb{R}^4 \mid \vec{D} =\left[\vec{k}, E_B, I(\vec{k}, E_B)\right]\right\} \mapsto \begin{cases}
        \left\{\vec{D}\in \mathbb{R}^4 \mid \vec{D} =\left[\vec{k}, E_B, I(\vec{k}, E_B)\right]\right\} & \text{if } I(\vec{k},E_B) > T_{\text{min}}\\
        \emptyset &\text{ otherwise}
    \end{cases}
\end{equation*}
\subsubsection{Surface Computation}
Next we perform a grid interpolation on our point set $\mathcal{D}'$ to obtain a semi-continuous representation of the Fermi sea. This done through cubic spline interpolation on a grid of momentum values $\vec{k}$ and energy values $E_B$. We construct the grid in the following way for our energy values: 
\begin{equation}
    E_{B_j} = E_{B_{min}} + j\Delta E_B, \quad j = 0, 1, 2, \dots, N_E, \quad \Delta E_B = \frac{E_{B_{max}} - E_{B_{min}}}{N_E}
\end{equation}
where $E_{B_{min}}$ and $E_{B_{max}}$ are the minimum and maximum binding energy values in our dataset, and $N_E$ is the number of energy points in our grid. We construct our momentum grid in a similar way: 
\begin{equation}
    k_{x_j} = k_{x_{min}} + j\Delta k_x, \quad j = 0, 1, 2, \dots, N_{k_x}, \quad \Delta k_x = \frac{k_{x_{max}} - k_{x_{min}}}{N_{k_x}}
\end{equation}
\begin{equation}
    k_{y_j} = k_{y_{min}} + j\Delta k_y, \quad j = 0, 1, 2, \dots, N_{k_y}, \quad \Delta k_y = \frac{k_{y_{max}} - k_{y_{min}}}{N_{k_y}}
\end{equation}
With our data now placed on a grid, we can perform cubic spline interpolation to approximate intermediate values of the Fermi sea. This is done by fitting a cubic polynomial to each of the 4 nearest neighbors of a given point on the grid. This results in a representation of the Fermi sea that we can then perform differential operations on to determine its topology using Morse theory.
Between two data points when placed on our regular grid, a cubic polynomial of the following form is fit to approximate intermediate values:
\begin{equation}
    \tilde{I}(k_x,k_y, E_B) = \sum_{i=0}^3 \sum_{j=0}^3 \sum _{\ell=0}^3 \, a_{ij\ell} (k_x - k_{x_i})^i (k_y - k_{y_j})^j (E_B - E_{B_\ell})^\ell
\end{equation}
where the coefficients $a_{ij\ell}$ are determined by the nearest neighbors of the point $(k_x, k_y, E_B)$ on the grid.
Finally, we apply a binary thresholding to our interpolated intensity profile to obtain a representation of the Fermi sea as a connected manifold. This is done by defining a threshold value $T$ and setting all intensity values above the threshold to 1 and all points below the threshold to 0. We can define this transformation as
\begin{equation}
    B: I(k_x,k_y, E_B) \mapsto \begin{cases}
        1 & \text{if } I(k_x,k_y, E_B) > T_\text{bin} \\
        0 & \text{otherwise}
    \end{cases}
\end{equation}
\subsection{Euler Characteristic Calculation}
Our Morse theoretic method for computing the Euler characteristic relies on the identification of critical points of our Morse function. In our case, this is the energy dispersion $E(\vec{k})$. To find the critical points of this function, we must find the points at which the gradient of the function is zero. In our case, this is done visually as we do not have an explicit form for the energy dispersion and even with surface interpolation direct computation of the gradient is not trivial given the memory requirements of our dataset and the structure of our interpolated surface. 
\subsubsection{Critical Points}
We screen our binary intensity profile for points where $I(k_x,k_y, E_B) = 1$. If we plot cross sections of this intensity profile along the $k_x$ and $k_y$ axes, we can visually match the critical points along each axis to estimate if the gradient is $0$ at these points. These points are the critical points of our Morse function. Additionally, if a point seems to be a maxima along one direction and minima along the other, it is very likely a saddle point.
Next we may take cross sections of our binary intensity profile to allow for clear visual inspection of the critical points. This has the same effect as taking contours of the energy dispersion along each axis. These can be formally defined as
\begin{equation}
    E_B(k_x)\Big|_{k_y = k_{y_0}} = \set{E_B \mid I(k_x,k_{y_0}, E_B) = 1}
\end{equation}
\begin{equation}
    E_B(k_y)\Big|_{k_x = k_{x_0}} = \set{E_B \mid I(k_{x_0}, k_y, E_B) = 1}
\end{equation}
\begin{figure}
    \begin{center}
        \includegraphics[width=0.70\textwidth]{Chapters/CriticalPointsEkx-2.png}
    \end{center}
    \caption[$E-k_x$ Cross section slices of the Fermi sea at different $k_y$ values.]{$E-k_x$ Cross section slices of the Fermi sea at different $k_y$ values. Individual surface slices are plotted in different colors. The critical points are circled in unique colors to match them with the critical points along the $k_y$ axis.}
    \label{fig:CriticalPoints}
\end{figure}
These cross sections are not necessarily functions and are certainly multivalued, although they allow us to inspect points that may be critical points of our Morse function. Figure \ref{fig:CriticalPoints}
\begin{figure}
    \begin{center}
        \begin{minipage}{0.49\textwidth}
            \includegraphics[width=\textwidth]{Chapters/CriticalPointsEkyNoSpike.png}
        \end{minipage}
        \hfill
        \begin{minipage}{0.49\textwidth}
            \includegraphics[width=\textwidth]{Chapters/CriticalPointsEkySpike.png}
        \end{minipage}
        
    \end{center}
    \caption[$E-k_y$ Cross section slices of the PtTe$_2$ Fermi sea at different $k_x$ values (No Spike-like feature)]{$E-k_y$ Cross section slices of the PtTe$_2$ Fermi sea at different $k_x$ values. Individual surface slices are plotted in different colors. The critical points are circled in unique colors to match them with the critical points along the $k_x$ axis.}
    \label{fig:CriticalPointsEky}
\end{figure}
highlights the critical points of our extracted PtTe$_2$ Fermi sea along the $k_x$ axis. The critical points are circled in unique colors to match them with the following Figure \ref{fig:CriticalPointsEky} , which marks the same critical points along the $k_y$ axis. Two different sets of cross sections are displayed for the $E-k_y$ axis as the Fermi sea has a spike-like feature that is obscured by the outer bands points when viewed along the $k_x$ axis. The color circles are meant to roughly match the critical points along the $k_x$ axis to their counterparts along the $k_y$ axis. 
We only aim to consider the energy range $E_B \in [-1, 0]$ since the relative position of the band structure with respect to the Fermi level as a function of Cr concentration, only shifts by at most $1$ eV. This means in the context of study the topological transition caused by critical points crossing the Fermi level, our analysis can be limited to this region.
By our visual inspection, we can identify $4$ critical points in the Fermi sea. We can label them as $\reddot, \bluedot, \greendot, \orangedot$ which correspond to the circles in Figures \ref{fig:CriticalPoints} and \ref{fig:CriticalPointsEky}. We may now proced by indexing these critical points.
\subsubsection{Indexing}
We recall from Morse Theory that the index of a critical point is the number of negative eigenvalues of the Hessian matrix of second derivatives of the Morse function at that critical point. In our case, the Morse function is the energy dispersion $E(\vec{k})$ and the Hessian matrix is given by
\begin{equation}
    H_{ij} = \frac{\partial^2 E}{\partial k_i \partial k_j}
\end{equation}
For similar reasons as before, we are unable to compute the Hessian matrix directly from our data. However we can identify the index of a critical point by visual inspection of the cross sections of the Fermi sea. If a point is a local minimum along both the $k_x$ and $k_y$ axes, it is a local minimum and has index 0. If a point is a local maximum along both the $k_x$ and $k_y$ axes, it is a local maximum and has index 2. If a point is a local minimum along one axis and a local maximum along the other, it is a saddle point and has index 1.
Let us define two maps $C_x$ and $C_y$ that act on our critical points
\begin{equation}
    C_x: \mathbb{R}^2 \mapsto \set{-1,1}, \quad C_x(c) = \begin{cases}
        1 & \text{if } c \text{ is a local minimum along the } k_x \text{ axis} \\
        -1 & \text{if } c \text{ is a local maximum along the } k_x \text{ axis}
    \end{cases}
\end{equation}
\begin{equation}
    C_y: \mathbb{R}^2 \mapsto \set{-1,1}, \quad C_y(c) = \begin{cases}
        1 & \text{if } c \text{ is a local minimum along the } k_y \text{ axis} \\
        -1 & \text{if } c \text{ is a local maximum along the } k_y \text{ axis}
        \end{cases}
\end{equation}
The index of a critical point $c$ is then defined as
\begin{equation}
    \text{index}(c) = 1 -\frac{1}{2}\left(C_x(c) + C_y(c)\right)
\end{equation}
Therefore referencing back to Figures \ref{fig:CriticalPoints} and \ref{fig:CriticalPointsEky}, we can determine the index of each critical point. The critical points are indexed as follows:
\begin{equation}
    \text{index}(\reddot) = 0, \quad \text{index}(\bluedot) = 0
    \end{equation}
    \begin{equation*}
    \text{index}(\greendot) = 0, \quad \text{index}(\orangedot) = 2
\end{equation*}
\subsubsection{Relative Euler Characteristic}
We utilize Morse's Theorem to compute the Euler characteristic of our Fermi sea for the case of PtTe$_2$. Since we are constraining our analysis to the energy range $E_B \in [-1, 0]$, we don't consider this Euler characteristic to be the absolute Euler characteristic of the Fermi sea, but rather a relative Euler characteristic with respect to the energy range we are considering. We have identified 4 critical points with indices 0, 0, 0, and 2. Therefore, the Euler characteristic is given by
\begin{equation}
    \chi_{\text{PtTe$_2$}} = \left(-1\right)^{0} + \left(-1\right)^{0} + \left(-1\right)^{0} + \left(-1\right)^{2} = 4
\end{equation}
Now as we have outlined previously, the presence of a Lifshitz transition is characterized by a change in the Euler characteristic of the Fermi sea. Therefore, to determine if a Lifshitz transition occurs in PtTe$_2$ and its Cr alloys, we must compute the Euler characteristic for each Cr concentration. We can rewrite our Euler characteristic calculation as a function of the Fermi level $E_F$ relatively to the Fermi level of PtTe$_2$. We can utilize the power law fit from Eq. \ref{tab:EDFitParams}
\begin{equation}
    E_D(x) = 5.844x^{2.203} - 1 \Rightarrow E_F(x) = -1 - E_D(x) = -5.844x^{2.203}
    \label{eq:EFx}
\end{equation}
Our Euler characteristic computation is now purely dependant one which critical points lie below our Fermi level
\begin{equation}
    \chi(E_F) = \sum_{c \in \set{\reddot, \bluedot, \greendot, \orangedot}} (-1)^{\text{index}(c)} \cdot \delta_{E(c) \leq E_F}
\end{equation}
where $\delta_{E(c) \leq E_F}$ is the Kronecker delta function which is 1 if the critical point $c$ lies below the Fermi level and 0 otherwise.
Referencing Figure \ref{fig:CriticalPoints} we can see that the highest critical point $\orangedot$ has an energy of approximately $-0.75$ eV. Therefore, we can expect the relative Euler characteristic to be 4 for all Cr concentrations below the critical concentration $x_{c_{\orangedot}}$ where $E_F(x_{c_{\orangedot}}) = -0.75$ eV. The next two critical points $\reddot$ and $\bluedot$ are at the same energy level of approximately $-0.8$ eV. Therefore, we can expect the relative Euler characteristic to be 3 for all Cr concentrations between $x_{c_{\orangedot}}$ and $x_{c_{\reddot,\bluedot}}$ where $E_F(x_{c_{\reddot,\bluedot}}) = -0.8$ eV. The lowest critical point $\greendot$ has an energy only slightly below those of $\reddot$ and $\bluedot$ at approximately $-0.85$ eV. Therefore, we can expect the relative Euler characteristic to be 1 for all Cr concentrations between $x_{c_{\reddot,\bluedot}}$ and $x_{c_{\greendot}}$ where $E_F(x_{c_{\greendot}}) = -0.85$ eV. For all Cr concentrations above $x_{c_{\greendot}}$, none of the critical points will lie below the Fermi level and therefore the relative Euler characteristic will be 0. These observations are explicitly summarized in Table \ref{tab:EulerTransitions}.
\begin{table}

    \centering
    \caption{Changes in Euler characteristic $\chi$ as the Fermi level $E_F$ (relative to PtTe$_2$) crosses critical point energies.}
    \begin{tabular}{|c|c|c|c|}
        \hline
        \textbf{Critical Point(s)} & \textbf{$E_F$ (eV)} & $\Delta\chi$ & $\chi$ \\
        \hline
        $\emptyset$ & $> -0.75$ & $0$ & $4$ \\
        $\orangedot$ & $-0.75$ & $-1$ & $3$ \\
        $\reddot,\,\bluedot$ & $-0.80$ & $-2$ & $1$ \\
        $\greendot$ & $-0.85$ & $-1$ & $0$ \\
        \hline
    \end{tabular}
    \label{tab:EulerTransitions}
\end{table}

\subsection{Critical Concentrations}
We can now use Eq. \ref{eq:EFx} to determine the critical Cr concentration ratios $x_{c_{\orangedot}}, x_{c_{\reddot,\bluedot}}, x_{c_{\greendot}}$ where the relative Euler characteristic changes. First the equation must be inverted to solve for $x$ in terms of $E_F$
\begin{equation}
    x = \left(-\frac{E_F}{5.844}\right)^{1/2.203} \text{ where } E_F \leq 0
\end{equation}

Then we can determine the critical concentrations $x_{c_{\orangedot}}, x_{c_{\reddot,\bluedot}}, x_{c_{\greendot}}$ as
\begin{equation}
    x_{c_{\orangedot}} = \left(\frac{0.75}{5.844}\right)^{1/2.203} \approx 0.394, \quad x_{c_{\reddot,\bluedot}} = \left(\frac{0.8}{5.844}\right)^{1/2.203} = 0.405,
\end{equation}
\begin{equation*}
    x_{c_{\greendot}} = \left(\frac{0.85}{5.844}\right)^{1/2.203} = 0.417
\end{equation*}
\begin{table}
    \centering
    \caption{Association between critical points, critical Cr concentration ratios \(x_c\), and Fermi energies \(E_F\) where Lifshitz transitions occur.}
    \begin{tabular}{|c|c|c|}
        \hline
        \textbf{Critical Point(s)} & \(x_c\) & \textbf{\(E_F\) (eV)} \\
        \hline
        \(\orangedot\) & \(0.394\) & \(-0.75\) \\
        \(\reddot,\, \bluedot\) & \(0.405\) & \(-0.80\) \\
        \(\greendot\) & \(0.417\) & \(-0.85\) \\
        \hline
    \end{tabular}
    \label{tab:CriticalConcentrationReversed}
\end{table}
This is summarized in Table \ref{tab:CriticalConcentrationReversed}.
We have shown that the topological transition occurs in the Cr concentration range of $x \in [0.394, 0.417]$, and it is possible that multiple transitions occur within this range and exotic topological phases may exist in small concentration ranges within this interval. 


