#!/bin/bash

# Full LaTeX Build Script for Thesis
# This script performs a complete build with bibliography processing

set -e  # Exit on any error

echo "=== Starting Full LaTeX Build ==="
echo "Document: main.tex"
echo "Build time: $(date)"
echo

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check required commands
echo "Checking required tools..."
if ! command_exists xelatex; then
    echo "Error: xelatex not found. Please install XeLaTeX."
    exit 1
fi

if ! command_exists bibtex; then
    echo "Error: bibtex not found. Please install BibTeX."
    exit 1
fi

echo "✓ All required tools found"
echo

# Clean previous build artifacts (optional)
if [ "$1" = "clean" ]; then
    echo "Cleaning previous build artifacts..."
    rm -f *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot *.synctex.gz *.fdb_latexmk *.fls *.run.xml *.bcf *.nav *.snm *.vrb
    echo "✓ Clean completed"
    echo
fi

# Build process
echo "=== Build Process ==="

echo "Step 1/4: First XeLaTeX pass (generating aux files)..."
xelatex -shell-escape -synctex=1 -interaction=nonstopmode -file-line-error main.tex
echo "✓ First pass completed"

echo "Step 2/4: Processing bibliography with BibTeX..."
if [ -f "main.aux" ]; then
    bibtex main
    echo "✓ Bibliography processed"
else
    echo "Warning: main.aux not found, skipping BibTeX"
fi

echo "Step 3/4: Second XeLaTeX pass (incorporating bibliography)..."
xelatex -shell-escape -synctex=1 -interaction=nonstopmode -file-line-error main.tex
echo "✓ Second pass completed"

echo "Step 4/4: Final XeLaTeX pass (resolving all references)..."
xelatex -shell-escape -synctex=1 -interaction=nonstopmode -file-line-error main.tex
echo "✓ Final pass completed"

echo
echo "=== Build Summary ==="
if [ -f "main.pdf" ]; then
    echo "✓ Build successful!"
    echo "Output: main.pdf"
    echo "Pages: $(pdfinfo main.pdf 2>/dev/null | grep Pages | awk '{print $2}' || echo 'Unknown')"
    echo "Size: $(ls -lh main.pdf | awk '{print $5}')"
else
    echo "✗ Build failed - main.pdf not generated"
    exit 1
fi

echo "Build completed at: $(date)"
echo

# Check for common warnings
echo "=== Checking for Issues ==="
if [ -f "main.log" ]; then
    warnings=$(grep -c "Warning" main.log || echo "0")
    errors=$(grep -c "Error" main.log || echo "0")
    
    echo "Warnings found: $warnings"
    echo "Errors found: $errors"
    
    if [ "$errors" -gt "0" ]; then
        echo
        echo "Recent errors from log:"
        grep -A 2 "Error" main.log | tail -10
    fi
    
    if [ "$warnings" -gt "0" ]; then
        echo
        echo "Recent warnings from log:"
        grep "Warning" main.log | tail -5
    fi
else
    echo "No log file found"
fi

echo
echo "=== Build Complete ==="
