# LaTeX Makefile configuration for XeLaTeX with BibTeX backend
# This ensures proper compilation sequence for biblatex with backend=bibtex

# Use XeLaTeX as the main compiler
$pdf_mode = 5;  # 5 = xelatex

# Enable shell escape for minted package and other options
$xelatex = 'xelatex -shell-escape -synctex=1 -interaction=nonstopmode -file-line-error %O %S';

# Use BibTeX (not Biber) since you're using backend=bibtex
$bibtex_use = 2;  # 2 = use bibtex when .bbl files are out of date

# Force bibliography processing
$bibtex = 'bibtex %O %S';

# Ensure proper compilation sequence for bibliography
# This forces multiple passes to resolve all references
$max_repeat = 6;  # Increased to ensure all references are resolved

# Always run at least 3 times to ensure proper cross-references
$min_repeat = 3;

# Force regeneration of auxiliary files when source changes
$force_mode = 1;

# Clean up auxiliary files (expanded list)
$clean_ext = "aux bbl blg fdb_latexmk fls log out run.xml synctex.gz toc lof lot nav snm vrb figlist makefile fls_latexmk";

# Show used CPU time and be verbose about what's happening
$show_time = 1;
$silent = 0;  # Show compilation output

# Ensure bibliography is processed when citations.bib changes
add_cus_dep('bib', 'bbl', 0, 'run_bibtex');

sub run_bibtex {
    my $base = shift @_;
    system("bibtex \"$base\"");
    return $?;
}

# Auto-detect and handle file dependencies
$recorder = 1;

# Preview mode settings (useful for continuous compilation)
$preview_continuous_mode = 1;
$pdf_previewer = 'start';

# Ensure proper handling of included files
$dependents_list = 1;
